using MqttBroker.Metrics.Models;

namespace MqttBroker.Metrics.Collectors;

/// <summary>
/// 性能指标收集器接口
/// </summary>
public interface IMetricsCollector
{
    /// <summary>
    /// 收集当前性能指标
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>性能指标数据</returns>
    Task<PerformanceMetrics> CollectMetricsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 开始指标收集
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止指标收集
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 指标收集事件
    /// </summary>
    event EventHandler<MetricsCollectedEventArgs>? MetricsCollected;
}

/// <summary>
/// 连接指标收集器接口
/// </summary>
public interface IConnectionMetricsCollector
{
    /// <summary>
    /// 收集连接指标
    /// </summary>
    /// <returns>连接指标</returns>
    ConnectionMetrics CollectConnectionMetrics();

    /// <summary>
    /// 记录新连接
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    void RecordConnection(string clientId);

    /// <summary>
    /// 记录连接断开
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    /// <param name="duration">连接持续时间</param>
    void RecordDisconnection(string clientId, TimeSpan duration);

    /// <summary>
    /// 记录认证失败
    /// </summary>
    void RecordAuthenticationFailure();

    /// <summary>
    /// 记录连接超时
    /// </summary>
    void RecordConnectionTimeout();
}

/// <summary>
/// 消息指标收集器接口
/// </summary>
public interface IMessageMetricsCollector
{
    /// <summary>
    /// 收集消息指标
    /// </summary>
    /// <returns>消息指标</returns>
    MessageMetrics CollectMessageMetrics();

    /// <summary>
    /// 记录消息发送
    /// </summary>
    /// <param name="messageSize">消息大小</param>
    /// <param name="routingLatency">路由延迟</param>
    void RecordMessageSent(int messageSize, TimeSpan routingLatency);

    /// <summary>
    /// 记录消息接收
    /// </summary>
    /// <param name="messageSize">消息大小</param>
    void RecordMessageReceived(int messageSize);

    /// <summary>
    /// 记录离线消息
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    void RecordOfflineMessage(string clientId);

    /// <summary>
    /// 记录死信消息
    /// </summary>
    /// <param name="reason">失败原因</param>
    void RecordDeadLetterMessage(string reason);

    /// <summary>
    /// 记录保留消息
    /// </summary>
    /// <param name="topic">主题</param>
    void RecordRetainedMessage(string topic);
}

/// <summary>
/// 订阅指标收集器接口
/// </summary>
public interface ISubscriptionMetricsCollector
{
    /// <summary>
    /// 收集订阅指标
    /// </summary>
    /// <returns>订阅指标</returns>
    SubscriptionMetrics CollectSubscriptionMetrics();

    /// <summary>
    /// 记录订阅
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    /// <param name="topicFilter">主题过滤器</param>
    /// <param name="isWildcard">是否为通配符订阅</param>
    void RecordSubscription(string clientId, string topicFilter, bool isWildcard);

    /// <summary>
    /// 记录取消订阅
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    /// <param name="topicFilter">主题过滤器</param>
    void RecordUnsubscription(string clientId, string topicFilter);

    /// <summary>
    /// 记录主题匹配延迟
    /// </summary>
    /// <param name="latency">匹配延迟</param>
    void RecordTopicMatchingLatency(TimeSpan latency);

    /// <summary>
    /// 记录活跃主题
    /// </summary>
    /// <param name="topic">主题</param>
    void RecordActiveTopic(string topic);
}

/// <summary>
/// QoS 指标收集器接口
/// </summary>
public interface IQoSMetricsCollector
{
    /// <summary>
    /// 收集 QoS 指标
    /// </summary>
    /// <returns>QoS 指标</returns>
    QoSMetrics CollectQoSMetrics();

    /// <summary>
    /// 记录 QoS 消息处理
    /// </summary>
    /// <param name="qosLevel">QoS 级别</param>
    /// <param name="processingLatency">处理延迟</param>
    /// <param name="success">是否成功</param>
    void RecordQoSProcessing(int qosLevel, TimeSpan processingLatency, bool success);

    /// <summary>
    /// 记录待确认消息
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    /// <param name="messageId">消息 ID</param>
    void RecordPendingAcknowledgment(string clientId, ushort messageId);

    /// <summary>
    /// 记录消息确认
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    /// <param name="messageId">消息 ID</param>
    void RecordMessageAcknowledged(string clientId, ushort messageId);

    /// <summary>
    /// 记录消息重传
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    /// <param name="messageId">消息 ID</param>
    void RecordMessageRetransmission(string clientId, ushort messageId);
}

/// <summary>
/// 系统指标收集器接口
/// </summary>
public interface ISystemMetricsCollector
{
    /// <summary>
    /// 收集系统指标
    /// </summary>
    /// <returns>系统指标</returns>
    SystemMetrics CollectSystemMetrics();
}

/// <summary>
/// 网络指标收集器接口
/// </summary>
public interface INetworkMetricsCollector
{
    /// <summary>
    /// 收集网络指标
    /// </summary>
    /// <returns>网络指标</returns>
    NetworkMetrics CollectNetworkMetrics();

    /// <summary>
    /// 记录网络发送
    /// </summary>
    /// <param name="bytes">发送字节数</param>
    void RecordBytesSent(long bytes);

    /// <summary>
    /// 记录网络接收
    /// </summary>
    /// <param name="bytes">接收字节数</param>
    void RecordBytesReceived(long bytes);

    /// <summary>
    /// 记录网络错误
    /// </summary>
    /// <param name="errorType">错误类型</param>
    void RecordNetworkError(string errorType);

    /// <summary>
    /// 记录连接重置
    /// </summary>
    void RecordConnectionReset();
}

/// <summary>
/// 指标收集事件参数
/// </summary>
public class MetricsCollectedEventArgs : EventArgs
{
    /// <summary>
    /// 性能指标数据
    /// </summary>
    public PerformanceMetrics Metrics { get; }

    /// <summary>
    /// 收集时间
    /// </summary>
    public DateTime CollectedAt { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="metrics">性能指标数据</param>
    public MetricsCollectedEventArgs(PerformanceMetrics metrics)
    {
        Metrics = metrics;
        CollectedAt = DateTime.UtcNow;
    }
}
