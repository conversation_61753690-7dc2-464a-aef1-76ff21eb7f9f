using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using MqttBroker.Core.Client;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Topic;
using Xunit;

namespace MqttBroker.Tests.Unit.Topic;

/// <summary>
/// 主题订阅管理器单元测试
/// </summary>
public class TopicSubscriptionManagerTests
{
    private readonly Mock<IMqttTopicMatcher> _mockTopicMatcher;
    private readonly Mock<ILogger<TopicSubscriptionManager>> _mockLogger;
    private readonly TopicSubscriptionOptions _options;
    private readonly TopicSubscriptionManager _subscriptionManager;

    public TopicSubscriptionManagerTests()
    {
        _mockTopicMatcher = new Mock<IMqttTopicMatcher>();
        _mockLogger = new Mock<ILogger<TopicSubscriptionManager>>();
        _options = new TopicSubscriptionOptions
        {
            MaxSubscriptionsPerClient = 100,
            MaxQoSLevel = MqttQoSLevel.ExactlyOnce,
            AllowWildcardSubscriptions = true,
            AllowSystemTopicSubscriptions = true
        };

        var optionsMock = new Mock<IOptions<TopicSubscriptionOptions>>();
        optionsMock.Setup(x => x.Value).Returns(_options);

        _subscriptionManager = new TopicSubscriptionManager(
            _mockTopicMatcher.Object,
            _mockLogger.Object,
            optionsMock.Object);
    }

    [Fact]
    public async Task SubscribeAsync_ValidSubscription_ShouldReturnSuccess()
    {
        // Arrange
        var mockClient = CreateMockClient("test-client");
        var subscription = new MqttSubscription
        {
            TopicFilter = "test/topic",
            QoSLevel = MqttQoSLevel.AtLeastOnce
        };

        _mockTopicMatcher.Setup(x => x.IsValidTopicFilter(subscription.TopicFilter))
            .Returns(true);

        // Act
        var result = await _subscriptionManager.SubscribeAsync(mockClient, subscription);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(subscription.TopicFilter, result.TopicFilter);
        Assert.Equal(MqttQoSLevel.AtLeastOnce, result.GrantedQoSLevel);
        Assert.Equal(MqttReasonCode.GrantedQoS1, result.ReasonCode);
    }

    [Fact]
    public async Task SubscribeAsync_InvalidTopicFilter_ShouldReturnFailure()
    {
        // Arrange
        var mockClient = CreateMockClient("test-client");
        var subscription = new MqttSubscription
        {
            TopicFilter = "invalid/topic/filter",
            QoSLevel = MqttQoSLevel.AtLeastOnce
        };

        _mockTopicMatcher.Setup(x => x.IsValidTopicFilter(subscription.TopicFilter))
            .Returns(false);

        // Act
        var result = await _subscriptionManager.SubscribeAsync(mockClient, subscription);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(MqttReasonCode.TopicFilterInvalid, result.ReasonCode);
    }

    [Fact]
    public async Task UnsubscribeAsync_ExistingSubscription_ShouldReturnSuccess()
    {
        // Arrange
        var mockClient = CreateMockClient("test-client");
        var subscription = new MqttSubscription
        {
            TopicFilter = "test/topic",
            QoSLevel = MqttQoSLevel.AtLeastOnce
        };

        _mockTopicMatcher.Setup(x => x.IsValidTopicFilter(subscription.TopicFilter))
            .Returns(true);

        // 先订阅
        await _subscriptionManager.SubscribeAsync(mockClient, subscription);

        // Act
        var result = await _subscriptionManager.UnsubscribeAsync(mockClient, subscription.TopicFilter);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(subscription.TopicFilter, result.TopicFilter);
        Assert.Equal(MqttReasonCode.Success, result.ReasonCode);
    }

    [Fact]
    public async Task UnsubscribeAsync_NonExistentSubscription_ShouldReturnFailure()
    {
        // Arrange
        var mockClient = CreateMockClient("test-client");
        var topicFilter = "non/existent/topic";

        // Act
        var result = await _subscriptionManager.UnsubscribeAsync(mockClient, topicFilter);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(MqttReasonCode.NoSubscriptionExisted, result.ReasonCode);
    }

    [Fact]
    public async Task GetSubscribersAsync_WithMatchingSubscriptions_ShouldReturnSubscribers()
    {
        // Arrange
        var mockClient1 = CreateMockClient("client1");
        var mockClient2 = CreateMockClient("client2");
        
        var subscription1 = new MqttSubscription
        {
            TopicFilter = "sensors/temperature",
            QoSLevel = MqttQoSLevel.AtLeastOnce
        };
        
        var subscription2 = new MqttSubscription
        {
            TopicFilter = "sensors/+",
            QoSLevel = MqttQoSLevel.AtMostOnce
        };

        _mockTopicMatcher.Setup(x => x.IsValidTopicFilter(It.IsAny<string>()))
            .Returns(true);

        // 添加订阅
        await _subscriptionManager.SubscribeAsync(mockClient1, subscription1);
        await _subscriptionManager.SubscribeAsync(mockClient2, subscription2);

        // Act
        var subscribers = await _subscriptionManager.GetSubscribersAsync("sensors/temperature");

        // Assert
        Assert.NotEmpty(subscribers);
        // 注意：实际的匹配逻辑在 TopicTree 中实现，这里只是验证接口调用
    }

    [Fact]
    public async Task GetClientSubscriptionsAsync_WithExistingSubscriptions_ShouldReturnSubscriptions()
    {
        // Arrange
        var mockClient = CreateMockClient("test-client");
        var subscription = new MqttSubscription
        {
            TopicFilter = "test/topic",
            QoSLevel = MqttQoSLevel.AtLeastOnce
        };

        _mockTopicMatcher.Setup(x => x.IsValidTopicFilter(subscription.TopicFilter))
            .Returns(true);

        await _subscriptionManager.SubscribeAsync(mockClient, subscription);

        // Act
        var subscriptions = await _subscriptionManager.GetClientSubscriptionsAsync("test-client");

        // Assert
        Assert.Single(subscriptions);
        Assert.Equal(subscription.TopicFilter, subscriptions.First().TopicFilter);
        Assert.Equal(subscription.QoSLevel, subscriptions.First().QoSLevel);
    }

    [Fact]
    public async Task CleanupClientSubscriptionsAsync_ShouldRemoveAllClientSubscriptions()
    {
        // Arrange
        var mockClient = CreateMockClient("test-client");
        var subscription1 = new MqttSubscription
        {
            TopicFilter = "test/topic1",
            QoSLevel = MqttQoSLevel.AtLeastOnce
        };
        var subscription2 = new MqttSubscription
        {
            TopicFilter = "test/topic2",
            QoSLevel = MqttQoSLevel.AtMostOnce
        };

        _mockTopicMatcher.Setup(x => x.IsValidTopicFilter(It.IsAny<string>()))
            .Returns(true);

        await _subscriptionManager.SubscribeAsync(mockClient, subscription1);
        await _subscriptionManager.SubscribeAsync(mockClient, subscription2);

        // Act
        await _subscriptionManager.CleanupClientSubscriptionsAsync("test-client");

        // Assert
        var subscriptions = await _subscriptionManager.GetClientSubscriptionsAsync("test-client");
        Assert.Empty(subscriptions);
    }

    [Fact]
    public async Task GetStatisticsAsync_ShouldReturnValidStatistics()
    {
        // Arrange
        var mockClient = CreateMockClient("test-client");
        var subscription = new MqttSubscription
        {
            TopicFilter = "test/+",
            QoSLevel = MqttQoSLevel.AtLeastOnce
        };

        _mockTopicMatcher.Setup(x => x.IsValidTopicFilter(subscription.TopicFilter))
            .Returns(true);

        await _subscriptionManager.SubscribeAsync(mockClient, subscription);

        // Act
        var statistics = await _subscriptionManager.GetStatisticsAsync();

        // Assert
        Assert.True(statistics.TotalSubscriptions >= 1);
        Assert.True(statistics.ActiveClients >= 1);
        Assert.True(statistics.WildcardSubscriptions >= 1);
    }

    private static IMqttClient CreateMockClient(string clientId)
    {
        var mockClient = new Mock<IMqttClient>();
        mockClient.Setup(x => x.ClientId).Returns(clientId);
        mockClient.Setup(x => x.IsAuthenticated).Returns(true);
        mockClient.Setup(x => x.State).Returns(MqttClientState.Authenticated);
        return mockClient.Object;
    }
}
