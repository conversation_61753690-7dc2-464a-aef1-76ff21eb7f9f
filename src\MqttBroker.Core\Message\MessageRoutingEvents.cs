using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;

namespace MqttBroker.Core.Message;

/// <summary>
/// 消息路由成功事件参数
/// </summary>
public class MessageRoutedEventArgs : EventArgs
{
    /// <summary>
    /// 路由结果
    /// </summary>
    public MessageRoutingResult Result { get; }

    /// <summary>
    /// 发布数据包
    /// </summary>
    public MqttPublishPacket PublishPacket { get; }

    /// <summary>
    /// 发布者客户端ID
    /// </summary>
    public string? PublisherClientId { get; }

    /// <summary>
    /// 初始化消息路由成功事件参数
    /// </summary>
    /// <param name="result">路由结果</param>
    /// <param name="publishPacket">发布数据包</param>
    /// <param name="publisherClientId">发布者客户端ID</param>
    public MessageRoutedEventArgs(MessageRoutingResult result, MqttPublishPacket publishPacket, string? publisherClientId = null)
    {
        Result = result ?? throw new ArgumentNullException(nameof(result));
        PublishPacket = publishPacket ?? throw new ArgumentNullException(nameof(publishPacket));
        PublisherClientId = publisherClientId;
    }
}

/// <summary>
/// 消息路由失败事件参数
/// </summary>
public class MessageRoutingFailedEventArgs : EventArgs
{
    /// <summary>
    /// 主题名称
    /// </summary>
    public string TopicName { get; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; }

    /// <summary>
    /// 异常信息
    /// </summary>
    public Exception? Exception { get; }

    /// <summary>
    /// 发布数据包
    /// </summary>
    public MqttPublishPacket PublishPacket { get; }

    /// <summary>
    /// 发布者客户端ID
    /// </summary>
    public string? PublisherClientId { get; }

    /// <summary>
    /// 初始化消息路由失败事件参数
    /// </summary>
    /// <param name="topicName">主题名称</param>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="publishPacket">发布数据包</param>
    /// <param name="publisherClientId">发布者客户端ID</param>
    /// <param name="exception">异常信息</param>
    public MessageRoutingFailedEventArgs(string topicName, string errorMessage, MqttPublishPacket publishPacket, string? publisherClientId = null, Exception? exception = null)
    {
        TopicName = topicName ?? throw new ArgumentNullException(nameof(topicName));
        ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));
        PublishPacket = publishPacket ?? throw new ArgumentNullException(nameof(publishPacket));
        PublisherClientId = publisherClientId;
        Exception = exception;
    }
}

/// <summary>
/// 离线消息存储事件参数
/// </summary>
public class OfflineMessageStoredEventArgs : EventArgs
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; }

    /// <summary>
    /// 主题名称
    /// </summary>
    public string TopicName { get; }

    /// <summary>
    /// 消息ID
    /// </summary>
    public string MessageId { get; }

    /// <summary>
    /// QoS级别
    /// </summary>
    public MqttQoSLevel QoSLevel { get; }

    /// <summary>
    /// 消息大小（字节）
    /// </summary>
    public int MessageSize { get; }

    /// <summary>
    /// 存储时间
    /// </summary>
    public DateTime StoredAt { get; }

    /// <summary>
    /// 初始化离线消息存储事件参数
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="topicName">主题名称</param>
    /// <param name="messageId">消息ID</param>
    /// <param name="qosLevel">QoS级别</param>
    /// <param name="messageSize">消息大小</param>
    /// <param name="storedAt">存储时间</param>
    public OfflineMessageStoredEventArgs(string clientId, string topicName, string messageId, MqttQoSLevel qosLevel, int messageSize, DateTime storedAt)
    {
        ClientId = clientId ?? throw new ArgumentNullException(nameof(clientId));
        TopicName = topicName ?? throw new ArgumentNullException(nameof(topicName));
        MessageId = messageId ?? throw new ArgumentNullException(nameof(messageId));
        QoSLevel = qosLevel;
        MessageSize = messageSize;
        StoredAt = storedAt;
    }
}
