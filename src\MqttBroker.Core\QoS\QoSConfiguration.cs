using MqttBroker.Core.Protocol;

namespace MqttBroker.Core.QoS;

/// <summary>
/// QoS 管理器配置选项
/// </summary>
public class QoSManagerOptions
{
    /// <summary>
    /// 是否启用 QoS 处理
    /// </summary>
    public bool EnableQoSProcessing { get; set; } = true;

    /// <summary>
    /// 支持的最大 QoS 级别
    /// </summary>
    public MqttQoSLevel MaxSupportedQoSLevel { get; set; } = MqttQoSLevel.ExactlyOnce;

    /// <summary>
    /// 是否启用统计信息收集
    /// </summary>
    public bool EnableStatistics { get; set; } = true;

    /// <summary>
    /// 统计信息更新间隔（毫秒）
    /// </summary>
    public int StatisticsUpdateIntervalMs { get; set; } = 5000;

    /// <summary>
    /// 是否启用性能监控
    /// </summary>
    public bool EnablePerformanceMonitoring { get; set; } = true;

    /// <summary>
    /// 最大并发 QoS 处理数量
    /// </summary>
    public int MaxConcurrentQoSProcessing { get; set; } = 10000;

    /// <summary>
    /// QoS 处理超时时间（毫秒）
    /// </summary>
    public int QoSProcessingTimeoutMs { get; set; } = 30000;
}

/// <summary>
/// 消息确认服务配置选项
/// </summary>
public class MessageAcknowledgmentOptions
{
    /// <summary>
    /// 每个客户端的最大待确认消息数量
    /// </summary>
    public int MaxPendingMessagesPerClient { get; set; } = 1000;

    /// <summary>
    /// 消息确认超时时间（毫秒）
    /// </summary>
    public int AcknowledgmentTimeoutMs { get; set; } = 60000;

    /// <summary>
    /// 是否启用消息确认统计
    /// </summary>
    public bool EnableAcknowledgmentStatistics { get; set; } = true;

    /// <summary>
    /// 统计信息清理间隔（毫秒）
    /// </summary>
    public int StatisticsCleanupIntervalMs { get; set; } = 300000; // 5分钟

    /// <summary>
    /// 是否在客户端断开连接时自动清理待确认消息
    /// </summary>
    public bool AutoCleanupOnClientDisconnect { get; set; } = true;

    /// <summary>
    /// QoS 2 消息的最大等待时间（毫秒）
    /// </summary>
    public int QoS2MaxWaitTimeMs { get; set; } = 120000; // 2分钟
}

/// <summary>
/// 消息重传服务配置选项
/// </summary>
public class MessageRetransmissionOptions
{
    /// <summary>
    /// 是否启用消息重传
    /// </summary>
    public bool EnableRetransmission { get; set; } = true;

    /// <summary>
    /// 重传检查间隔（毫秒）
    /// </summary>
    public int RetransmissionCheckIntervalMs { get; set; } = 30000; // 30秒

    /// <summary>
    /// 消息重传超时时间（毫秒）
    /// </summary>
    public int RetransmissionTimeoutMs { get; set; } = 60000; // 1分钟

    /// <summary>
    /// 最大重传次数
    /// </summary>
    public int MaxRetransmissionAttempts { get; set; } = 3;

    /// <summary>
    /// 重传间隔递增因子
    /// </summary>
    public double RetransmissionBackoffFactor { get; set; } = 1.5;

    /// <summary>
    /// 最大重传间隔（毫秒）
    /// </summary>
    public int MaxRetransmissionIntervalMs { get; set; } = 300000; // 5分钟

    /// <summary>
    /// 是否启用指数退避
    /// </summary>
    public bool EnableExponentialBackoff { get; set; } = true;

    /// <summary>
    /// 是否在客户端重连时立即重传
    /// </summary>
    public bool RetransmitOnClientReconnect { get; set; } = true;

    /// <summary>
    /// 重传失败后是否移动到死信队列
    /// </summary>
    public bool MoveToDeadLetterQueueOnFailure { get; set; } = true;

    /// <summary>
    /// 最大并发重传数量
    /// </summary>
    public int MaxConcurrentRetransmissions { get; set; } = 100;
}

/// <summary>
/// 消息去重服务配置选项
/// </summary>
public class MessageDeduplicationOptions
{
    /// <summary>
    /// 是否启用消息去重
    /// </summary>
    public bool EnableDeduplication { get; set; } = true;

    /// <summary>
    /// 已处理消息记录的默认过期时间（毫秒）
    /// </summary>
    public int DefaultRecordExpirationMs { get; set; } = 3600000; // 1小时

    /// <summary>
    /// 过期记录清理间隔（毫秒）
    /// </summary>
    public int ExpiredRecordCleanupIntervalMs { get; set; } = 600000; // 10分钟

    /// <summary>
    /// 每个客户端的最大已处理消息记录数量
    /// </summary>
    public int MaxProcessedRecordsPerClient { get; set; } = 10000;

    /// <summary>
    /// 是否使用消息内容哈希进行去重
    /// </summary>
    public bool UseContentHashForDeduplication { get; set; } = true;

    /// <summary>
    /// 是否在客户端断开连接时保留去重记录
    /// </summary>
    public bool RetainRecordsOnClientDisconnect { get; set; } = true;

    /// <summary>
    /// 去重记录的内存限制（字节）
    /// </summary>
    public long MaxMemoryUsageBytes { get; set; } = 100 * 1024 * 1024; // 100MB

    /// <summary>
    /// 是否启用去重统计
    /// </summary>
    public bool EnableDeduplicationStatistics { get; set; } = true;

    /// <summary>
    /// 批量清理的最大记录数量
    /// </summary>
    public int MaxBatchCleanupSize { get; set; } = 1000;
}

/// <summary>
/// QoS 性能配置选项
/// </summary>
public class QoSPerformanceOptions
{
    /// <summary>
    /// 是否启用内存池优化
    /// </summary>
    public bool EnableMemoryPoolOptimization { get; set; } = true;

    /// <summary>
    /// 内存池的最大对象数量
    /// </summary>
    public int MemoryPoolMaxObjects { get; set; } = 10000;

    /// <summary>
    /// 是否启用批量处理
    /// </summary>
    public bool EnableBatchProcessing { get; set; } = true;

    /// <summary>
    /// 批量处理的最大批次大小
    /// </summary>
    public int MaxBatchSize { get; set; } = 100;

    /// <summary>
    /// 批量处理的超时时间（毫秒）
    /// </summary>
    public int BatchProcessingTimeoutMs { get; set; } = 1000;

    /// <summary>
    /// 是否启用并行处理
    /// </summary>
    public bool EnableParallelProcessing { get; set; } = true;

    /// <summary>
    /// 并行处理的最大并发度
    /// </summary>
    public int MaxParallelism { get; set; } = Environment.ProcessorCount * 2;

    /// <summary>
    /// 是否启用性能计数器
    /// </summary>
    public bool EnablePerformanceCounters { get; set; } = true;

    /// <summary>
    /// 性能监控采样间隔（毫秒）
    /// </summary>
    public int PerformanceMonitoringIntervalMs { get; set; } = 1000;
}

/// <summary>
/// QoS 日志配置选项
/// </summary>
public class QoSLoggingOptions
{
    /// <summary>
    /// 是否启用详细日志
    /// </summary>
    public bool EnableVerboseLogging { get; set; } = false;

    /// <summary>
    /// 是否记录 QoS 处理性能
    /// </summary>
    public bool LogPerformanceMetrics { get; set; } = true;

    /// <summary>
    /// 是否记录重传事件
    /// </summary>
    public bool LogRetransmissionEvents { get; set; } = true;

    /// <summary>
    /// 是否记录去重事件
    /// </summary>
    public bool LogDeduplicationEvents { get; set; } = true;

    /// <summary>
    /// 是否记录确认事件
    /// </summary>
    public bool LogAcknowledgmentEvents { get; set; } = true;

    /// <summary>
    /// 日志级别阈值（毫秒）- 超过此阈值的操作将被记录
    /// </summary>
    public int LogThresholdMs { get; set; } = 100;

    /// <summary>
    /// 是否记录统计信息
    /// </summary>
    public bool LogStatistics { get; set; } = true;

    /// <summary>
    /// 统计信息记录间隔（毫秒）
    /// </summary>
    public int StatisticsLoggingIntervalMs { get; set; } = 60000; // 1分钟
}

/// <summary>
/// 完整的 QoS 配置
/// </summary>
public class QoSConfiguration
{
    /// <summary>
    /// QoS 管理器选项
    /// </summary>
    public QoSManagerOptions Manager { get; set; } = new();

    /// <summary>
    /// 消息确认选项
    /// </summary>
    public MessageAcknowledgmentOptions Acknowledgment { get; set; } = new();

    /// <summary>
    /// 消息重传选项
    /// </summary>
    public MessageRetransmissionOptions Retransmission { get; set; } = new();

    /// <summary>
    /// 消息去重选项
    /// </summary>
    public MessageDeduplicationOptions Deduplication { get; set; } = new();

    /// <summary>
    /// 性能选项
    /// </summary>
    public QoSPerformanceOptions Performance { get; set; } = new();

    /// <summary>
    /// 日志选项
    /// </summary>
    public QoSLoggingOptions Logging { get; set; } = new();

    /// <summary>
    /// 验证配置的有效性
    /// </summary>
    public void Validate()
    {
        if (Manager.MaxConcurrentQoSProcessing <= 0)
            throw new ArgumentException("MaxConcurrentQoSProcessing must be greater than 0");

        if (Acknowledgment.MaxPendingMessagesPerClient <= 0)
            throw new ArgumentException("MaxPendingMessagesPerClient must be greater than 0");

        if (Retransmission.MaxRetransmissionAttempts < 0)
            throw new ArgumentException("MaxRetransmissionAttempts must be non-negative");

        if (Deduplication.MaxProcessedRecordsPerClient <= 0)
            throw new ArgumentException("MaxProcessedRecordsPerClient must be greater than 0");

        if (Performance.MaxBatchSize <= 0)
            throw new ArgumentException("MaxBatchSize must be greater than 0");

        if (Performance.MaxParallelism <= 0)
            throw new ArgumentException("MaxParallelism must be greater than 0");
    }
}
