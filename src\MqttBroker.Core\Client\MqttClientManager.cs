using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Network;
using System.Collections.Concurrent;

namespace MqttBroker.Core.Client;

/// <summary>
/// MQTT 客户端管理器实现
/// </summary>
public class MqttClientManager : IMqttClientManager
{
    private readonly ILogger<MqttClientManager> _logger;
    private readonly ILoggerFactory _loggerFactory;
    private readonly IMqttClientAuthenticator _authenticator;
    private readonly MqttClientManagerOptions _options;
    private readonly ConcurrentDictionary<string, IMqttClient> _clients = new();
    private readonly object _lockObject = new();
    private readonly MqttClientManagerStatistics _statistics = new();
    private bool _disposed;

    /// <summary>
    /// 初始化 MQTT 客户端管理器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="loggerFactory">日志工厂</param>
    /// <param name="authenticator">客户端认证器</param>
    /// <param name="options">管理器选项</param>
    public MqttClientManager(
        ILogger<MqttClientManager> logger,
        ILoggerFactory loggerFactory,
        IMqttClientAuthenticator authenticator,
        IOptions<MqttClientManagerOptions> options)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
        _authenticator = authenticator ?? throw new ArgumentNullException(nameof(authenticator));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));

        _statistics.MaxConnections = _options.MaxConnections;
    }

    /// <summary>
    /// 当前连接的客户端数量
    /// </summary>
    public int ConnectedClientCount => _clients.Count;

    /// <summary>
    /// 最大客户端连接数
    /// </summary>
    public int MaxClientConnections => _options.MaxConnections;

    /// <summary>
    /// 客户端连接事件
    /// </summary>
    public event EventHandler<MqttClientConnectedEventArgs>? ClientConnected;

    /// <summary>
    /// 客户端断开连接事件
    /// </summary>
    public event EventHandler<MqttClientDisconnectedEventArgs>? ClientDisconnected;

    /// <summary>
    /// 客户端认证事件
    /// </summary>
    public event EventHandler<MqttClientAuthenticatedEventArgs>? ClientAuthenticated;

    /// <summary>
    /// 处理客户端连接请求
    /// </summary>
    /// <param name="connection">网络连接</param>
    /// <param name="connectPacket">CONNECT 数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理结果</returns>
    public async Task<MqttConnectResult> HandleConnectAsync(IClientConnection connection, MqttConnectPacket connectPacket, CancellationToken cancellationToken = default)
    {
        if (connection == null)
            throw new ArgumentNullException(nameof(connection));
        if (connectPacket == null)
            throw new ArgumentNullException(nameof(connectPacket));

        try
        {
            _logger.LogInformation("Handling CONNECT request from {RemoteEndPoint}, ClientId: {ClientId}", 
                connection.RemoteEndPoint, connectPacket.ClientId);

            // 检查连接数限制
            if (!CanAcceptNewConnection())
            {
                _logger.LogWarning("Connection limit reached, rejecting client: {ClientId}", connectPacket.ClientId);
                _statistics.RejectedConnections++;
                return MqttConnectResult.Failure(
                    MqttConnectReturnCode.ServerUnavailable,
                    MqttReasonCode.ServerUnavailable,
                    "Connection limit reached"
                );
            }

            // 处理空客户端 ID
            var clientId = connectPacket.ClientId;
            if (string.IsNullOrEmpty(clientId))
            {
                if (connectPacket.CleanSession)
                {
                    clientId = _authenticator.GenerateClientId();
                    _logger.LogDebug("Generated client ID for empty client ID: {ClientId}", clientId);
                }
                else
                {
                    _logger.LogWarning("Empty client ID with CleanSession=false is not allowed");
                    return MqttConnectResult.Failure(
                        MqttConnectReturnCode.IdentifierRejected,
                        MqttReasonCode.ClientIdentifierNotValid,
                        "Empty client ID with CleanSession=false is not allowed"
                    );
                }
            }

            // 检查客户端 ID 是否已存在
            var sessionPresent = false;
            if (_clients.TryGetValue(clientId, out var existingClient))
            {
                if (_options.AllowClientIdReuse)
                {
                    _logger.LogInformation("Disconnecting existing client with same ID: {ClientId}", clientId);
                    await existingClient.DisconnectAsync(DisconnectionReason.ClientDisconnected, cancellationToken);
                    sessionPresent = !connectPacket.CleanSession; // 会话是否存在取决于 CleanSession 标志
                }
                else
                {
                    _logger.LogWarning("Client ID already in use: {ClientId}", clientId);
                    return MqttConnectResult.Failure(
                        MqttConnectReturnCode.IdentifierRejected,
                        MqttReasonCode.ClientIdentifierNotValid,
                        "Client ID already in use"
                    );
                }
            }

            // 执行客户端认证
            var authResult = await _authenticator.AuthenticateAsync(connectPacket, cancellationToken);
            if (!authResult.IsSuccess)
            {
                _logger.LogWarning("Authentication failed for client: {ClientId}, Reason: {Reason}", 
                    clientId, authResult.FailureReason);
                _statistics.AuthenticationFailures++;

                // 触发认证事件
                var tempClient = new MqttClient(clientId, connection, connectPacket, _loggerFactory.CreateLogger<MqttClient>());
                ClientAuthenticated?.Invoke(this, new MqttClientAuthenticatedEventArgs(tempClient, false, authResult.FailureReason));
                tempClient.Dispose();

                return MqttConnectResult.Failure(authResult.ReturnCode, authResult.ReasonCode, authResult.FailureReason);
            }

            // 创建 MQTT 客户端
            var mqttClient = new MqttClient(clientId, connection, connectPacket, _loggerFactory.CreateLogger<MqttClient>());
            mqttClient.SetAuthenticated(true);

            // 添加到客户端集合
            lock (_lockObject)
            {
                _clients.AddOrUpdate(clientId, mqttClient, (key, oldValue) =>
                {
                    oldValue?.Dispose();
                    return mqttClient;
                });

                _statistics.TotalConnections++;
                _statistics.ConnectedClients = ConnectedClientCount;
            }

            // 设置网络连接的客户端 ID
            connection.ClientId = clientId;

            _logger.LogInformation("Client connected successfully: {ClientId}, Username: {Username}, CleanSession: {CleanSession}", 
                clientId, authResult.AuthenticatedUsername, connectPacket.CleanSession);

            // 触发事件
            ClientConnected?.Invoke(this, new MqttClientConnectedEventArgs(mqttClient));
            ClientAuthenticated?.Invoke(this, new MqttClientAuthenticatedEventArgs(mqttClient, true));

            return MqttConnectResult.Success(sessionPresent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling CONNECT request from {RemoteEndPoint}", connection.RemoteEndPoint);
            return MqttConnectResult.Failure(
                MqttConnectReturnCode.ServerUnavailable,
                MqttReasonCode.ServerUnavailable,
                "Internal server error"
            );
        }
    }

    /// <summary>
    /// 处理客户端断开连接
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    /// <param name="reason">断开原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    public async Task HandleDisconnectAsync(string clientId, DisconnectionReason reason, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(clientId))
            return;

        try
        {
            if (_clients.TryRemove(clientId, out var client))
            {
                _logger.LogInformation("Handling disconnect for client: {ClientId}, Reason: {Reason}", clientId, reason);

                lock (_lockObject)
                {
                    _statistics.TotalDisconnections++;
                    _statistics.ConnectedClients = ConnectedClientCount;
                }

                // 触发断开连接事件
                ClientDisconnected?.Invoke(this, new MqttClientDisconnectedEventArgs(client, reason));

                // 断开客户端连接
                await client.DisconnectAsync(reason, cancellationToken);
                client.Dispose();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling disconnect for client: {ClientId}", clientId);
        }
    }

    /// <summary>
    /// 获取客户端连接
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    /// <returns>客户端连接，如果不存在则返回 null</returns>
    public IMqttClient? GetClient(string clientId)
    {
        if (string.IsNullOrEmpty(clientId))
            return null;

        _clients.TryGetValue(clientId, out var client);
        return client;
    }

    /// <summary>
    /// 通过连接ID获取客户端
    /// </summary>
    /// <param name="connectionId">连接 ID</param>
    /// <returns>客户端连接，如果不存在则返回 null</returns>
    public IMqttClient? GetClientByConnectionId(string connectionId)
    {
        if (string.IsNullOrEmpty(connectionId))
            return null;

        return _clients.Values.FirstOrDefault(client => client.Connection.Id == connectionId);
    }

    /// <summary>
    /// 获取所有连接的客户端
    /// </summary>
    /// <returns>所有客户端连接</returns>
    public IEnumerable<IMqttClient> GetAllClients()
    {
        return _clients.Values.ToList();
    }

    /// <summary>
    /// 检查客户端是否已连接
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    /// <returns>如果已连接则返回 true，否则返回 false</returns>
    public bool IsClientConnected(string clientId)
    {
        return !string.IsNullOrEmpty(clientId) && _clients.ContainsKey(clientId);
    }

    /// <summary>
    /// 强制断开客户端连接
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    /// <param name="reason">断开原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>断开任务</returns>
    public async Task DisconnectClientAsync(string clientId, DisconnectionReason reason = DisconnectionReason.ServerDisconnected, CancellationToken cancellationToken = default)
    {
        await HandleDisconnectAsync(clientId, reason, cancellationToken);
    }

    /// <summary>
    /// 清理超时的客户端连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理任务</returns>
    public async Task CleanupTimeoutClientsAsync(CancellationToken cancellationToken = default)
    {
        var timeoutClients = new List<IMqttClient>();

        foreach (var client in GetAllClients())
        {
            try
            {
                if (client.IsTimeout())
                {
                    timeoutClients.Add(client);
                    _logger.LogDebug("Client timeout detected: {ClientId}, LastActivity: {LastActivity}", 
                        client.ClientId, client.LastActivity);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking timeout for client: {ClientId}", client.ClientId);
            }
        }

        if (timeoutClients.Count > 0)
        {
            _logger.LogInformation("Cleaning up {Count} timeout clients", timeoutClients.Count);

            var disconnectTasks = timeoutClients.Select(async client =>
            {
                try
                {
                    await HandleDisconnectAsync(client.ClientId, DisconnectionReason.Timeout, cancellationToken);
                    _statistics.TimeoutConnections++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error disconnecting timeout client: {ClientId}", client.ClientId);
                }
            });

            await Task.WhenAll(disconnectTasks);

            _statistics.CleanedUpConnections += timeoutClients.Count;
            _statistics.LastCleanupTime = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// 获取客户端管理器统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    public MqttClientManagerStatistics GetStatistics()
    {
        _statistics.ConnectedClients = ConnectedClientCount;

        // 按协议版本分组
        _statistics.ConnectionsByProtocolVersion.Clear();
        _statistics.ConnectionsByCleanSession.Clear();

        foreach (var client in GetAllClients())
        {
            // 按协议版本分组
            if (_statistics.ConnectionsByProtocolVersion.ContainsKey(client.ProtocolVersion))
            {
                _statistics.ConnectionsByProtocolVersion[client.ProtocolVersion]++;
            }
            else
            {
                _statistics.ConnectionsByProtocolVersion[client.ProtocolVersion] = 1;
            }

            // 按 Clean Session 分组
            if (_statistics.ConnectionsByCleanSession.ContainsKey(client.CleanSession))
            {
                _statistics.ConnectionsByCleanSession[client.CleanSession]++;
            }
            else
            {
                _statistics.ConnectionsByCleanSession[client.CleanSession] = 1;
            }
        }

        return _statistics;
    }

    /// <summary>
    /// 检查是否可以接受新连接
    /// </summary>
    /// <returns>如果可以接受新连接则返回 true，否则返回 false</returns>
    private bool CanAcceptNewConnection()
    {
        return ConnectedClientCount < MaxClientConnections;
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        try
        {
            var clients = GetAllClients().ToList();
            _logger.LogInformation("Disposing MQTT client manager, disconnecting {Count} clients", clients.Count);

            var disconnectTasks = clients.Select(async client =>
            {
                try
                {
                    await client.DisconnectAsync(DisconnectionReason.ServerShutdown);
                    client.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error disposing client: {ClientId}", client.ClientId);
                }
            });

            Task.WhenAll(disconnectTasks).GetAwaiter().GetResult();

            _clients.Clear();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during MQTT client manager disposal");
        }

        GC.SuppressFinalize(this);
    }
}

/// <summary>
/// MQTT 客户端管理器选项
/// </summary>
public class MqttClientManagerOptions
{
    /// <summary>
    /// 最大客户端连接数
    /// </summary>
    public int MaxConnections { get; set; } = 10000;

    /// <summary>
    /// 是否允许客户端 ID 重用
    /// </summary>
    public bool AllowClientIdReuse { get; set; } = true;

    /// <summary>
    /// 客户端超时检查间隔（毫秒）
    /// </summary>
    public int TimeoutCheckIntervalMs { get; set; } = 30000;

    /// <summary>
    /// 是否启用客户端统计信息收集
    /// </summary>
    public bool EnableStatistics { get; set; } = true;
}
