using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using MqttBroker.Metrics.Collectors;
using MqttBroker.Metrics.Configuration;
using MqttBroker.Metrics.Models;
using Xunit;

namespace MqttBroker.Metrics.Tests.Collectors;

/// <summary>
/// 指标收集器测试
/// </summary>
public class MetricsCollectorTests
{
    private readonly Mock<IConnectionMetricsCollector> _mockConnectionCollector;
    private readonly Mock<IMessageMetricsCollector> _mockMessageCollector;
    private readonly Mock<ISubscriptionMetricsCollector> _mockSubscriptionCollector;
    private readonly Mock<IQoSMetricsCollector> _mockQoSCollector;
    private readonly Mock<ISystemMetricsCollector> _mockSystemCollector;
    private readonly Mock<INetworkMetricsCollector> _mockNetworkCollector;
    private readonly Mock<ILogger<MetricsCollector>> _mockLogger;
    private readonly IOptions<MetricsOptions> _options;

    public MetricsCollectorTests()
    {
        _mockConnectionCollector = new Mock<IConnectionMetricsCollector>();
        _mockMessageCollector = new Mock<IMessageMetricsCollector>();
        _mockSubscriptionCollector = new Mock<ISubscriptionMetricsCollector>();
        _mockQoSCollector = new Mock<IQoSMetricsCollector>();
        _mockSystemCollector = new Mock<ISystemMetricsCollector>();
        _mockNetworkCollector = new Mock<INetworkMetricsCollector>();
        _mockLogger = new Mock<ILogger<MetricsCollector>>();

        _options = Options.Create(new MetricsOptions
        {
            EnableMetrics = true,
            CollectionIntervalMs = 1000,
            EnableSystemMetrics = true,
            EnableNetworkMetrics = true
        });
    }

    [Fact]
    public async Task CollectMetricsAsync_ShouldReturnValidMetrics()
    {
        // Arrange
        var connectionMetrics = new ConnectionMetrics { ActiveConnections = 10 };
        var messageMetrics = new MessageMetrics { MessagesSent = 100 };
        var subscriptionMetrics = new SubscriptionMetrics { TotalSubscriptions = 50 };
        var qosMetrics = new QoSMetrics { QoS0Messages = 80 };
        var systemMetrics = new SystemMetrics { CpuUsage = 25.5 };
        var networkMetrics = new NetworkMetrics { BytesSent = 1024 };

        _mockConnectionCollector.Setup(x => x.CollectConnectionMetrics()).Returns(connectionMetrics);
        _mockMessageCollector.Setup(x => x.CollectMessageMetrics()).Returns(messageMetrics);
        _mockSubscriptionCollector.Setup(x => x.CollectSubscriptionMetrics()).Returns(subscriptionMetrics);
        _mockQoSCollector.Setup(x => x.CollectQoSMetrics()).Returns(qosMetrics);
        _mockSystemCollector.Setup(x => x.CollectSystemMetrics()).Returns(systemMetrics);
        _mockNetworkCollector.Setup(x => x.CollectNetworkMetrics()).Returns(networkMetrics);

        var collector = new MetricsCollector(
            _mockConnectionCollector.Object,
            _mockMessageCollector.Object,
            _mockSubscriptionCollector.Object,
            _mockQoSCollector.Object,
            _mockSystemCollector.Object,
            _mockNetworkCollector.Object,
            _mockLogger.Object,
            _options);

        // Act
        var result = await collector.CollectMetricsAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(10, result.Connection.ActiveConnections);
        Assert.Equal(100, result.Message.MessagesSent);
        Assert.Equal(50, result.Subscription.TotalSubscriptions);
        Assert.Equal(80, result.QoS.QoS0Messages);
        Assert.Equal(25.5, result.System.CpuUsage);
        Assert.Equal(1024, result.Network.BytesSent);
        Assert.True(result.Timestamp <= DateTime.UtcNow);
    }

    [Fact]
    public async Task CollectMetricsAsync_WhenSystemMetricsDisabled_ShouldNotCollectSystemMetrics()
    {
        // Arrange
        var options = Options.Create(new MetricsOptions
        {
            EnableMetrics = true,
            EnableSystemMetrics = false,
            EnableNetworkMetrics = true
        });

        var collector = new MetricsCollector(
            _mockConnectionCollector.Object,
            _mockMessageCollector.Object,
            _mockSubscriptionCollector.Object,
            _mockQoSCollector.Object,
            _mockSystemCollector.Object,
            _mockNetworkCollector.Object,
            _mockLogger.Object,
            options);

        // Act
        var result = await collector.CollectMetricsAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(0, result.System.CpuUsage); // 默认值
        _mockSystemCollector.Verify(x => x.CollectSystemMetrics(), Times.Never);
    }

    [Fact]
    public async Task StartAsync_ShouldStartCollectionTimer()
    {
        // Arrange
        var collector = new MetricsCollector(
            _mockConnectionCollector.Object,
            _mockMessageCollector.Object,
            _mockSubscriptionCollector.Object,
            _mockQoSCollector.Object,
            _mockSystemCollector.Object,
            _mockNetworkCollector.Object,
            _mockLogger.Object,
            _options);

        bool eventFired = false;
        collector.MetricsCollected += (sender, args) => eventFired = true;

        // Act
        await collector.StartAsync();
        await Task.Delay(1500); // 等待超过收集间隔

        // Assert
        Assert.True(eventFired);

        // Cleanup
        await collector.StopAsync();
    }

    [Fact]
    public async Task StopAsync_ShouldStopCollectionTimer()
    {
        // Arrange
        var collector = new MetricsCollector(
            _mockConnectionCollector.Object,
            _mockMessageCollector.Object,
            _mockSubscriptionCollector.Object,
            _mockQoSCollector.Object,
            _mockSystemCollector.Object,
            _mockNetworkCollector.Object,
            _mockLogger.Object,
            _options);

        await collector.StartAsync();

        // Act
        await collector.StopAsync();

        // Assert
        // 验证停止后不再触发事件
        bool eventFired = false;
        collector.MetricsCollected += (sender, args) => eventFired = true;
        await Task.Delay(1500);
        Assert.False(eventFired);
    }
}

/// <summary>
/// 连接指标收集器测试
/// </summary>
public class ConnectionMetricsCollectorTests
{
    [Fact]
    public void RecordConnection_ShouldIncreaseActiveConnections()
    {
        // Arrange
        var collector = new ConnectionMetricsCollector();

        // Act
        collector.RecordConnection("client1");
        collector.RecordConnection("client2");

        // Assert
        var metrics = collector.CollectConnectionMetrics();
        Assert.Equal(2, metrics.ActiveConnections);
        Assert.Equal(2, metrics.TotalConnections);
        Assert.Equal(2, metrics.PeakConnections);
    }

    [Fact]
    public void RecordDisconnection_ShouldDecreaseActiveConnections()
    {
        // Arrange
        var collector = new ConnectionMetricsCollector();
        collector.RecordConnection("client1");
        collector.RecordConnection("client2");

        // Act
        collector.RecordDisconnection("client1", TimeSpan.FromMinutes(5));

        // Assert
        var metrics = collector.CollectConnectionMetrics();
        Assert.Equal(1, metrics.ActiveConnections);
        Assert.Equal(2, metrics.TotalConnections);
        Assert.Equal(2, metrics.PeakConnections);
        Assert.True(metrics.AverageConnectionDuration > 0);
    }

    [Fact]
    public void RecordAuthenticationFailure_ShouldIncreaseFailureCount()
    {
        // Arrange
        var collector = new ConnectionMetricsCollector();

        // Act
        collector.RecordAuthenticationFailure();
        collector.RecordAuthenticationFailure();

        // Assert
        var metrics = collector.CollectConnectionMetrics();
        Assert.Equal(2, metrics.AuthenticationFailures);
    }

    [Fact]
    public void RecordConnectionTimeout_ShouldIncreaseTimeoutCount()
    {
        // Arrange
        var collector = new ConnectionMetricsCollector();

        // Act
        collector.RecordConnectionTimeout();

        // Assert
        var metrics = collector.CollectConnectionMetrics();
        Assert.Equal(1, metrics.ConnectionTimeouts);
    }

    [Fact]
    public void CollectConnectionMetrics_ShouldCalculateRates()
    {
        // Arrange
        var collector = new ConnectionMetricsCollector();

        // Act
        collector.RecordConnection("client1");
        Thread.Sleep(100); // 等待一小段时间
        collector.RecordConnection("client2");

        // Assert
        var metrics = collector.CollectConnectionMetrics();
        Assert.True(metrics.ConnectionRate >= 0);
        Assert.True(metrics.DisconnectionRate >= 0);
    }
}
