﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MqttBroker.Core;
using MqttBroker.Network;
using MqttBroker.Storage;
using MqttBroker.Configuration;
using MqttBroker.Logging;
using MqttBroker.Metrics;

namespace MqttBroker.Host;

public class Program
{
    public static async Task Main(string[] args)
    {
        var host = CreateHostBuilder(args).Build();

        var logger = host.Services.GetRequiredService<ILogger<Program>>();
        logger.LogInformation("MQTT Broker 服务器正在启动...");

        try
        {
            await host.RunAsync();
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "MQTT Broker 服务器启动失败");
            throw;
        }
    }

    private static IHostBuilder CreateHostBuilder(string[] args) =>
        Microsoft.Extensions.Hosting.Host.CreateDefaultBuilder(args)
            .ConfigureServices((context, services) =>
            {
                // 注册核心服务
                services.AddMqttBrokerCore();
                services.AddMqttBrokerNetwork(context.Configuration);
                services.AddMqttBrokerStorage(context.Configuration);
                services.AddMqttBrokerConfiguration();
                services.AddMqttBrokerLogging();
                services.AddMqttBrokerMetrics();

                // 注册主机服务
                services.AddHostedService<MqttBrokerService>();
            });
}
