using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using MqttBroker.Core.Client;
using MqttBroker.Core.Message;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Topic;
using Xunit;

namespace MqttBroker.Tests.Unit.Message;

/// <summary>
/// 消息路由引擎单元测试
/// </summary>
public class MessageRoutingEngineTests
{
    private readonly Mock<ITopicSubscriptionManager> _mockSubscriptionManager;
    private readonly Mock<IMessageFilterManager> _mockFilterManager;
    private readonly Mock<IMessagePersistenceService> _mockPersistenceService;
    private readonly Mock<IDeadLetterQueueService> _mockDeadLetterService;
    private readonly Mock<ILogger<MessageRoutingEngine>> _mockLogger;
    private readonly MessageRoutingEngine _routingEngine;

    public MessageRoutingEngineTests()
    {
        _mockSubscriptionManager = new Mock<ITopicSubscriptionManager>();
        _mockFilterManager = new Mock<IMessageFilterManager>();
        _mockPersistenceService = new Mock<IMessagePersistenceService>();
        _mockDeadLetterService = new Mock<IDeadLetterQueueService>();
        _mockLogger = new Mock<ILogger<MessageRoutingEngine>>();

        var options = Options.Create(new MessageRoutingOptions());

        _routingEngine = new MessageRoutingEngine(
            _mockSubscriptionManager.Object,
            _mockFilterManager.Object,
            _mockPersistenceService.Object,
            _mockDeadLetterService.Object,
            _mockLogger.Object,
            options);
    }

    [Fact]
    public async Task RouteMessageAsync_WithNoSubscribers_ReturnsEmptyResult()
    {
        // Arrange
        var publishPacket = MqttPublishPacket.Create("test/topic", "test message"u8.ToArray());
        
        _mockSubscriptionManager
            .Setup(x => x.GetSubscribersAsync("test/topic", It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<TopicSubscriber>());

        // Act
        var result = await _routingEngine.RouteMessageAsync(publishPacket, "test-publisher");

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal("test/topic", result.TopicName);
        Assert.Equal(0, result.OnlineDeliveries);
        Assert.Equal(0, result.OfflineStorages);
        Assert.Equal(0, result.FailedDeliveries);
        Assert.Equal(0, result.FilteredCount);
    }

    [Fact]
    public async Task RouteMessageAsync_WithOnlineSubscriber_DeliversMessage()
    {
        // Arrange
        var publishPacket = MqttPublishPacket.Create("test/topic", "test message"u8.ToArray(), MqttQoSLevel.AtLeastOnce, false, 1);
        
        var mockClient = new Mock<IMqttClient>();
        mockClient.Setup(x => x.State).Returns(MqttClientState.Authenticated);
        mockClient.Setup(x => x.SendPacketAsync(It.IsAny<IMqttPacket>(), It.IsAny<CancellationToken>()))
               .Returns(Task.CompletedTask);

        var subscriber = new TopicSubscriber
        {
            ClientId = "test-client",
            TopicFilter = "test/+",
            QoSLevel = MqttQoSLevel.AtLeastOnce,
            Client = mockClient.Object,
            Options = new MqttSubscriptionOptions()
        };

        _mockSubscriptionManager
            .Setup(x => x.GetSubscribersAsync("test/topic", It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<TopicSubscriber> { subscriber });

        _mockFilterManager
            .Setup(x => x.ApplyFiltersAsync(It.IsAny<MessageFilterContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(MessageFilterResult.Allow());

        // Act
        var result = await _routingEngine.RouteMessageAsync(publishPacket, "test-publisher");

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal("test/topic", result.TopicName);
        Assert.Equal(1, result.OnlineDeliveries);
        Assert.Equal(0, result.OfflineStorages);
        Assert.Equal(0, result.FailedDeliveries);
        Assert.Equal(0, result.FilteredCount);

        // Verify client received the message
        mockClient.Verify(x => x.SendPacketAsync(It.IsAny<IMqttPacket>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task RouteMessageAsync_WithOfflineSubscriber_StoresMessage()
    {
        // Arrange
        var publishPacket = MqttPublishPacket.Create("test/topic", "test message"u8.ToArray(), MqttQoSLevel.AtLeastOnce, false, 2);
        
        var mockClient = new Mock<IMqttClient>();
        mockClient.Setup(x => x.State).Returns(MqttClientState.Disconnected);

        var subscriber = new TopicSubscriber
        {
            ClientId = "test-client",
            TopicFilter = "test/+",
            QoSLevel = MqttQoSLevel.AtLeastOnce,
            Client = mockClient.Object,
            Options = new MqttSubscriptionOptions()
        };

        _mockSubscriptionManager
            .Setup(x => x.GetSubscribersAsync("test/topic", It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<TopicSubscriber> { subscriber });

        _mockFilterManager
            .Setup(x => x.ApplyFiltersAsync(It.IsAny<MessageFilterContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(MessageFilterResult.Allow());

        _mockPersistenceService
            .Setup(x => x.StoreOfflineMessageAsync("test-client", It.IsAny<MqttPublishPacket>(), MqttQoSLevel.AtLeastOnce, It.IsAny<CancellationToken>()))
            .ReturnsAsync(MessageStorageResult.Success("msg-123", DateTime.UtcNow));

        // Act
        var result = await _routingEngine.RouteMessageAsync(publishPacket, "test-publisher");

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal("test/topic", result.TopicName);
        Assert.Equal(0, result.OnlineDeliveries);
        Assert.Equal(1, result.OfflineStorages);
        Assert.Equal(0, result.FailedDeliveries);
        Assert.Equal(0, result.FilteredCount);

        // Verify message was stored
        _mockPersistenceService.Verify(x => x.StoreOfflineMessageAsync("test-client", It.IsAny<MqttPublishPacket>(), MqttQoSLevel.AtLeastOnce, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task RouteMessageAsync_WithFilteredMessage_ReturnsFilteredResult()
    {
        // Arrange
        var publishPacket = MqttPublishPacket.Create("test/topic", "test message"u8.ToArray());
        
        var mockClient = new Mock<IMqttClient>();
        mockClient.Setup(x => x.State).Returns(MqttClientState.Authenticated);

        var subscriber = new TopicSubscriber
        {
            ClientId = "test-client",
            TopicFilter = "test/+",
            QoSLevel = MqttQoSLevel.AtMostOnce,
            Client = mockClient.Object,
            Options = new MqttSubscriptionOptions()
        };

        _mockSubscriptionManager
            .Setup(x => x.GetSubscribersAsync("test/topic", It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<TopicSubscriber> { subscriber });

        _mockFilterManager
            .Setup(x => x.ApplyFiltersAsync(It.IsAny<MessageFilterContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(MessageFilterResult.Deny("Message blocked by filter"));

        // Act
        var result = await _routingEngine.RouteMessageAsync(publishPacket, "test-publisher");

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal("test/topic", result.TopicName);
        Assert.Equal(0, result.OnlineDeliveries);
        Assert.Equal(0, result.OfflineStorages);
        Assert.Equal(0, result.FailedDeliveries);
        Assert.Equal(1, result.FilteredCount);

        // Verify client did not receive the message
        mockClient.Verify(x => x.SendPacketAsync(It.IsAny<IMqttPacket>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task RouteMessagesAsync_WithMultipleMessages_ProcessesAllMessages()
    {
        // Arrange
        var messages = new List<MessageRoutingRequest>
        {
            MessageRoutingRequest.Create(MqttPublishPacket.Create("topic1", "message1"u8.ToArray()), "publisher1"),
            MessageRoutingRequest.Create(MqttPublishPacket.Create("topic2", "message2"u8.ToArray()), "publisher2", MessageRoutingPriority.High)
        };

        _mockSubscriptionManager
            .Setup(x => x.GetSubscribersAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<TopicSubscriber>());

        // Act
        var results = await _routingEngine.RouteMessagesAsync(messages);

        // Assert
        Assert.Equal(2, results.Count);
        Assert.All(results, r => Assert.True(r.IsSuccess));
        
        // Verify high priority message was processed first
        Assert.Equal("topic2", results[0].TopicName);
        Assert.Equal("topic1", results[1].TopicName);
    }

    [Fact]
    public async Task ProcessOfflineMessagesAsync_WithOfflineMessages_DeliversMessages()
    {
        // Arrange
        var clientId = "test-client";
        var offlineMessages = new List<OfflineMessage>
        {
            new OfflineMessage
            {
                MessageId = "msg-1",
                ClientId = clientId,
                TopicName = "test/topic",
                Payload = "message1"u8.ToArray(),
                QoSLevel = MqttQoSLevel.AtLeastOnce,
                PacketIdentifier = 1,
                StoredAt = DateTime.UtcNow.AddMinutes(-5)
            }
        };

        var mockClient = new Mock<IMqttClient>();
        mockClient.Setup(x => x.State).Returns(MqttClientState.Authenticated);
        mockClient.Setup(x => x.SendPacketAsync(It.IsAny<IMqttPacket>(), It.IsAny<CancellationToken>()))
               .Returns(Task.CompletedTask);

        var subscriber = new TopicSubscriber
        {
            ClientId = clientId,
            TopicFilter = "test/topic",
            QoSLevel = MqttQoSLevel.AtLeastOnce,
            Client = mockClient.Object
        };

        _mockPersistenceService
            .Setup(x => x.GetOfflineMessagesAsync(clientId, It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(offlineMessages);

        _mockSubscriptionManager
            .Setup(x => x.GetSubscriberAsync(clientId, "test/topic", It.IsAny<CancellationToken>()))
            .ReturnsAsync(subscriber);

        _mockPersistenceService
            .Setup(x => x.DeleteOfflineMessagesAsync(It.IsAny<IList<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new BatchDeleteResult { TotalCount = 1, SuccessCount = 1 });

        // Act
        var result = await _routingEngine.ProcessOfflineMessagesAsync(clientId);

        // Assert
        if (!result.IsSuccess)
        {
            throw new Exception($"ProcessOfflineMessagesAsync failed. ErrorMessage: {result.ErrorMessage}");
        }
        Assert.True(result.IsSuccess);
        Assert.Equal(clientId, result.ClientId);
        Assert.Equal(1, result.ProcessedMessages);
        Assert.Equal(1, result.DeliveredMessages);
        Assert.Equal(0, result.FailedMessages);

        // Verify message was delivered and deleted
        mockClient.Verify(x => x.SendPacketAsync(It.IsAny<IMqttPacket>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockPersistenceService.Verify(x => x.DeleteOfflineMessagesAsync(It.Is<IList<string>>(ids => ids.Contains("msg-1")), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetStatisticsAsync_ReturnsCorrectStatistics()
    {
        // Act
        var statistics = await _routingEngine.GetStatisticsAsync();

        // Assert
        Assert.NotNull(statistics);
        Assert.Equal(0, statistics.TotalMessages);
        Assert.Equal(0, statistics.OnlineDeliveries);
        Assert.Equal(0, statistics.OfflineStorages);
        Assert.Equal(0, statistics.FailedRoutings);
        Assert.Equal(0, statistics.FilteredMessages);
    }

    [Fact]
    public async Task RouteToSubscriberAsync_WithOnlineClient_ReturnsOnlineDelivery()
    {
        // Arrange
        var publishPacket = MqttPublishPacket.Create("test/topic", "test message"u8.ToArray(), MqttQoSLevel.AtLeastOnce, false, 1);

        var mockClient = new Mock<IMqttClient>();
        mockClient.Setup(x => x.State).Returns(MqttClientState.Authenticated);
        mockClient.Setup(x => x.SendPacketAsync(It.IsAny<IMqttPacket>(), It.IsAny<CancellationToken>()))
               .Returns(Task.CompletedTask);

        var subscriber = new TopicSubscriber
        {
            ClientId = "test-client",
            TopicFilter = "test/+",
            QoSLevel = MqttQoSLevel.AtLeastOnce,
            Client = mockClient.Object,
            Options = new MqttSubscriptionOptions()
        };

        _mockFilterManager
            .Setup(x => x.ApplyFiltersAsync(It.IsAny<MessageFilterContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(MessageFilterResult.Allow());

        // Act - 使用反射调用私有方法
        var method = typeof(MessageRoutingEngine).GetMethod("RouteToSubscriberAsync",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

        var task = (Task<SubscriberRoutingResult>)method!.Invoke(_routingEngine,
            new object[] { publishPacket, subscriber, "test-publisher", CancellationToken.None })!;

        var result = await task;

        // Assert
        Assert.NotNull(result);
        if (result.RoutingType != MessageRoutingType.OnlineDelivery)
        {
            // 输出调试信息
            throw new Exception($"Expected OnlineDelivery but got {result.RoutingType}. IsSuccess: {result.IsSuccess}, ErrorMessage: {result.ErrorMessage}");
        }
        Assert.Equal(MessageRoutingType.OnlineDelivery, result.RoutingType);
        Assert.True(result.IsSuccess);
        Assert.Equal("test-client", result.ClientId);
        Assert.Equal("test/+", result.TopicFilter);
        Assert.Equal(MqttQoSLevel.AtLeastOnce, result.QoSLevel);

        // Verify client received the message
        mockClient.Verify(x => x.SendPacketAsync(It.IsAny<IMqttPacket>(), It.IsAny<CancellationToken>()), Times.Once);
    }
}
