using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MqttBroker.Metrics.Configuration;
using MqttBroker.Metrics.Models;
using MqttBroker.Metrics.Storage;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace MqttBroker.Metrics.Export;

/// <summary>
/// 指标导出管理器实现
/// </summary>
public class MetricsExportManager : IMetricsExportManager, IDisposable
{
    private readonly ConcurrentDictionary<string, IMetricsExporter> _exporters = new();
    private readonly ILogger<MetricsExportManager> _logger;
    private readonly MetricsExportOptions _options;

    // 统计信息
    private long _totalExports;
    private long _successfulExports;
    private long _failedExports;
    private double _totalExportTime;
    private long _totalExportedDataSize;
    private DateTime? _lastExportTime;
    private readonly ConcurrentDictionary<string, FormatExportStatistics> _formatStatistics = new();

    private bool _disposed;

    /// <summary>
    /// 构造函数
    /// </summary>
    public MetricsExportManager(
        ILogger<MetricsExportManager> logger,
        IOptions<MetricsExportOptions> options)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));

        _logger.LogInformation("指标导出管理器已初始化");
    }

    /// <summary>
    /// 注册导出器
    /// </summary>
    public void RegisterExporter(IMetricsExporter exporter)
    {
        if (exporter == null)
            throw new ArgumentNullException(nameof(exporter));

        _exporters[exporter.Format] = exporter;
        _formatStatistics[exporter.Format] = new FormatExportStatistics
        {
            Format = exporter.Format
        };

        _logger.LogInformation("已注册导出器: {Format}", exporter.Format);
    }

    /// <summary>
    /// 获取支持的导出格式
    /// </summary>
    public IEnumerable<string> GetSupportedFormats()
    {
        return _exporters.Keys.ToList();
    }

    /// <summary>
    /// 导出当前指标
    /// </summary>
    public async Task<string> ExportCurrentMetricsAsync(PerformanceMetrics metrics, string format, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            if (metrics == null)
                throw new ArgumentNullException(nameof(metrics));

            if (string.IsNullOrEmpty(format))
                throw new ArgumentException("导出格式不能为空", nameof(format));

            if (!_exporters.TryGetValue(format.ToLowerInvariant(), out var exporter))
                throw new NotSupportedException($"不支持的导出格式: {format}");

            var data = await exporter.ExportCurrentMetricsAsync(metrics, cancellationToken);
            
            // 更新统计信息
            UpdateStatistics(format, stopwatch.Elapsed, data.Length, true);

            _logger.LogDebug("已导出当前指标，格式: {Format}，大小: {Size} 字节，耗时: {Duration}ms",
                format, data.Length, stopwatch.ElapsedMilliseconds);

            return data;
        }
        catch (Exception ex)
        {
            UpdateStatistics(format, stopwatch.Elapsed, 0, false);
            _logger.LogError(ex, "导出当前指标时发生错误，格式: {Format}", format);
            throw;
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    /// <summary>
    /// 导出历史指标
    /// </summary>
    public async Task<string> ExportHistoricalMetricsAsync(IEnumerable<PerformanceMetrics> metricsList, string format, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            if (metricsList == null)
                throw new ArgumentNullException(nameof(metricsList));

            if (string.IsNullOrEmpty(format))
                throw new ArgumentException("导出格式不能为空", nameof(format));

            if (!_exporters.TryGetValue(format.ToLowerInvariant(), out var exporter))
                throw new NotSupportedException($"不支持的导出格式: {format}");

            var data = await exporter.ExportHistoricalMetricsAsync(metricsList, cancellationToken);
            
            // 更新统计信息
            UpdateStatistics(format, stopwatch.Elapsed, data.Length, true);

            _logger.LogDebug("已导出历史指标，格式: {Format}，大小: {Size} 字节，耗时: {Duration}ms",
                format, data.Length, stopwatch.ElapsedMilliseconds);

            return data;
        }
        catch (Exception ex)
        {
            UpdateStatistics(format, stopwatch.Elapsed, 0, false);
            _logger.LogError(ex, "导出历史指标时发生错误，格式: {Format}", format);
            throw;
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    /// <summary>
    /// 导出聚合指标
    /// </summary>
    public async Task<string> ExportAggregatedMetricsAsync(IEnumerable<AggregatedMetrics> aggregatedMetrics, string format, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            if (aggregatedMetrics == null)
                throw new ArgumentNullException(nameof(aggregatedMetrics));

            if (string.IsNullOrEmpty(format))
                throw new ArgumentException("导出格式不能为空", nameof(format));

            if (!_exporters.TryGetValue(format.ToLowerInvariant(), out var exporter))
                throw new NotSupportedException($"不支持的导出格式: {format}");

            var data = await exporter.ExportAggregatedMetricsAsync(aggregatedMetrics, cancellationToken);
            
            // 更新统计信息
            UpdateStatistics(format, stopwatch.Elapsed, data.Length, true);

            _logger.LogDebug("已导出聚合指标，格式: {Format}，大小: {Size} 字节，耗时: {Duration}ms",
                format, data.Length, stopwatch.ElapsedMilliseconds);

            return data;
        }
        catch (Exception ex)
        {
            UpdateStatistics(format, stopwatch.Elapsed, 0, false);
            _logger.LogError(ex, "导出聚合指标时发生错误，格式: {Format}", format);
            throw;
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    /// <summary>
    /// 导出到文件
    /// </summary>
    public async Task ExportToFileAsync(string data, string filePath, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(data))
                throw new ArgumentException("数据不能为空", nameof(data));

            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            // 确保目录存在
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            await File.WriteAllTextAsync(filePath, data, cancellationToken);

            _logger.LogInformation("已导出数据到文件: {FilePath}，大小: {Size} 字节", filePath, data.Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出数据到文件时发生错误: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// 获取导出统计信息
    /// </summary>
    public ExportStatistics GetStatistics()
    {
        return new ExportStatistics
        {
            TotalExports = _totalExports,
            SuccessfulExports = _successfulExports,
            FailedExports = _failedExports,
            AverageExportDurationMs = _totalExports > 0 ? _totalExportTime / _totalExports : 0,
            TotalExportedDataSize = _totalExportedDataSize,
            LastExportTime = _lastExportTime,
            ExportsByFormat = new Dictionary<string, FormatExportStatistics>(_formatStatistics)
        };
    }

    /// <summary>
    /// 更新统计信息
    /// </summary>
    private void UpdateStatistics(string format, TimeSpan duration, long dataSize, bool success)
    {
        Interlocked.Increment(ref _totalExports);
        _totalExportTime += duration.TotalMilliseconds;
        _lastExportTime = DateTime.UtcNow;

        if (success)
        {
            Interlocked.Increment(ref _successfulExports);
            Interlocked.Add(ref _totalExportedDataSize, dataSize);
        }
        else
        {
            Interlocked.Increment(ref _failedExports);
        }

        // 更新格式统计
        if (_formatStatistics.TryGetValue(format.ToLowerInvariant(), out var formatStats))
        {
            formatStats.ExportCount++;
            formatStats.AverageExportDurationMs = (formatStats.AverageExportDurationMs * (formatStats.ExportCount - 1) + duration.TotalMilliseconds) / formatStats.ExportCount;
            formatStats.LastExportTime = DateTime.UtcNow;
            
            if (success)
            {
                formatStats.TotalDataSize += dataSize;
            }
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;
        }
        GC.SuppressFinalize(this);
    }
}

/// <summary>
/// Prometheus 导出器实现
/// </summary>
public class PrometheusExporter : IPrometheusExporter
{
    private readonly ILogger<PrometheusExporter> _logger;

    /// <summary>
    /// Prometheus 端点路径
    /// </summary>
    public string EndpointPath => "/metrics";

    /// <summary>
    /// 构造函数
    /// </summary>
    public PrometheusExporter(ILogger<PrometheusExporter> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 导出 Prometheus 格式的指标
    /// </summary>
    public Task<string> ExportPrometheusMetricsAsync(PerformanceMetrics metrics, CancellationToken cancellationToken = default)
    {
        try
        {
            if (metrics == null)
                throw new ArgumentNullException(nameof(metrics));

            var prometheusMetrics = new List<string>();

            // 连接指标
            prometheusMetrics.Add($"# HELP mqtt_active_connections Current number of active MQTT connections");
            prometheusMetrics.Add($"# TYPE mqtt_active_connections gauge");
            prometheusMetrics.Add($"mqtt_active_connections {metrics.Connection.ActiveConnections}");

            prometheusMetrics.Add($"# HELP mqtt_total_connections Total number of MQTT connections");
            prometheusMetrics.Add($"# TYPE mqtt_total_connections counter");
            prometheusMetrics.Add($"mqtt_total_connections {metrics.Connection.TotalConnections}");

            // 消息指标
            prometheusMetrics.Add($"# HELP mqtt_messages_sent_total Total number of messages sent");
            prometheusMetrics.Add($"# TYPE mqtt_messages_sent_total counter");
            prometheusMetrics.Add($"mqtt_messages_sent_total {metrics.Message.MessagesSent}");

            prometheusMetrics.Add($"# HELP mqtt_messages_received_total Total number of messages received");
            prometheusMetrics.Add($"# TYPE mqtt_messages_received_total counter");
            prometheusMetrics.Add($"mqtt_messages_received_total {metrics.Message.MessagesReceived}");

            prometheusMetrics.Add($"# HELP mqtt_message_send_rate Messages sent per second");
            prometheusMetrics.Add($"# TYPE mqtt_message_send_rate gauge");
            prometheusMetrics.Add($"mqtt_message_send_rate {metrics.Message.MessageSendRate:F2}");

            // QoS 指标
            prometheusMetrics.Add($"# HELP mqtt_qos0_messages_total Total QoS 0 messages");
            prometheusMetrics.Add($"# TYPE mqtt_qos0_messages_total counter");
            prometheusMetrics.Add($"mqtt_qos0_messages_total {metrics.QoS.QoS0Messages}");

            prometheusMetrics.Add($"# HELP mqtt_qos1_messages_total Total QoS 1 messages");
            prometheusMetrics.Add($"# TYPE mqtt_qos1_messages_total counter");
            prometheusMetrics.Add($"mqtt_qos1_messages_total {metrics.QoS.QoS1Messages}");

            prometheusMetrics.Add($"# HELP mqtt_qos2_messages_total Total QoS 2 messages");
            prometheusMetrics.Add($"# TYPE mqtt_qos2_messages_total counter");
            prometheusMetrics.Add($"mqtt_qos2_messages_total {metrics.QoS.QoS2Messages}");

            // 系统指标
            prometheusMetrics.Add($"# HELP mqtt_cpu_usage_percent CPU usage percentage");
            prometheusMetrics.Add($"# TYPE mqtt_cpu_usage_percent gauge");
            prometheusMetrics.Add($"mqtt_cpu_usage_percent {metrics.System.CpuUsage:F2}");

            prometheusMetrics.Add($"# HELP mqtt_memory_usage_bytes Memory usage in bytes");
            prometheusMetrics.Add($"# TYPE mqtt_memory_usage_bytes gauge");
            prometheusMetrics.Add($"mqtt_memory_usage_bytes {metrics.System.Memory.TotalMemory}");

            var result = string.Join("\n", prometheusMetrics) + "\n";
            
            _logger.LogDebug("已导出 Prometheus 格式指标，大小: {Size} 字节", result.Length);
            
            return Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出 Prometheus 格式指标时发生错误");
            throw;
        }
    }
}
