using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;

namespace MqttBroker.Core.Message;

/// <summary>
/// 死信队列服务接口
/// </summary>
public interface IDeadLetterQueueService
{
    /// <summary>
    /// 添加消息到死信队列
    /// </summary>
    /// <param name="deadLetterMessage">死信消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功添加</returns>
    Task<bool> AddToDeadLetterQueueAsync(DeadLetterMessage deadLetterMessage, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取死信队列消息
    /// </summary>
    /// <param name="maxMessages">最大消息数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>死信消息列表</returns>
    Task<IList<DeadLetterMessage>> GetDeadLetterMessagesAsync(int maxMessages = 100, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据客户端ID获取死信消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="maxMessages">最大消息数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>死信消息列表</returns>
    Task<IList<DeadLetterMessage>> GetDeadLetterMessagesByClientAsync(string clientId, int maxMessages = 100, CancellationToken cancellationToken = default);

    /// <summary>
    /// 重新处理死信消息
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重新处理结果</returns>
    Task<DeadLetterReprocessResult> ReprocessDeadLetterMessageAsync(string messageId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量重新处理死信消息
    /// </summary>
    /// <param name="messageIds">消息ID列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>批量重新处理结果</returns>
    Task<BatchReprocessResult> ReprocessDeadLetterMessagesAsync(IList<string> messageIds, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除死信消息
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功删除</returns>
    Task<bool> DeleteDeadLetterMessageAsync(string messageId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理过期死信消息
    /// </summary>
    /// <param name="expiredBefore">过期时间点</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理结果</returns>
    Task<DeadLetterCleanupResult> CleanupExpiredDeadLetterMessagesAsync(DateTime expiredBefore, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取死信队列统计信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>统计信息</returns>
    Task<DeadLetterStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 死信消息
/// </summary>
public class DeadLetterMessage
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// 原始客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 主题名称
    /// </summary>
    public string TopicName { get; set; } = string.Empty;

    /// <summary>
    /// 消息负载
    /// </summary>
    public byte[] Payload { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// QoS级别
    /// </summary>
    public MqttQoSLevel QoSLevel { get; set; }

    /// <summary>
    /// 是否保留消息
    /// </summary>
    public bool Retain { get; set; }

    /// <summary>
    /// 失败原因
    /// </summary>
    public string FailureReason { get; set; } = string.Empty;

    /// <summary>
    /// 失败类型
    /// </summary>
    public DeadLetterReason FailureType { get; set; }

    /// <summary>
    /// 原始发布时间
    /// </summary>
    public DateTime OriginalPublishTime { get; set; }

    /// <summary>
    /// 进入死信队列时间
    /// </summary>
    public DateTime DeadLetterTime { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// 最后重试时间
    /// </summary>
    public DateTime? LastRetryTime { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// 消息属性
    /// </summary>
    public Dictionary<string, object>? Properties { get; set; }

    /// <summary>
    /// 创建死信消息
    /// </summary>
    /// <param name="publishPacket">发布数据包</param>
    /// <param name="clientId">客户端ID</param>
    /// <param name="failureReason">失败原因</param>
    /// <param name="failureType">失败类型</param>
    /// <param name="retryCount">重试次数</param>
    /// <returns>死信消息</returns>
    public static DeadLetterMessage Create(MqttPublishPacket publishPacket, string clientId, string failureReason, DeadLetterReason failureType, int retryCount = 0)
    {
        var now = DateTime.UtcNow;
        return new DeadLetterMessage
        {
            MessageId = Guid.NewGuid().ToString(),
            ClientId = clientId,
            TopicName = publishPacket.Topic,
            Payload = publishPacket.Payload,
            QoSLevel = publishPacket.QoSLevel,
            Retain = publishPacket.Retain,
            FailureReason = failureReason,
            FailureType = failureType,
            OriginalPublishTime = now,
            DeadLetterTime = now,
            RetryCount = retryCount,
            ExpiresAt = now.AddDays(7) // 默认7天过期
        };
    }

    /// <summary>
    /// 转换为MQTT发布数据包
    /// </summary>
    /// <returns>MQTT发布数据包</returns>
    public MqttPublishPacket ToPublishPacket()
    {
        // 为QoS > 0的消息生成新的PacketIdentifier
        ushort? packetIdentifier = null;
        if (QoSLevel > MqttQoSLevel.AtMostOnce)
        {
            packetIdentifier = (ushort)(DateTime.UtcNow.Ticks % ushort.MaxValue + 1);
        }

        return MqttPublishPacket.Create(TopicName, Payload, QoSLevel, Retain, packetIdentifier);
    }
}

/// <summary>
/// 死信原因
/// </summary>
public enum DeadLetterReason
{
    /// <summary>
    /// 客户端离线
    /// </summary>
    ClientOffline,

    /// <summary>
    /// 网络错误
    /// </summary>
    NetworkError,

    /// <summary>
    /// 超时
    /// </summary>
    Timeout,

    /// <summary>
    /// 客户端拒绝
    /// </summary>
    ClientRejected,

    /// <summary>
    /// 权限不足
    /// </summary>
    InsufficientPermissions,

    /// <summary>
    /// 消息过大
    /// </summary>
    MessageTooLarge,

    /// <summary>
    /// 队列满
    /// </summary>
    QueueFull,

    /// <summary>
    /// 重试次数超限
    /// </summary>
    MaxRetriesExceeded,

    /// <summary>
    /// 系统错误
    /// </summary>
    SystemError,

    /// <summary>
    /// 未知错误
    /// </summary>
    Unknown
}

/// <summary>
/// 死信重新处理结果
/// </summary>
public class DeadLetterReprocessResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 消息ID
    /// </summary>
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// 重新处理结果
    /// </summary>
    public MessageRoutingResult? RoutingResult { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 处理时间
    /// </summary>
    public DateTime ProcessedAt { get; set; }
}

/// <summary>
/// 批量重新处理结果
/// </summary>
public class BatchReprocessResult
{
    /// <summary>
    /// 总数量
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 成功数量
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailedCount { get; set; }

    /// <summary>
    /// 处理结果详情
    /// </summary>
    public IList<DeadLetterReprocessResult> Results { get; set; } = new List<DeadLetterReprocessResult>();

    /// <summary>
    /// 处理耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }
}

/// <summary>
/// 死信清理结果
/// </summary>
public class DeadLetterCleanupResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 清理的消息数量
    /// </summary>
    public int CleanedCount { get; set; }

    /// <summary>
    /// 清理耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 死信队列统计信息
/// </summary>
public class DeadLetterStatistics
{
    /// <summary>
    /// 总死信消息数
    /// </summary>
    public long TotalDeadLetterMessages { get; set; }

    /// <summary>
    /// 今日新增死信消息数
    /// </summary>
    public long TodayDeadLetterMessages { get; set; }

    /// <summary>
    /// 按失败原因分组的统计
    /// </summary>
    public Dictionary<DeadLetterReason, long> MessagesByReason { get; set; } = new();

    /// <summary>
    /// 按客户端分组的统计
    /// </summary>
    public Dictionary<string, long> MessagesByClient { get; set; } = new();

    /// <summary>
    /// 重新处理成功数
    /// </summary>
    public long ReprocessedSuccessCount { get; set; }

    /// <summary>
    /// 重新处理失败数
    /// </summary>
    public long ReprocessedFailedCount { get; set; }

    /// <summary>
    /// 平均消息大小（字节）
    /// </summary>
    public double AverageMessageSize { get; set; }

    /// <summary>
    /// 最早死信消息时间
    /// </summary>
    public DateTime? EarliestDeadLetterTime { get; set; }

    /// <summary>
    /// 最新死信消息时间
    /// </summary>
    public DateTime? LatestDeadLetterTime { get; set; }
}
