namespace MqttBroker.Metrics.Models;

/// <summary>
/// 告警级别
/// </summary>
public enum AlertLevel
{
    /// <summary>
    /// 信息
    /// </summary>
    Info = 0,

    /// <summary>
    /// 警告
    /// </summary>
    Warning = 1,

    /// <summary>
    /// 错误
    /// </summary>
    Error = 2,

    /// <summary>
    /// 严重
    /// </summary>
    Critical = 3
}

/// <summary>
/// 告警状态
/// </summary>
public enum AlertStatus
{
    /// <summary>
    /// 活跃
    /// </summary>
    Active = 0,

    /// <summary>
    /// 已解决
    /// </summary>
    Resolved = 1,

    /// <summary>
    /// 已确认
    /// </summary>
    Acknowledged = 2,

    /// <summary>
    /// 已抑制
    /// </summary>
    Suppressed = 3
}

/// <summary>
/// 告警规则类型
/// </summary>
public enum AlertRuleType
{
    /// <summary>
    /// 阈值告警
    /// </summary>
    Threshold = 0,

    /// <summary>
    /// 变化率告警
    /// </summary>
    ChangeRate = 1,

    /// <summary>
    /// 异常检测告警
    /// </summary>
    Anomaly = 2,

    /// <summary>
    /// 复合条件告警
    /// </summary>
    Composite = 3
}

/// <summary>
/// 比较操作符
/// </summary>
public enum ComparisonOperator
{
    /// <summary>
    /// 大于
    /// </summary>
    GreaterThan = 0,

    /// <summary>
    /// 大于等于
    /// </summary>
    GreaterThanOrEqual = 1,

    /// <summary>
    /// 小于
    /// </summary>
    LessThan = 2,

    /// <summary>
    /// 小于等于
    /// </summary>
    LessThanOrEqual = 3,

    /// <summary>
    /// 等于
    /// </summary>
    Equal = 4,

    /// <summary>
    /// 不等于
    /// </summary>
    NotEqual = 5
}

/// <summary>
/// 告警规则
/// </summary>
public class AlertRule
{
    /// <summary>
    /// 规则 ID
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 规则名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 规则描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 规则类型
    /// </summary>
    public AlertRuleType Type { get; set; }

    /// <summary>
    /// 告警级别
    /// </summary>
    public AlertLevel Level { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 指标路径（如 "Connection.ActiveConnections"）
    /// </summary>
    public string MetricPath { get; set; } = string.Empty;

    /// <summary>
    /// 比较操作符
    /// </summary>
    public ComparisonOperator Operator { get; set; }

    /// <summary>
    /// 阈值
    /// </summary>
    public double Threshold { get; set; }

    /// <summary>
    /// 评估窗口（秒）
    /// </summary>
    public int EvaluationWindowSeconds { get; set; } = 60;

    /// <summary>
    /// 触发条件（连续多少次满足条件才触发告警）
    /// </summary>
    public int TriggerCount { get; set; } = 1;

    /// <summary>
    /// 恢复条件（连续多少次不满足条件才恢复告警）
    /// </summary>
    public int RecoveryCount { get; set; } = 1;

    /// <summary>
    /// 抑制时间（秒，避免重复告警）
    /// </summary>
    public int SuppressionSeconds { get; set; } = 300;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 标签（用于分组和过滤）
    /// </summary>
    public Dictionary<string, string> Tags { get; set; } = new();
}

/// <summary>
/// 告警实例
/// </summary>
public class Alert
{
    /// <summary>
    /// 告警 ID
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 告警规则 ID
    /// </summary>
    public string RuleId { get; set; } = string.Empty;

    /// <summary>
    /// 告警规则名称
    /// </summary>
    public string RuleName { get; set; } = string.Empty;

    /// <summary>
    /// 告警级别
    /// </summary>
    public AlertLevel Level { get; set; }

    /// <summary>
    /// 告警状态
    /// </summary>
    public AlertStatus Status { get; set; } = AlertStatus.Active;

    /// <summary>
    /// 告警消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 详细描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 指标路径
    /// </summary>
    public string MetricPath { get; set; } = string.Empty;

    /// <summary>
    /// 当前值
    /// </summary>
    public double CurrentValue { get; set; }

    /// <summary>
    /// 阈值
    /// </summary>
    public double Threshold { get; set; }

    /// <summary>
    /// 首次触发时间
    /// </summary>
    public DateTime FirstTriggeredAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后触发时间
    /// </summary>
    public DateTime LastTriggeredAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 解决时间
    /// </summary>
    public DateTime? ResolvedAt { get; set; }

    /// <summary>
    /// 确认时间
    /// </summary>
    public DateTime? AcknowledgedAt { get; set; }

    /// <summary>
    /// 确认人
    /// </summary>
    public string? AcknowledgedBy { get; set; }

    /// <summary>
    /// 触发次数
    /// </summary>
    public int TriggerCount { get; set; } = 1;

    /// <summary>
    /// 标签
    /// </summary>
    public Dictionary<string, string> Tags { get; set; } = new();

    /// <summary>
    /// 附加数据
    /// </summary>
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}

/// <summary>
/// 告警统计信息
/// </summary>
public class AlertStatistics
{
    /// <summary>
    /// 统计时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 活跃告警总数
    /// </summary>
    public int ActiveAlerts { get; set; }

    /// <summary>
    /// 按级别分组的活跃告警数
    /// </summary>
    public Dictionary<AlertLevel, int> ActiveAlertsByLevel { get; set; } = new();

    /// <summary>
    /// 今日新增告警数
    /// </summary>
    public int TodayNewAlerts { get; set; }

    /// <summary>
    /// 今日解决告警数
    /// </summary>
    public int TodayResolvedAlerts { get; set; }

    /// <summary>
    /// 平均解决时间（分钟）
    /// </summary>
    public double AverageResolutionTimeMinutes { get; set; }

    /// <summary>
    /// 最频繁的告警规则
    /// </summary>
    public List<AlertRuleFrequency> MostFrequentRules { get; set; } = new();
}

/// <summary>
/// 告警规则频率统计
/// </summary>
public class AlertRuleFrequency
{
    /// <summary>
    /// 规则 ID
    /// </summary>
    public string RuleId { get; set; } = string.Empty;

    /// <summary>
    /// 规则名称
    /// </summary>
    public string RuleName { get; set; } = string.Empty;

    /// <summary>
    /// 触发次数
    /// </summary>
    public int TriggerCount { get; set; }

    /// <summary>
    /// 最后触发时间
    /// </summary>
    public DateTime LastTriggeredAt { get; set; }
}

/// <summary>
/// 告警事件
/// </summary>
public class AlertEvent
{
    /// <summary>
    /// 事件 ID
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 告警 ID
    /// </summary>
    public string AlertId { get; set; } = string.Empty;

    /// <summary>
    /// 事件类型
    /// </summary>
    public AlertEventType Type { get; set; }

    /// <summary>
    /// 事件时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 事件消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 操作人
    /// </summary>
    public string? OperatedBy { get; set; }

    /// <summary>
    /// 附加数据
    /// </summary>
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}

/// <summary>
/// 告警事件类型
/// </summary>
public enum AlertEventType
{
    /// <summary>
    /// 告警触发
    /// </summary>
    Triggered = 0,

    /// <summary>
    /// 告警解决
    /// </summary>
    Resolved = 1,

    /// <summary>
    /// 告警确认
    /// </summary>
    Acknowledged = 2,

    /// <summary>
    /// 告警抑制
    /// </summary>
    Suppressed = 3,

    /// <summary>
    /// 告警升级
    /// </summary>
    Escalated = 4
}
