using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace MqttBroker.Core.Will;

/// <summary>
/// 遗嘱消息清理后台服务
/// </summary>
public class WillMessageCleanupService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<WillMessageCleanupService> _logger;
    private readonly WillMessageOptions _options;
    private readonly Timer? _cleanupTimer;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    /// <param name="options">配置选项</param>
    /// <param name="logger">日志记录器</param>
    public WillMessageCleanupService(
        IServiceProvider serviceProvider,
        IOptions<WillMessageOptions> options,
        ILogger<WillMessageCleanupService> logger)
    {
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _logger.LogInformation("WillMessageCleanupService initialized with cleanup interval: {IntervalMinutes} minutes", 
            _options.CleanupIntervalMinutes);
    }

    /// <summary>
    /// 执行后台服务
    /// </summary>
    /// <param name="stoppingToken">停止令牌</param>
    /// <returns>执行任务</returns>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        if (!_options.EnableAutomaticCleanup)
        {
            _logger.LogInformation("Automatic cleanup is disabled, WillMessageCleanupService will not run");
            return;
        }

        _logger.LogInformation("WillMessageCleanupService started");

        try
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await PerformCleanupAsync(stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    // 正常取消，退出循环
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error during will message cleanup");
                }

                // 等待下一次清理
                try
                {
                    await Task.Delay(TimeSpan.FromMinutes(_options.CleanupIntervalMinutes), stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    // 正常取消，退出循环
                    break;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fatal error in WillMessageCleanupService");
        }
        finally
        {
            _logger.LogInformation("WillMessageCleanupService stopped");
        }
    }

    /// <summary>
    /// 执行清理操作
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理任务</returns>
    private async Task PerformCleanupAsync(CancellationToken cancellationToken)
    {
        _logger.LogDebug("Starting will message cleanup");

        using var scope = _serviceProvider.CreateScope();
        var willMessageManager = scope.ServiceProvider.GetRequiredService<IWillMessageManager>();

        try
        {
            var cleanupResult = await willMessageManager.CleanupExpiredWillMessagesAsync(cancellationToken);

            if (cleanupResult.IsSuccess)
            {
                if (cleanupResult.CleanedCount > 0)
                {
                    _logger.LogInformation("Cleaned up {Count} expired will messages in {ElapsedMs}ms", 
                        cleanupResult.CleanedCount, cleanupResult.ProcessingTimeMs);
                }
                else
                {
                    _logger.LogTrace("No expired will messages found during cleanup");
                }
            }
            else
            {
                _logger.LogWarning("Will message cleanup failed: {ErrorMessage}", cleanupResult.ErrorMessage);
            }

            // 记录统计信息
            if (_options.EnableStatistics)
            {
                await LogStatisticsAsync(willMessageManager, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing will message cleanup");
        }
    }

    /// <summary>
    /// 记录统计信息
    /// </summary>
    /// <param name="willMessageManager">遗嘱消息管理器</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>记录任务</returns>
    private async Task LogStatisticsAsync(IWillMessageManager willMessageManager, CancellationToken cancellationToken)
    {
        try
        {
            var statistics = await willMessageManager.GetStatisticsAsync();

            _logger.LogDebug("Will message statistics: Total={Total}, Active={Active}, Triggered={Triggered}, Expired={Expired}, " +
                           "Registrations={Registrations}, Triggers={Triggers}, Clears={Clears}, Failures={Failures}, " +
                           "AvgProcessingTime={AvgProcessingTime}ms",
                statistics.TotalWillMessages,
                statistics.ActiveWillMessages,
                statistics.TriggeredWillMessages,
                statistics.ExpiredWillMessages,
                statistics.TotalRegistrations,
                statistics.TotalTriggers,
                statistics.TotalClears,
                statistics.TotalFailures,
                statistics.AverageProcessingTimeMs);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error logging will message statistics");
        }
    }

    /// <summary>
    /// 停止服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping WillMessageCleanupService");

        // 执行最后一次清理
        if (_options.EnableAutomaticCleanup)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var willMessageManager = scope.ServiceProvider.GetRequiredService<IWillMessageManager>();
                
                _logger.LogInformation("Performing final will message cleanup before shutdown");
                var cleanupResult = await willMessageManager.CleanupExpiredWillMessagesAsync(cancellationToken);
                
                if (cleanupResult.IsSuccess && cleanupResult.CleanedCount > 0)
                {
                    _logger.LogInformation("Final cleanup completed: {Count} expired will messages cleaned", 
                        cleanupResult.CleanedCount);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error during final will message cleanup");
            }
        }

        await base.StopAsync(cancellationToken);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public override void Dispose()
    {
        _cleanupTimer?.Dispose();
        base.Dispose();
    }
}
