using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;

namespace MqttBroker.Core.Message;

/// <summary>
/// 消息路由引擎接口
/// </summary>
public interface IMessageRoutingEngine
{
    /// <summary>
    /// 路由消息到订阅者
    /// </summary>
    /// <param name="publishPacket">发布数据包</param>
    /// <param name="publisherClientId">发布者客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>路由结果</returns>
    Task<MessageRoutingResult> RouteMessageAsync(MqttPublishPacket publishPacket, string? publisherClientId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量路由消息
    /// </summary>
    /// <param name="messages">消息列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>路由结果列表</returns>
    Task<IList<MessageRoutingResult>> RouteMessagesAsync(IList<MessageRoutingRequest> messages, CancellationToken cancellationToken = default);

    /// <summary>
    /// 处理离线消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理结果</returns>
    Task<OfflineMessageResult> ProcessOfflineMessagesAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取路由统计信息
    /// </summary>
    /// <returns>路由统计信息</returns>
    Task<MessageRoutingStatistics> GetStatisticsAsync();

    /// <summary>
    /// 消息路由成功事件
    /// </summary>
    event EventHandler<MessageRoutedEventArgs>? MessageRouted;

    /// <summary>
    /// 消息路由失败事件
    /// </summary>
    event EventHandler<MessageRoutingFailedEventArgs>? MessageRoutingFailed;

    /// <summary>
    /// 离线消息存储事件
    /// </summary>
    event EventHandler<OfflineMessageStoredEventArgs>? OfflineMessageStored;
}

/// <summary>
/// 消息路由请求
/// </summary>
public class MessageRoutingRequest
{
    /// <summary>
    /// 发布数据包
    /// </summary>
    public MqttPublishPacket PublishPacket { get; set; } = null!;

    /// <summary>
    /// 发布者客户端ID
    /// </summary>
    public string? PublisherClientId { get; set; }

    /// <summary>
    /// 路由优先级
    /// </summary>
    public MessageRoutingPriority Priority { get; set; } = MessageRoutingPriority.Normal;

    /// <summary>
    /// 创建消息路由请求
    /// </summary>
    /// <param name="publishPacket">发布数据包</param>
    /// <param name="publisherClientId">发布者客户端ID</param>
    /// <param name="priority">路由优先级</param>
    /// <returns>消息路由请求</returns>
    public static MessageRoutingRequest Create(MqttPublishPacket publishPacket, string? publisherClientId = null, MessageRoutingPriority priority = MessageRoutingPriority.Normal)
    {
        return new MessageRoutingRequest
        {
            PublishPacket = publishPacket,
            PublisherClientId = publisherClientId,
            Priority = priority
        };
    }
}

/// <summary>
/// 消息路由优先级
/// </summary>
public enum MessageRoutingPriority
{
    /// <summary>
    /// 低优先级
    /// </summary>
    Low = 0,

    /// <summary>
    /// 正常优先级
    /// </summary>
    Normal = 1,

    /// <summary>
    /// 高优先级
    /// </summary>
    High = 2,

    /// <summary>
    /// 紧急优先级
    /// </summary>
    Critical = 3
}

/// <summary>
/// 消息路由结果
/// </summary>
public class MessageRoutingResult
{
    /// <summary>
    /// 主题名称
    /// </summary>
    public string TopicName { get; set; } = string.Empty;

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 在线分发数量
    /// </summary>
    public int OnlineDeliveries { get; set; }

    /// <summary>
    /// 离线存储数量
    /// </summary>
    public int OfflineStorages { get; set; }

    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailedDeliveries { get; set; }

    /// <summary>
    /// 过滤掉的数量
    /// </summary>
    public int FilteredCount { get; set; }

    /// <summary>
    /// 路由耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 路由详情
    /// </summary>
    public IList<SubscriberRoutingResult> RoutingDetails { get; set; } = new List<SubscriberRoutingResult>();

    /// <summary>
    /// 创建成功的路由结果
    /// </summary>
    /// <param name="topicName">主题名称</param>
    /// <param name="onlineDeliveries">在线分发数量</param>
    /// <param name="offlineStorages">离线存储数量</param>
    /// <param name="failedDeliveries">失败数量</param>
    /// <param name="filteredCount">过滤数量</param>
    /// <param name="elapsedMilliseconds">耗时</param>
    /// <returns>路由结果</returns>
    public static MessageRoutingResult Success(string topicName, int onlineDeliveries, int offlineStorages, int failedDeliveries, int filteredCount, long elapsedMilliseconds)
    {
        return new MessageRoutingResult
        {
            TopicName = topicName,
            IsSuccess = true,
            OnlineDeliveries = onlineDeliveries,
            OfflineStorages = offlineStorages,
            FailedDeliveries = failedDeliveries,
            FilteredCount = filteredCount,
            ElapsedMilliseconds = elapsedMilliseconds
        };
    }

    /// <summary>
    /// 创建失败的路由结果
    /// </summary>
    /// <param name="topicName">主题名称</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>路由结果</returns>
    public static MessageRoutingResult Failure(string topicName, string errorMessage)
    {
        return new MessageRoutingResult
        {
            TopicName = topicName,
            IsSuccess = false,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// 订阅者路由结果
/// </summary>
public class SubscriberRoutingResult
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 主题过滤器
    /// </summary>
    public string TopicFilter { get; set; } = string.Empty;

    /// <summary>
    /// 路由类型
    /// </summary>
    public MessageRoutingType RoutingType { get; set; }

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 使用的QoS级别
    /// </summary>
    public MqttQoSLevel QoSLevel { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 路由耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }
}

/// <summary>
/// 消息路由类型
/// </summary>
public enum MessageRoutingType
{
    /// <summary>
    /// 在线分发
    /// </summary>
    OnlineDelivery,

    /// <summary>
    /// 离线存储
    /// </summary>
    OfflineStorage,

    /// <summary>
    /// 被过滤
    /// </summary>
    Filtered,

    /// <summary>
    /// 失败
    /// </summary>
    Failed
}

/// <summary>
/// 离线消息处理结果
/// </summary>
public class OfflineMessageResult
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 处理的消息数量
    /// </summary>
    public int ProcessedMessages { get; set; }

    /// <summary>
    /// 成功分发的消息数量
    /// </summary>
    public int DeliveredMessages { get; set; }

    /// <summary>
    /// 失败的消息数量
    /// </summary>
    public int FailedMessages { get; set; }

    /// <summary>
    /// 处理耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 消息路由统计信息
/// </summary>
public class MessageRoutingStatistics
{
    /// <summary>
    /// 总路由消息数
    /// </summary>
    public long TotalMessages { get; set; }

    /// <summary>
    /// 在线分发数
    /// </summary>
    public long OnlineDeliveries { get; set; }

    /// <summary>
    /// 离线存储数
    /// </summary>
    public long OfflineStorages { get; set; }

    /// <summary>
    /// 失败路由数
    /// </summary>
    public long FailedRoutings { get; set; }

    /// <summary>
    /// 过滤消息数
    /// </summary>
    public long FilteredMessages { get; set; }

    /// <summary>
    /// 平均路由延迟（毫秒）
    /// </summary>
    public double AverageRoutingLatency { get; set; }

    /// <summary>
    /// 最大路由延迟（毫秒）
    /// </summary>
    public long MaxRoutingLatency { get; set; }

    /// <summary>
    /// 每秒路由消息数
    /// </summary>
    public double MessagesPerSecond { get; set; }

    /// <summary>
    /// 死信队列消息数
    /// </summary>
    public long DeadLetterMessages { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate => TotalMessages > 0 ? (double)(OnlineDeliveries + OfflineStorages) / TotalMessages * 100 : 0;
}
