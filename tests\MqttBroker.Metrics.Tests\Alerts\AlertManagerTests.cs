using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using MqttBroker.Metrics.Alerts;
using MqttBroker.Metrics.Configuration;
using MqttBroker.Metrics.Models;
using Xunit;

namespace MqttBroker.Metrics.Tests.Alerts;

/// <summary>
/// 告警管理器测试
/// </summary>
public class AlertManagerTests
{
    private readonly Mock<IAlertStorage> _mockStorage;
    private readonly Mock<IAlertRuleEvaluator> _mockEvaluator;
    private readonly Mock<ILogger<AlertManager>> _mockLogger;
    private readonly IOptions<AlertOptions> _options;

    public AlertManagerTests()
    {
        _mockStorage = new Mock<IAlertStorage>();
        _mockEvaluator = new Mock<IAlertRuleEvaluator>();
        _mockLogger = new Mock<ILogger<AlertManager>>();

        _options = Options.Create(new AlertOptions
        {
            EnableAlerts = true,
            EvaluationIntervalSeconds = 30,
            MaxActiveAlerts = 100
        });
    }

    [Fact]
    public async Task AddRuleAsync_ShouldStoreRule()
    {
        // Arrange
        var alertManager = new AlertManager(
            _mockStorage.Object,
            new[] { _mockEvaluator.Object },
            _mockLogger.Object,
            _options);

        var rule = new AlertRule
        {
            Name = "Test Rule",
            MetricPath = "Connection.ActiveConnections",
            Operator = ComparisonOperator.GreaterThan,
            Threshold = 100
        };

        // Act
        await alertManager.AddRuleAsync(rule);

        // Assert
        _mockStorage.Verify(x => x.StoreRuleAsync(rule, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task EvaluateMetricsAsync_WhenRuleTriggered_ShouldCreateAlert()
    {
        // Arrange
        var rule = new AlertRule
        {
            Id = "rule1",
            Name = "Test Rule",
            MetricPath = "Connection.ActiveConnections",
            Operator = ComparisonOperator.GreaterThan,
            Threshold = 100,
            IsEnabled = true
        };

        var metrics = new PerformanceMetrics
        {
            Connection = new ConnectionMetrics { ActiveConnections = 150 }
        };

        var evaluationResult = new AlertEvaluationResult
        {
            IsTriggered = true,
            CurrentValue = 150,
            Threshold = 100,
            Message = "Connection count exceeded threshold"
        };

        _mockStorage.Setup(x => x.GetRulesAsync(It.IsAny<CancellationToken>()))
                   .ReturnsAsync(new[] { rule });

        _mockEvaluator.Setup(x => x.SupportsRule(rule)).Returns(true);
        _mockEvaluator.Setup(x => x.EvaluateRule(rule, metrics)).Returns(evaluationResult);

        var alertManager = new AlertManager(
            _mockStorage.Object,
            new[] { _mockEvaluator.Object },
            _mockLogger.Object,
            _options);

        bool alertTriggered = false;
        alertManager.AlertTriggered += (sender, args) => alertTriggered = true;

        // 等待规则加载
        await Task.Delay(100);

        // Act
        await alertManager.EvaluateMetricsAsync(metrics);

        // Assert
        Assert.True(alertTriggered);
        _mockStorage.Verify(x => x.StoreAlertAsync(It.IsAny<Alert>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task AcknowledgeAlertAsync_ShouldUpdateAlertStatus()
    {
        // Arrange
        var alert = new Alert
        {
            Id = "alert1",
            Status = AlertStatus.Active
        };

        var alertManager = new AlertManager(
            _mockStorage.Object,
            new[] { _mockEvaluator.Object },
            _mockLogger.Object,
            _options);

        // 手动添加活跃告警
        var activeAlertsField = typeof(AlertManager).GetField("_activeAlerts", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var activeAlerts = activeAlertsField?.GetValue(alertManager) as System.Collections.Concurrent.ConcurrentDictionary<string, Alert>;
        activeAlerts?.TryAdd(alert.Id, alert);

        // Act
        await alertManager.AcknowledgeAlertAsync(alert.Id, "admin");

        // Assert
        Assert.Equal(AlertStatus.Acknowledged, alert.Status);
        Assert.Equal("admin", alert.AcknowledgedBy);
        Assert.NotNull(alert.AcknowledgedAt);
        _mockStorage.Verify(x => x.UpdateAlertAsync(alert, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetActiveAlertsAsync_ShouldReturnActiveAlerts()
    {
        // Arrange
        var alertManager = new AlertManager(
            _mockStorage.Object,
            new[] { _mockEvaluator.Object },
            _mockLogger.Object,
            _options);

        // Act
        var activeAlerts = await alertManager.GetActiveAlertsAsync();

        // Assert
        Assert.NotNull(activeAlerts);
        Assert.Empty(activeAlerts); // 初始状态应该没有活跃告警
    }
}

/// <summary>
/// 阈值告警规则评估器测试
/// </summary>
public class ThresholdAlertRuleEvaluatorTests
{
    private readonly Mock<ILogger<ThresholdAlertRuleEvaluator>> _mockLogger;
    private readonly ThresholdAlertRuleEvaluator _evaluator;

    public ThresholdAlertRuleEvaluatorTests()
    {
        _mockLogger = new Mock<ILogger<ThresholdAlertRuleEvaluator>>();
        _evaluator = new ThresholdAlertRuleEvaluator(_mockLogger.Object);
    }

    [Fact]
    public void SupportsRule_WithThresholdRule_ShouldReturnTrue()
    {
        // Arrange
        var rule = new AlertRule { Type = AlertRuleType.Threshold };

        // Act
        var result = _evaluator.SupportsRule(rule);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void SupportsRule_WithNonThresholdRule_ShouldReturnFalse()
    {
        // Arrange
        var rule = new AlertRule { Type = AlertRuleType.ChangeRate };

        // Act
        var result = _evaluator.SupportsRule(rule);

        // Assert
        Assert.False(result);
    }

    [Theory]
    [InlineData(ComparisonOperator.GreaterThan, 150, 100, true)]
    [InlineData(ComparisonOperator.GreaterThan, 50, 100, false)]
    [InlineData(ComparisonOperator.LessThan, 50, 100, true)]
    [InlineData(ComparisonOperator.LessThan, 150, 100, false)]
    [InlineData(ComparisonOperator.Equal, 100, 100, true)]
    [InlineData(ComparisonOperator.Equal, 99, 100, false)]
    public void EvaluateRule_WithDifferentOperators_ShouldReturnCorrectResult(
        ComparisonOperator op, double currentValue, double threshold, bool expectedTriggered)
    {
        // Arrange
        var rule = new AlertRule
        {
            Name = "Test Rule",
            MetricPath = "Connection.ActiveConnections",
            Operator = op,
            Threshold = threshold
        };

        var metrics = new PerformanceMetrics
        {
            Connection = new ConnectionMetrics { ActiveConnections = (long)currentValue }
        };

        // Act
        var result = _evaluator.EvaluateRule(rule, metrics);

        // Assert
        Assert.Equal(expectedTriggered, result.IsTriggered);
        Assert.Equal(currentValue, result.CurrentValue);
        Assert.Equal(threshold, result.Threshold);
    }

    [Fact]
    public void EvaluateRule_WithInvalidMetricPath_ShouldHandleGracefully()
    {
        // Arrange
        var rule = new AlertRule
        {
            Name = "Test Rule",
            MetricPath = "Invalid.Path",
            Operator = ComparisonOperator.GreaterThan,
            Threshold = 100
        };

        var metrics = new PerformanceMetrics();

        // Act
        var result = _evaluator.EvaluateRule(rule, metrics);

        // Assert
        Assert.False(result.IsTriggered);
        Assert.Contains("评估规则时发生错误", result.Message);
    }

    [Fact]
    public void EvaluateRule_WithNestedMetricPath_ShouldWork()
    {
        // Arrange
        var rule = new AlertRule
        {
            Name = "Test Rule",
            MetricPath = "Message.RoutingLatency.Average",
            Operator = ComparisonOperator.GreaterThan,
            Threshold = 5.0
        };

        var metrics = new PerformanceMetrics
        {
            Message = new MessageMetrics
            {
                RoutingLatency = new MessageLatencyMetrics { Average = 10.5 }
            }
        };

        // Act
        var result = _evaluator.EvaluateRule(rule, metrics);

        // Assert
        Assert.True(result.IsTriggered);
        Assert.Equal(10.5, result.CurrentValue);
        Assert.Equal(5.0, result.Threshold);
    }
}
