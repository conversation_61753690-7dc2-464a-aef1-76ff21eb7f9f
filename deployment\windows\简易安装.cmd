@echo off
REM MQTT Broker 简易安装脚本 (中文版)
REM 专为解决路径和命令问题设计

setlocal enabledelayedexpansion

REM 设置控制台为中文显示
chcp 936 >nul

echo ==========================================
echo        MQTT Broker 一键安装工具
echo ==========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 【错误】需要管理员权限运行
    echo.
    echo 解决方法：
    echo 1. 按 Win+X 键
    echo 2. 选择"命令提示符(管理员)"或"Windows PowerShell(管理员)"
    echo 3. 重新运行此脚本
    echo.
    pause
    exit /b 1
)

echo 【检查】管理员权限 - 通过
echo.

REM 获取脚本路径
set "CURRENT_DIR=%cd%"
set "SCRIPT_PATH=%~dp0"

echo 【信息】当前目录: %CURRENT_DIR%
echo 【信息】脚本位置: %SCRIPT_PATH%
echo.

REM 检查 .NET 运行时
echo 【检查】.NET 8 运行时...
dotnet --version >nul 2>&1
if %errorLevel% neq 0 (
    echo 【警告】未检测到 .NET 运行时
    echo.
    echo 正在尝试安装 .NET 8 运行时...
    echo 请稍候，这可能需要几分钟...
    
    REM 下载并安装 .NET 8
    powershell -Command "try { Invoke-WebRequest -Uri 'https://download.microsoft.com/download/8/4/f/84f2e1e6-e1e1-4e1e-8e1e-1e1e1e1e1e1e/dotnet-runtime-8.0.0-win-x64.exe' -OutFile '%TEMP%\dotnet8.exe' -UseBasicParsing; Start-Process '%TEMP%\dotnet8.exe' -ArgumentList '/quiet' -Wait; Remove-Item '%TEMP%\dotnet8.exe' } catch { Write-Host '下载失败，请手动安装' }"
    
    REM 再次检查
    dotnet --version >nul 2>&1
    if %errorLevel% neq 0 (
        echo 【错误】.NET 8 安装失败
        echo.
        echo 请手动下载安装 .NET 8 运行时：
        echo https://dotnet.microsoft.com/download/dotnet/8.0
        echo.
        pause
        exit /b 1
    )
)

for /f "tokens=1 delims=." %%a in ('dotnet --version') do set "DOTNET_MAJOR=%%a"
echo 【检查】.NET 版本: %DOTNET_MAJOR% - 通过
echo.

REM 设置安装参数
set "INSTALL_PATH=C:\MqttBroker"
set "PROJECT_SRC=%SCRIPT_PATH%..\..\src\MqttBroker.Host"

REM 检查项目源码
if not exist "%PROJECT_SRC%" (
    echo 【错误】未找到项目源代码
    echo 期望位置: %PROJECT_SRC%
    echo.
    echo 请确保：
    echo 1. 在正确的项目目录中运行
    echo 2. 项目结构完整
    echo.
    pause
    exit /b 1
)

echo 【检查】项目源码 - 通过
echo.

REM 创建安装目录
echo 【操作】创建安装目录...
if exist "%INSTALL_PATH%" (
    echo 【警告】安装目录已存在，将清理旧文件
    rmdir /s /q "%INSTALL_PATH%\app" 2>nul
)

mkdir "%INSTALL_PATH%" 2>nul
mkdir "%INSTALL_PATH%\app" 2>nul
mkdir "%INSTALL_PATH%\data" 2>nul
mkdir "%INSTALL_PATH%\logs" 2>nul
mkdir "%INSTALL_PATH%\scripts" 2>nul

echo 【完成】目录创建完成
echo.

REM 构建项目
echo 【操作】构建 MQTT Broker...
cd /d "%SCRIPT_PATH%..\.."

echo   - 清理项目...
dotnet clean --configuration Release >nul 2>&1

echo   - 还原依赖...
dotnet restore >nul 2>&1
if %errorLevel% neq 0 (
    echo 【错误】依赖还原失败
    pause
    exit /b 1
)

echo   - 发布应用...
dotnet publish src/MqttBroker.Host -c Release -o "%INSTALL_PATH%\app" --self-contained false --runtime win-x64 >nul 2>&1
if %errorLevel% neq 0 (
    echo 【错误】应用发布失败
    pause
    exit /b 1
)

echo 【完成】应用构建完成
echo.

REM 创建配置文件
echo 【操作】创建配置文件...
(
echo {
echo   "Logging": {
echo     "LogLevel": {
echo       "Default": "Information",
echo       "Microsoft": "Warning"
echo     },
echo     "Console": { "IncludeScopes": true },
echo     "File": {
echo       "Path": "C:\\MqttBroker\\logs\\mqtt-broker-.log",
echo       "RollingInterval": "Day",
echo       "RetainedFileCountLimit": 7
echo     }
echo   },
echo   "MqttBroker": {
echo     "Network": {
echo       "Tcp": { "Enabled": true, "Port": 1883, "Address": "127.0.0.1" },
echo       "WebSocket": { "Enabled": true, "Port": 8080 }
echo     },
echo     "Storage": {
echo       "Provider": "SQLite",
echo       "ConnectionString": "Data Source=C:\\MqttBroker\\data\\mqtt_broker.db",
echo       "EnablePersistence": true
echo     },
echo     "Security": {
echo       "AllowAnonymous": true,
echo       "RequireAuthentication": false
echo     },
echo     "HealthChecks": {
echo       "Enabled": true,
echo       "Port": 9090,
echo       "Path": "/health"
echo     }
echo   }
echo }
) > "%INSTALL_PATH%\app\appsettings.json"

echo 【完成】配置文件创建完成
echo.

REM 创建管理脚本
echo 【操作】创建管理脚本...

REM 启动脚本
(
echo @echo off
echo echo 启动 MQTT Broker...
echo cd /d "C:\MqttBroker\app"
echo start "MQTT Broker" MqttBroker.Host.exe
echo timeout /t 3 /nobreak ^>nul
echo echo.
echo echo MQTT Broker 已启动！
echo echo.
echo echo 访问地址:
echo echo   MQTT TCP: localhost:1883
echo echo   WebSocket: ws://localhost:8080/mqtt
echo echo   健康检查: http://localhost:9090/health
echo echo.
echo pause
) > "%INSTALL_PATH%\scripts\启动.cmd"

REM 停止脚本
(
echo @echo off
echo echo 停止 MQTT Broker...
echo taskkill /f /im MqttBroker.Host.exe ^>nul 2^>^&1
echo if %%errorLevel%% equ 0 (
echo     echo MQTT Broker 已停止
echo ^) else (
echo     echo MQTT Broker 未在运行
echo ^)
echo pause
) > "%INSTALL_PATH%\scripts\停止.cmd"

REM 状态脚本
(
echo @echo off
echo echo 检查 MQTT Broker 状态...
echo echo.
echo tasklist /fi "imagename eq MqttBroker.Host.exe" ^| find /i "MqttBroker.Host.exe" ^>nul
echo if %%errorLevel%% equ 0 (
echo     echo [状态] 正在运行
echo     echo [端口] 检查中...
echo     netstat -an ^| find ":1883 " ^| find "LISTENING" ^>nul ^&^& echo   MQTT: 监听中 ^|^| echo   MQTT: 未监听
echo     netstat -an ^| find ":8080 " ^| find "LISTENING" ^>nul ^&^& echo   WebSocket: 监听中 ^|^| echo   WebSocket: 未监听
echo     netstat -an ^| find ":9090 " ^| find "LISTENING" ^>nul ^&^& echo   管理: 监听中 ^|^| echo   管理: 未监听
echo ^) else (
echo     echo [状态] 未运行
echo ^)
echo echo.
echo pause
) > "%INSTALL_PATH%\scripts\状态.cmd"

echo 【完成】管理脚本创建完成
echo.

REM 安装完成
echo ==========================================
echo           安装成功完成！
echo ==========================================
echo.
echo 安装位置: %INSTALL_PATH%
echo.
echo 管理脚本:
echo   启动服务: %INSTALL_PATH%\scripts\启动.cmd
echo   停止服务: %INSTALL_PATH%\scripts\停止.cmd
echo   查看状态: %INSTALL_PATH%\scripts\状态.cmd
echo.
echo 访问地址:
echo   MQTT TCP: localhost:1883
echo   WebSocket: ws://localhost:8080/mqtt
echo   健康检查: http://localhost:9090/health
echo.

REM 询问是否立即启动
set /p "START_NOW=是否立即启动 MQTT Broker? (Y/n): "
if /i not "%START_NOW%"=="n" (
    echo.
    echo 正在启动...
    call "%INSTALL_PATH%\scripts\启动.cmd"
) else (
    echo.
    echo 安装完成！您可以稍后手动启动服务。
    pause
)

endlocal
