using MqttBroker.Core.Session;
using MqttBroker.Core.QoS;

namespace MqttBroker.Storage.Repositories;

/// <summary>
/// 未确认消息仓储接口
/// </summary>
public interface IPendingMessageRepository
{
    /// <summary>
    /// 保存未确认消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="pendingMessage">未确认消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<SessionOperationResult> SavePendingMessageAsync(string clientId, PendingMessage pendingMessage, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取客户端未确认消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>未确认消息列表</returns>
    Task<IEnumerable<PendingMessage>> GetPendingMessagesAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除未确认消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="messageId">消息ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<SessionOperationResult> DeletePendingMessageAsync(string clientId, ushort messageId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除客户端所有未确认消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<SessionOperationResult> DeleteAllPendingMessagesAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新消息重传信息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="messageId">消息ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<SessionOperationResult> UpdateRetransmissionInfoAsync(string clientId, ushort messageId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取过期的未确认消息
    /// </summary>
    /// <param name="expiredBefore">过期时间点</param>
    /// <param name="limit">限制数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>过期消息列表</returns>
    Task<IEnumerable<PendingMessage>> GetExpiredPendingMessagesAsync(DateTime expiredBefore, int limit = 100, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量删除过期消息
    /// </summary>
    /// <param name="expiredBefore">过期时间点</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除的消息数量</returns>
    Task<int> DeleteExpiredPendingMessagesAsync(DateTime expiredBefore, CancellationToken cancellationToken = default);
}
