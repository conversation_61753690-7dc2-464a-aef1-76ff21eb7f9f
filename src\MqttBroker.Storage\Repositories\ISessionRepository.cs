using MqttBroker.Core.Session;
using MqttBroker.Core.Topic;
using MqttBroker.Core.QoS;

namespace MqttBroker.Storage.Repositories;

/// <summary>
/// 会话仓储接口
/// </summary>
public interface ISessionRepository
{
    /// <summary>
    /// 创建或更新会话
    /// </summary>
    /// <param name="session">会话信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<SessionOperationResult> CreateOrUpdateAsync(MqttSession session, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取会话
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>会话信息</returns>
    Task<MqttSession?> GetAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除会话
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<SessionOperationResult> DeleteAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查会话是否存在
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    Task<bool> ExistsAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新会话活动时间
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="lastActivity">最后活动时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<SessionOperationResult> UpdateActivityAsync(string clientId, DateTime lastActivity, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取活跃会话
    /// </summary>
    /// <param name="limit">限制数量</param>
    /// <param name="offset">偏移量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>会话列表</returns>
    Task<IEnumerable<MqttSession>> GetActiveSessionsAsync(int limit = 100, int offset = 0, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取过期会话
    /// </summary>
    /// <param name="expiredBefore">过期时间点</param>
    /// <param name="limit">限制数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>过期会话列表</returns>
    Task<IEnumerable<MqttSession>> GetExpiredSessionsAsync(DateTime expiredBefore, int limit = 100, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量删除会话
    /// </summary>
    /// <param name="clientIds">客户端ID列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>批量操作结果</returns>
    Task<BatchSessionOperationResult> DeleteBatchAsync(IEnumerable<string> clientIds, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取会话统计信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>统计信息</returns>
    Task<SessionStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default);
}


