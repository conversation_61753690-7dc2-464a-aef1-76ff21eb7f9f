using System.Collections.Concurrent;
using System.Diagnostics;
using Microsoft.Extensions.Logging;

namespace MqttBroker.Core.Message;

/// <summary>
/// 消息过滤器管理器实现
/// </summary>
public class MessageFilterManager : IMessageFilterManager
{
    private readonly ILogger<MessageFilterManager> _logger;
    private readonly ConcurrentDictionary<string, IMessageFilter> _filters;
    private readonly object _statsLock = new();
    private MessageFilterStatistics _statistics;

    /// <summary>
    /// 初始化消息过滤器管理器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public MessageFilterManager(ILogger<MessageFilterManager> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _filters = new ConcurrentDictionary<string, IMessageFilter>();
        _statistics = new MessageFilterStatistics();
    }

    /// <summary>
    /// 注册过滤器
    /// </summary>
    /// <param name="filter">过滤器</param>
    public void RegisterFilter(IMessageFilter filter)
    {
        if (filter == null)
            throw new ArgumentNullException(nameof(filter));

        if (string.IsNullOrWhiteSpace(filter.Name))
            throw new ArgumentException("Filter name cannot be null or empty", nameof(filter));

        _filters.AddOrUpdate(filter.Name, filter, (key, oldValue) => filter);
        
        lock (_statsLock)
        {
            if (!_statistics.FilterStats.ContainsKey(filter.Name))
            {
                _statistics.FilterStats[filter.Name] = new FilterStatistics
                {
                    Name = filter.Name,
                    IsEnabled = filter.IsEnabled
                };
            }
        }

        _logger.LogInformation("Registered message filter: {FilterName} with priority {Priority}", 
            filter.Name, filter.Priority);
    }

    /// <summary>
    /// 注销过滤器
    /// </summary>
    /// <param name="filterName">过滤器名称</param>
    /// <returns>是否成功注销</returns>
    public bool UnregisterFilter(string filterName)
    {
        if (string.IsNullOrWhiteSpace(filterName))
            return false;

        var removed = _filters.TryRemove(filterName, out var filter);
        
        if (removed)
        {
            _logger.LogInformation("Unregistered message filter: {FilterName}", filterName);
        }

        return removed;
    }

    /// <summary>
    /// 获取所有过滤器
    /// </summary>
    /// <returns>过滤器列表</returns>
    public IList<IMessageFilter> GetFilters()
    {
        return _filters.Values
            .OrderBy(f => f.Priority)
            .ThenBy(f => f.Name)
            .ToList();
    }

    /// <summary>
    /// 应用过滤器链
    /// </summary>
    /// <param name="context">过滤上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>最终过滤结果</returns>
    public async Task<MessageFilterResult> ApplyFiltersAsync(MessageFilterContext context, CancellationToken cancellationToken = default)
    {
        if (context == null)
            throw new ArgumentNullException(nameof(context));

        var stopwatch = Stopwatch.StartNew();
        var enabledFilters = GetFilters().Where(f => f.IsEnabled).ToList();

        if (enabledFilters.Count == 0)
        {
            _logger.LogTrace("No enabled filters found, allowing message through");
            return MessageFilterResult.Allow();
        }

        _logger.LogTrace("Applying {FilterCount} filters to message for topic: {TopicName}", 
            enabledFilters.Count, context.PublishPacket.Topic);

        var currentPacket = context.PublishPacket;
        var combinedProperties = new Dictionary<string, object>(context.Properties);

        foreach (var filter in enabledFilters)
        {
            try
            {
                var filterStopwatch = Stopwatch.StartNew();
                
                // 更新上下文中的数据包（可能被前面的过滤器修改过）
                context.PublishPacket = currentPacket;
                context.Properties = combinedProperties;

                var result = await filter.FilterAsync(context, cancellationToken);
                filterStopwatch.Stop();

                // 更新过滤器统计信息
                UpdateFilterStatistics(filter.Name, result.IsAllowed, filterStopwatch.ElapsedMilliseconds);

                if (!result.IsAllowed)
                {
                    stopwatch.Stop();
                    _logger.LogDebug("Message filtered out by filter: {FilterName}, reason: {Reason}", 
                        filter.Name, result.Reason);

                    var finalResult = MessageFilterResult.Deny(result.Reason ?? "Filtered by " + filter.Name);
                    finalResult.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
                    
                    UpdateGlobalStatistics(false, stopwatch.ElapsedMilliseconds);
                    return finalResult;
                }

                // 如果过滤器修改了消息，使用修改后的版本
                if (result.ModifiedPacket != null)
                {
                    currentPacket = result.ModifiedPacket;
                    _logger.LogTrace("Message modified by filter: {FilterName}", filter.Name);
                }

                // 合并过滤器属性
                foreach (var prop in result.Properties)
                {
                    combinedProperties[prop.Key] = prop.Value;
                }

                _logger.LogTrace("Message passed filter: {FilterName} in {ElapsedMs}ms", 
                    filter.Name, filterStopwatch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error applying filter: {FilterName}", filter.Name);
                
                // 过滤器出错时，根据配置决定是否允许消息通过
                // 这里默认允许通过，避免因过滤器错误导致消息丢失
                UpdateFilterStatistics(filter.Name, true, 0);
                continue;
            }
        }

        stopwatch.Stop();

        var allowResult = MessageFilterResult.Allow(currentPacket != context.PublishPacket ? currentPacket : null);
        allowResult.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
        allowResult.Properties = combinedProperties;

        UpdateGlobalStatistics(true, stopwatch.ElapsedMilliseconds);

        _logger.LogTrace("Message passed all {FilterCount} filters in {ElapsedMs}ms", 
            enabledFilters.Count, stopwatch.ElapsedMilliseconds);

        return allowResult;
    }

    /// <summary>
    /// 获取过滤器统计信息
    /// </summary>
    /// <returns>过滤器统计信息</returns>
    public Task<MessageFilterStatistics> GetStatisticsAsync()
    {
        lock (_statsLock)
        {
            // 创建统计信息的副本
            var stats = new MessageFilterStatistics
            {
                TotalFilters = _statistics.TotalFilters,
                AllowedMessages = _statistics.AllowedMessages,
                DeniedMessages = _statistics.DeniedMessages,
                AverageFilterLatency = _statistics.AverageFilterLatency,
                MaxFilterLatency = _statistics.MaxFilterLatency,
                FilterStats = new Dictionary<string, FilterStatistics>()
            };

            // 复制每个过滤器的统计信息
            foreach (var kvp in _statistics.FilterStats)
            {
                stats.FilterStats[kvp.Key] = new FilterStatistics
                {
                    Name = kvp.Value.Name,
                    ExecutionCount = kvp.Value.ExecutionCount,
                    AllowedCount = kvp.Value.AllowedCount,
                    DeniedCount = kvp.Value.DeniedCount,
                    AverageExecutionTime = kvp.Value.AverageExecutionTime,
                    MaxExecutionTime = kvp.Value.MaxExecutionTime,
                    IsEnabled = kvp.Value.IsEnabled
                };
            }

            return Task.FromResult(stats);
        }
    }

    #region 私有方法

    /// <summary>
    /// 更新过滤器统计信息
    /// </summary>
    /// <param name="filterName">过滤器名称</param>
    /// <param name="isAllowed">是否允许通过</param>
    /// <param name="elapsedMilliseconds">执行时间</param>
    private void UpdateFilterStatistics(string filterName, bool isAllowed, long elapsedMilliseconds)
    {
        lock (_statsLock)
        {
            if (!_statistics.FilterStats.TryGetValue(filterName, out var filterStats))
            {
                filterStats = new FilterStatistics { Name = filterName };
                _statistics.FilterStats[filterName] = filterStats;
            }

            filterStats.ExecutionCount++;
            
            if (isAllowed)
            {
                filterStats.AllowedCount++;
            }
            else
            {
                filterStats.DeniedCount++;
            }

            // 更新执行时间统计
            if (elapsedMilliseconds > filterStats.MaxExecutionTime)
            {
                filterStats.MaxExecutionTime = elapsedMilliseconds;
            }

            filterStats.AverageExecutionTime = (filterStats.AverageExecutionTime * (filterStats.ExecutionCount - 1) + elapsedMilliseconds) / filterStats.ExecutionCount;
        }
    }

    /// <summary>
    /// 更新全局统计信息
    /// </summary>
    /// <param name="isAllowed">是否允许通过</param>
    /// <param name="elapsedMilliseconds">执行时间</param>
    private void UpdateGlobalStatistics(bool isAllowed, long elapsedMilliseconds)
    {
        lock (_statsLock)
        {
            _statistics.TotalFilters++;
            
            if (isAllowed)
            {
                _statistics.AllowedMessages++;
            }
            else
            {
                _statistics.DeniedMessages++;
            }

            // 更新延迟统计
            if (elapsedMilliseconds > _statistics.MaxFilterLatency)
            {
                _statistics.MaxFilterLatency = elapsedMilliseconds;
            }

            _statistics.AverageFilterLatency = (_statistics.AverageFilterLatency * (_statistics.TotalFilters - 1) + elapsedMilliseconds) / _statistics.TotalFilters;
        }
    }

    #endregion
}
