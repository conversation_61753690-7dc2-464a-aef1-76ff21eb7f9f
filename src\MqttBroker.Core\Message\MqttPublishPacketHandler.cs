using Microsoft.Extensions.Logging;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;

namespace MqttBroker.Core.Message;

/// <summary>
/// MQTT PUBLISH 数据包处理器
/// </summary>
public class MqttPublishPacketHandler : IMessagePacketHandler
{
    private readonly IMessageRoutingEngine _routingEngine;
    private readonly ILogger<MqttPublishPacketHandler> _logger;

    /// <summary>
    /// 支持的数据包类型
    /// </summary>
    public MqttPacketType PacketType => MqttPacketType.Publish;

    /// <summary>
    /// 初始化PUBLISH数据包处理器
    /// </summary>
    /// <param name="routingEngine">消息路由引擎</param>
    /// <param name="logger">日志记录器</param>
    public MqttPublishPacketHandler(
        IMessageRoutingEngine routingEngine,
        ILogger<MqttPublishPacketHandler> logger)
    {
        _routingEngine = routingEngine ?? throw new ArgumentNullException(nameof(routingEngine));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 处理PUBLISH数据包
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="packet">数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    public async Task HandleAsync(IClientConnection connection, IMqttPacket packet, CancellationToken cancellationToken = default)
    {
        if (packet is not MqttPublishPacket publishPacket)
        {
            _logger.LogWarning("Received non-PUBLISH packet in PUBLISH handler from client: {ClientId}", 
                connection.ClientId);
            return;
        }

        try
        {
            _logger.LogDebug("Processing PUBLISH packet from client: {ClientId}, topic: {Topic}, QoS: {QoS}", 
                connection.ClientId, publishPacket.Topic, publishPacket.QoSLevel);

            // 验证主题名称
            if (string.IsNullOrWhiteSpace(publishPacket.Topic))
            {
                _logger.LogWarning("Received PUBLISH packet with empty topic from client: {ClientId}", 
                    connection.ClientId);
                
                await SendPublishResponseAsync(connection, publishPacket, MqttReasonCode.TopicNameInvalid, cancellationToken);
                return;
            }

            // 验证主题名称格式
            if (!IsValidTopicName(publishPacket.Topic))
            {
                _logger.LogWarning("Received PUBLISH packet with invalid topic '{Topic}' from client: {ClientId}", 
                    publishPacket.Topic, connection.ClientId);
                
                await SendPublishResponseAsync(connection, publishPacket, MqttReasonCode.TopicNameInvalid, cancellationToken);
                return;
            }

            // 路由消息
            var routingResult = await _routingEngine.RouteMessageAsync(publishPacket, connection.ClientId, cancellationToken);

            if (routingResult.IsSuccess)
            {
                _logger.LogDebug("Message routed successfully for topic: {Topic}. Online: {OnlineCount}, Offline: {OfflineCount}, Failed: {FailedCount}", 
                    publishPacket.Topic, routingResult.OnlineDeliveries, routingResult.OfflineStorages, routingResult.FailedDeliveries);

                // 根据QoS级别发送响应
                await SendPublishResponseAsync(connection, publishPacket, MqttReasonCode.Success, cancellationToken);
            }
            else
            {
                _logger.LogWarning("Message routing failed for topic: {Topic}, error: {Error}", 
                    publishPacket.Topic, routingResult.ErrorMessage);

                await SendPublishResponseAsync(connection, publishPacket, MqttReasonCode.UnspecifiedError, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing PUBLISH packet from client: {ClientId}", connection.ClientId);
            
            try
            {
                await SendPublishResponseAsync(connection, publishPacket, MqttReasonCode.UnspecifiedError, cancellationToken);
            }
            catch (Exception responseEx)
            {
                _logger.LogError(responseEx, "Error sending PUBLISH response to client: {ClientId}", connection.ClientId);
            }
        }
    }

    /// <summary>
    /// 发送PUBLISH响应
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="publishPacket">原始PUBLISH数据包</param>
    /// <param name="reasonCode">原因码</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    private async Task SendPublishResponseAsync(IClientConnection connection, MqttPublishPacket publishPacket, MqttReasonCode reasonCode, CancellationToken cancellationToken)
    {
        try
        {
            switch (publishPacket.QoSLevel)
            {
                case MqttQoSLevel.AtMostOnce:
                    // QoS 0 不需要响应
                    break;

                case MqttQoSLevel.AtLeastOnce:
                    // QoS 1 发送 PUBACK
                    var pubackPacket = MqttPubAckPacket.Create(
                        publishPacket.PacketIdentifier ?? 0,
                        reasonCode);
                    
                    await connection.SendPacketAsync(pubackPacket, cancellationToken);
                    
                    _logger.LogTrace("Sent PUBACK to client: {ClientId}, PacketId: {PacketId}, ReasonCode: {ReasonCode}", 
                        connection.ClientId, publishPacket.PacketIdentifier, reasonCode);
                    break;

                case MqttQoSLevel.ExactlyOnce:
                    // QoS 2 发送 PUBREC
                    var pubrecPacket = MqttPubRecPacket.Create(
                        publishPacket.PacketIdentifier ?? 0,
                        reasonCode);
                    
                    await connection.SendPacketAsync(pubrecPacket, cancellationToken);
                    
                    _logger.LogTrace("Sent PUBREC to client: {ClientId}, PacketId: {PacketId}, ReasonCode: {ReasonCode}", 
                        connection.ClientId, publishPacket.PacketIdentifier, reasonCode);
                    break;

                default:
                    _logger.LogWarning("Unknown QoS level: {QoSLevel} from client: {ClientId}", 
                        publishPacket.QoSLevel, connection.ClientId);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending PUBLISH response to client: {ClientId}", connection.ClientId);
            throw;
        }
    }

    /// <summary>
    /// 验证主题名称是否有效
    /// </summary>
    /// <param name="topicName">主题名称</param>
    /// <returns>是否有效</returns>
    private bool IsValidTopicName(string topicName)
    {
        if (string.IsNullOrWhiteSpace(topicName))
            return false;

        // 主题名称不能包含通配符
        if (topicName.Contains('+') || topicName.Contains('#'))
            return false;

        // 主题名称不能以 $ 开头（系统主题）
        if (topicName.StartsWith("$"))
            return false;

        // 检查主题长度
        if (topicName.Length > 65535)
            return false;

        // 检查是否包含空字符
        if (topicName.Contains('\0'))
            return false;

        return true;
    }
}

/// <summary>
/// MQTT PUBREC 数据包处理器（QoS 2 第二步）
/// </summary>
public class MqttPubRecPacketHandler : IMessagePacketHandler
{
    private readonly ILogger<MqttPubRecPacketHandler> _logger;

    /// <summary>
    /// 支持的数据包类型
    /// </summary>
    public MqttPacketType PacketType => MqttPacketType.PubRec;

    /// <summary>
    /// 初始化PUBREC数据包处理器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public MqttPubRecPacketHandler(ILogger<MqttPubRecPacketHandler> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 处理PUBREC数据包
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="packet">数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    public async Task HandleAsync(IClientConnection connection, IMqttPacket packet, CancellationToken cancellationToken = default)
    {
        if (packet is not MqttPubRecPacket pubrecPacket)
        {
            _logger.LogWarning("Received non-PUBREC packet in PUBREC handler from client: {ClientId}", 
                connection.ClientId);
            return;
        }

        try
        {
            _logger.LogTrace("Processing PUBREC packet from client: {ClientId}, PacketId: {PacketId}", 
                connection.ClientId, pubrecPacket.PacketIdentifier);

            // 发送 PUBREL 响应
            var pubrelPacket = MqttPubRelPacket.Create(
                pubrecPacket.PacketIdentifier ?? 0,
                MqttReasonCode.Success);
            
            await connection.SendPacketAsync(pubrelPacket, cancellationToken);
            
            _logger.LogTrace("Sent PUBREL to client: {ClientId}, PacketId: {PacketId}", 
                connection.ClientId, pubrecPacket.PacketIdentifier);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing PUBREC packet from client: {ClientId}", connection.ClientId);
        }
    }
}

/// <summary>
/// MQTT PUBREL 数据包处理器（QoS 2 第三步）
/// </summary>
public class MqttPubRelPacketHandler : IMessagePacketHandler
{
    private readonly ILogger<MqttPubRelPacketHandler> _logger;

    /// <summary>
    /// 支持的数据包类型
    /// </summary>
    public MqttPacketType PacketType => MqttPacketType.PubRel;

    /// <summary>
    /// 初始化PUBREL数据包处理器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public MqttPubRelPacketHandler(ILogger<MqttPubRelPacketHandler> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 处理PUBREL数据包
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="packet">数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    public async Task HandleAsync(IClientConnection connection, IMqttPacket packet, CancellationToken cancellationToken = default)
    {
        if (packet is not MqttPubRelPacket pubrelPacket)
        {
            _logger.LogWarning("Received non-PUBREL packet in PUBREL handler from client: {ClientId}", 
                connection.ClientId);
            return;
        }

        try
        {
            _logger.LogTrace("Processing PUBREL packet from client: {ClientId}, PacketId: {PacketId}", 
                connection.ClientId, pubrelPacket.PacketIdentifier);

            // 发送 PUBCOMP 响应
            var pubcompPacket = MqttPubCompPacket.Create(
                pubrelPacket.PacketIdentifier ?? 0,
                MqttReasonCode.Success);
            
            await connection.SendPacketAsync(pubcompPacket, cancellationToken);
            
            _logger.LogTrace("Sent PUBCOMP to client: {ClientId}, PacketId: {PacketId}", 
                connection.ClientId, pubrelPacket.PacketIdentifier);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing PUBREL packet from client: {ClientId}", connection.ClientId);
        }
    }
}
