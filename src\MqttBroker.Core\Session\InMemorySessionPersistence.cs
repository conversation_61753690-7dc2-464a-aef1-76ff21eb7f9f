using Microsoft.Extensions.Logging;
using MqttBroker.Core.Topic;
using MqttBroker.Core.QoS;
using System.Collections.Concurrent;

namespace MqttBroker.Core.Session;

/// <summary>
/// 内存会话持久化服务实现
/// </summary>
public class InMemorySessionPersistence : ISessionPersistence
{
    private readonly ILogger<InMemorySessionPersistence> _logger;
    private readonly ConcurrentDictionary<string, MqttSession> _sessions = new();
    private readonly ConcurrentDictionary<string, List<ClientSubscription>> _subscriptions = new();
    private readonly ConcurrentDictionary<string, List<PendingMessage>> _pendingMessages = new();

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public InMemorySessionPersistence(ILogger<InMemorySessionPersistence> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 创建或更新客户端会话
    /// </summary>
    public Task<SessionOperationResult> CreateOrUpdateSessionAsync(MqttSession session, CancellationToken cancellationToken = default)
    {
        try
        {
            if (session == null)
                throw new ArgumentNullException(nameof(session));

            var operationType = _sessions.ContainsKey(session.ClientId) ? SessionOperationType.Update : SessionOperationType.Create;
            _sessions[session.ClientId] = session;
            
            _logger.LogDebug("Session {Operation} for client: {ClientId}", operationType, session.ClientId);
            
            return Task.FromResult(SessionOperationResult.Success(operationType, session.ClientId));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in CreateOrUpdateSessionAsync for client: {ClientId}", session?.ClientId);
            return Task.FromResult(SessionOperationResult.Failure(SessionOperationType.Create, ex.Message, session?.ClientId));
        }
    }

    /// <summary>
    /// 获取客户端会话
    /// </summary>
    public Task<MqttSession?> GetSessionAsync(string clientId, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(clientId))
                return Task.FromResult<MqttSession?>(null);

            if (_sessions.TryGetValue(clientId, out var session))
            {
                _logger.LogTrace("Retrieved session for client: {ClientId}", clientId);
                return Task.FromResult<MqttSession?>(session);
            }
            else
            {
                _logger.LogTrace("Session not found for client: {ClientId}", clientId);
                return Task.FromResult<MqttSession?>(null);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetSessionAsync for client: {ClientId}", clientId);
            return Task.FromResult<MqttSession?>(null);
        }
    }

    /// <summary>
    /// 删除客户端会话
    /// </summary>
    public Task<SessionOperationResult> DeleteSessionAsync(string clientId, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(clientId))
                throw new ArgumentException("Client ID cannot be null or empty", nameof(clientId));

            var removed = _sessions.TryRemove(clientId, out _);
            _subscriptions.TryRemove(clientId, out _);
            _pendingMessages.TryRemove(clientId, out _);

            _logger.LogDebug("Deleted session for client: {ClientId}", clientId);
            
            return Task.FromResult(SessionOperationResult.Success(SessionOperationType.Delete, clientId, removed ? 1 : 0));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in DeleteSessionAsync for client: {ClientId}", clientId);
            return Task.FromResult(SessionOperationResult.Failure(SessionOperationType.Delete, ex.Message, clientId));
        }
    }

    /// <summary>
    /// 检查会话是否存在
    /// </summary>
    public Task<bool> SessionExistsAsync(string clientId, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(clientId))
                return Task.FromResult(false);

            return Task.FromResult(_sessions.ContainsKey(clientId));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SessionExistsAsync for client: {ClientId}", clientId);
            return Task.FromResult(false);
        }
    }

    /// <summary>
    /// 保存客户端订阅信息
    /// </summary>
    public Task<SessionOperationResult> SaveSubscriptionsAsync(string clientId, IEnumerable<ClientSubscription> subscriptions, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(clientId))
                throw new ArgumentException("Client ID cannot be null or empty", nameof(clientId));

            var subscriptionList = subscriptions?.ToList() ?? new List<ClientSubscription>();
            _subscriptions[clientId] = subscriptionList;

            _logger.LogDebug("Saved {Count} subscriptions for client: {ClientId}", subscriptionList.Count, clientId);

            return Task.FromResult(SessionOperationResult.Success(SessionOperationType.SaveSubscriptions, clientId, subscriptionList.Count));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SaveSubscriptionsAsync for client: {ClientId}", clientId);
            return Task.FromResult(SessionOperationResult.Failure(SessionOperationType.SaveSubscriptions, ex.Message, clientId));
        }
    }

    /// <summary>
    /// 获取客户端订阅信息
    /// </summary>
    public Task<IEnumerable<ClientSubscription>> GetSubscriptionsAsync(string clientId, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(clientId))
                return Task.FromResult(Enumerable.Empty<ClientSubscription>());

            if (_subscriptions.TryGetValue(clientId, out var subscriptions))
            {
                _logger.LogTrace("Retrieved {Count} subscriptions for client: {ClientId}", subscriptions.Count, clientId);
                return Task.FromResult<IEnumerable<ClientSubscription>>(subscriptions);
            }
            else
            {
                return Task.FromResult(Enumerable.Empty<ClientSubscription>());
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetSubscriptionsAsync for client: {ClientId}", clientId);
            return Task.FromResult(Enumerable.Empty<ClientSubscription>());
        }
    }

    /// <summary>
    /// 保存未确认消息
    /// </summary>
    public Task<SessionOperationResult> SavePendingMessageAsync(string clientId, PendingMessage pendingMessage, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(clientId))
                throw new ArgumentException("Client ID cannot be null or empty", nameof(clientId));

            if (pendingMessage == null)
                throw new ArgumentNullException(nameof(pendingMessage));

            var messages = _pendingMessages.GetOrAdd(clientId, _ => new List<PendingMessage>());
            
            lock (messages)
            {
                // 移除现有的相同消息ID的消息
                messages.RemoveAll(m => m.MessageId == pendingMessage.MessageId);
                messages.Add(pendingMessage);
            }

            _logger.LogTrace("Saved pending message for client: {ClientId}, messageId: {MessageId}", 
                clientId, pendingMessage.MessageId);

            return Task.FromResult(SessionOperationResult.Success(SessionOperationType.SavePendingMessage, clientId));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SavePendingMessageAsync for client: {ClientId}, messageId: {MessageId}", 
                clientId, pendingMessage?.MessageId);
            return Task.FromResult(SessionOperationResult.Failure(SessionOperationType.SavePendingMessage, ex.Message, clientId));
        }
    }

    /// <summary>
    /// 获取客户端的未确认消息
    /// </summary>
    public Task<IEnumerable<PendingMessage>> GetPendingMessagesAsync(string clientId, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(clientId))
                return Task.FromResult(Enumerable.Empty<PendingMessage>());

            if (_pendingMessages.TryGetValue(clientId, out var messages))
            {
                lock (messages)
                {
                    var result = messages.ToList();
                    _logger.LogTrace("Retrieved {Count} pending messages for client: {ClientId}", result.Count, clientId);
                    return Task.FromResult<IEnumerable<PendingMessage>>(result);
                }
            }
            else
            {
                return Task.FromResult(Enumerable.Empty<PendingMessage>());
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetPendingMessagesAsync for client: {ClientId}", clientId);
            return Task.FromResult(Enumerable.Empty<PendingMessage>());
        }
    }

    /// <summary>
    /// 删除未确认消息
    /// </summary>
    public Task<SessionOperationResult> DeletePendingMessageAsync(string clientId, ushort messageId, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(clientId))
                throw new ArgumentException("Client ID cannot be null or empty", nameof(clientId));

            if (_pendingMessages.TryGetValue(clientId, out var messages))
            {
                lock (messages)
                {
                    var removed = messages.RemoveAll(m => m.MessageId == messageId);
                    
                    if (removed > 0)
                    {
                        _logger.LogTrace("Deleted pending message for client: {ClientId}, messageId: {MessageId}", 
                            clientId, messageId);
                    }

                    return Task.FromResult(SessionOperationResult.Success(SessionOperationType.DeletePendingMessage, clientId, removed));
                }
            }

            return Task.FromResult(SessionOperationResult.Success(SessionOperationType.DeletePendingMessage, clientId, 0));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in DeletePendingMessageAsync for client: {ClientId}, messageId: {MessageId}", 
                clientId, messageId);
            return Task.FromResult(SessionOperationResult.Failure(SessionOperationType.DeletePendingMessage, ex.Message, clientId));
        }
    }

    /// <summary>
    /// 清理过期会话
    /// </summary>
    public Task<SessionCleanupResult> CleanupExpiredSessionsAsync(DateTime expiredBefore, CancellationToken cancellationToken = default)
    {
        try
        {
            var startTime = DateTime.UtcNow;
            var cleanupResult = new SessionCleanupResult { StartTime = startTime };

            var expiredSessions = _sessions.Values
                .Where(s => s.ExpiresAt.HasValue && s.ExpiresAt.Value <= expiredBefore)
                .ToList();

            foreach (var session in expiredSessions)
            {
                _sessions.TryRemove(session.ClientId, out _);
                _subscriptions.TryRemove(session.ClientId, out _);
                _pendingMessages.TryRemove(session.ClientId, out _);
            }

            cleanupResult.CleanedSessionsCount = expiredSessions.Count;
            cleanupResult.CleanedSubscriptionsCount = expiredSessions.Count; // 估算值
            cleanupResult.CleanedPendingMessagesCount = expiredSessions.Count; // 估算值
            cleanupResult.IsSuccess = true;
            cleanupResult.EndTime = DateTime.UtcNow;

            _logger.LogInformation("Cleanup completed: {SessionsCount} sessions cleaned in {Duration}ms",
                cleanupResult.CleanedSessionsCount, cleanupResult.Duration.TotalMilliseconds);

            return Task.FromResult(cleanupResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in CleanupExpiredSessionsAsync");
            return Task.FromResult(SessionCleanupResult.Failure(ex.Message));
        }
    }

    /// <summary>
    /// 批量获取活跃会话
    /// </summary>
    public Task<IEnumerable<MqttSession>> GetActiveSessionsAsync(int limit = 100, int offset = 0, CancellationToken cancellationToken = default)
    {
        try
        {
            var sessions = _sessions.Values
                .Where(s => s.State == SessionState.Active)
                .Skip(offset)
                .Take(limit)
                .ToList();

            _logger.LogTrace("Retrieved {Count} active sessions (offset: {Offset}, limit: {Limit})", 
                sessions.Count, offset, limit);

            return Task.FromResult<IEnumerable<MqttSession>>(sessions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetActiveSessionsAsync");
            return Task.FromResult(Enumerable.Empty<MqttSession>());
        }
    }

    /// <summary>
    /// 获取会话统计信息
    /// </summary>
    public Task<SessionStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var statistics = new SessionStatistics();
            var allSessions = _sessions.Values.ToList();

            statistics.TotalSessions = allSessions.Count;
            statistics.ActiveSessions = allSessions.Count(s => s.State == SessionState.Active);
            statistics.OfflineSessions = allSessions.Count(s => s.State == SessionState.Offline);
            statistics.ExpiredSessions = allSessions.Count(s => s.State == SessionState.Expired);
            statistics.CleanSessions = allSessions.Count(s => s.IsCleanSession);
            statistics.PersistentSessions = allSessions.Count(s => !s.IsCleanSession);

            statistics.TotalSubscriptions = _subscriptions.Values.Sum(list => list.Count);
            statistics.TotalPendingMessages = _pendingMessages.Values.Sum(list => list.Count);

            if (allSessions.Any())
            {
                var now = DateTime.UtcNow;
                var ages = allSessions.Select(s => (now - s.CreatedAt).TotalHours).ToList();
                statistics.AverageSessionAgeHours = ages.Average();
                statistics.OldestSessionAgeHours = ages.Max();
            }

            _logger.LogTrace("Generated session statistics: {TotalSessions} total, {ActiveSessions} active", 
                statistics.TotalSessions, statistics.ActiveSessions);

            return Task.FromResult(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetStatisticsAsync");
            return Task.FromResult(new SessionStatistics());
        }
    }

    /// <summary>
    /// 更新会话的最后活动时间
    /// </summary>
    public Task<SessionOperationResult> UpdateSessionActivityAsync(string clientId, DateTime lastActivity, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(clientId))
                throw new ArgumentException("Client ID cannot be null or empty", nameof(clientId));

            if (_sessions.TryGetValue(clientId, out var session))
            {
                session.LastActivity = lastActivity;
                _logger.LogTrace("Updated activity time for client: {ClientId}", clientId);
                return Task.FromResult(SessionOperationResult.Success(SessionOperationType.UpdateActivity, clientId));
            }

            return Task.FromResult(SessionOperationResult.Success(SessionOperationType.UpdateActivity, clientId, 0));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in UpdateSessionActivityAsync for client: {ClientId}", clientId);
            return Task.FromResult(SessionOperationResult.Failure(SessionOperationType.UpdateActivity, ex.Message, clientId));
        }
    }

    /// <summary>
    /// 批量删除会话
    /// </summary>
    public Task<BatchSessionOperationResult> DeleteSessionsAsync(IEnumerable<string> clientIds, CancellationToken cancellationToken = default)
    {
        try
        {
            var clientIdList = clientIds?.ToList() ?? new List<string>();
            if (!clientIdList.Any())
            {
                return Task.FromResult(BatchSessionOperationResult.Success(SessionOperationType.BatchDelete, 0, 0));
            }

            var successCount = 0;
            foreach (var clientId in clientIdList)
            {
                if (_sessions.TryRemove(clientId, out _))
                {
                    _subscriptions.TryRemove(clientId, out _);
                    _pendingMessages.TryRemove(clientId, out _);
                    successCount++;
                }
            }

            _logger.LogDebug("Batch deleted {Count} sessions", successCount);

            return Task.FromResult(BatchSessionOperationResult.Success(SessionOperationType.BatchDelete, clientIdList.Count, successCount));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in DeleteSessionsAsync");
            var clientIdList = clientIds?.ToList() ?? new List<string>();
            var result = BatchSessionOperationResult.Success(SessionOperationType.BatchDelete, clientIdList.Count, 0);
            result.IsSuccess = false;
            return Task.FromResult(result);
        }
    }
}
