using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using MqttBroker.Storage.Repositories;

namespace MqttBroker.Storage;

/// <summary>
/// 存储层服务注册扩展
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加 MQTT Broker 存储服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddMqttBrokerStorage(this IServiceCollection services, IConfiguration configuration)
    {
        // 获取连接字符串
        var connectionString = configuration.GetConnectionString("DefaultConnection")
            ?? "Data Source=mqtt_broker.db";

        return services.AddMqttBrokerStorage(connectionString);
    }

    /// <summary>
    /// 添加 MQTT Broker 存储服务（指定连接字符串）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="connectionString">数据库连接字符串</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddMqttBrokerStorage(this IServiceCollection services, string connectionString)
    {
        // 注册 Entity Framework 数据库上下文
        services.AddDbContext<MqttBrokerDbContext>(options =>
        {
            options.UseSqlite(connectionString, sqliteOptions =>
            {
                sqliteOptions.CommandTimeout(30);
            });

            // 开发环境启用敏感数据日志记录
            if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development")
            {
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
            }
        });

        // 注册仓储服务
        services.AddScoped<ISessionRepository, SessionRepository>();
        services.AddScoped<ISubscriptionRepository, SubscriptionRepository>();
        services.AddScoped<IPendingMessageRepository, PendingMessageRepository>();

        return services;
    }

    /// <summary>
    /// 添加 MQTT Broker 存储服务（使用内存数据库）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="databaseName">内存数据库名称</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddMqttBrokerInMemoryStorage(this IServiceCollection services, string databaseName = "MqttBrokerInMemory")
    {
        // 注册 Entity Framework 内存数据库上下文
        services.AddDbContext<MqttBrokerDbContext>(options =>
        {
            options.UseInMemoryDatabase(databaseName);
            options.EnableSensitiveDataLogging();
            options.EnableDetailedErrors();
        });

        // 注册仓储服务
        services.AddScoped<ISessionRepository, SessionRepository>();
        services.AddScoped<ISubscriptionRepository, SubscriptionRepository>();
        services.AddScoped<IPendingMessageRepository, PendingMessageRepository>();

        return services;
    }

    /// <summary>
    /// 确保数据库已创建
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection EnsureDatabaseCreated(this IServiceCollection services)
    {
        var serviceProvider = services.BuildServiceProvider();
        using var scope = serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<MqttBrokerDbContext>();

        context.Database.EnsureCreated();

        return services;
    }

    /// <summary>
    /// 添加数据库健康检查
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="name">健康检查名称</param>
    /// <param name="tags">标签</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddMqttBrokerStorageHealthChecks(this IServiceCollection services, string name = "mqtt_broker_database", params string[] tags)
    {
        services.AddHealthChecks()
            .AddDbContextCheck<MqttBrokerDbContext>(name, tags: tags);

        return services;
    }

    /// <summary>
    /// 添加完整的存储层服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddCompleteMqttBrokerStorage(this IServiceCollection services, IConfiguration configuration)
    {
        return services
            .AddMqttBrokerStorage(configuration)
            .AddMqttBrokerStorageHealthChecks();
    }
}
