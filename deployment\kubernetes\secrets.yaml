apiVersion: v1
kind: Secret
metadata:
  name: mqtt-broker-secrets
  namespace: mqtt-system
  labels:
    app: mqtt-broker
type: Opaque
data:
  # PostgreSQL 密码 (base64 编码)
  postgres-password: bXF0dF9wYXNzd29yZF9jaGFuZ2VfbWU=
  
  # TLS 证书密码 (base64 编码)
  tls-cert-password: dGxzX3Bhc3N3b3JkX2NoYW5nZV9tZQ==
  
  # JWT 密钥 (base64 编码)
  jwt-secret-key: eW91ci0yNTYtYml0LXNlY3JldC1rZXktaGVyZS1jaGFuZ2UtbWU=
  
  # Redis 密码 (base64 编码，可选)
  redis-password: cmVkaXNfcGFzc3dvcmRfY2hhbmdlX21l

---
apiVersion: v1
kind: Secret
metadata:
  name: mqtt-broker-tls
  namespace: mqtt-system
  labels:
    app: mqtt-broker
type: kubernetes.io/tls
data:
  # TLS 证书 (base64 编码)
  tls.crt: |
    LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURYVENDQWtXZ0F3SUJBZ0lKQUxGOGlPdm1kMDdNTUEwR0NTcUdTSWIzRFFFQkN3VUFNRVl4Q3pBSkJnTlYKQkFZVEFrTk9NUkF3RGdZRFZRUUlEQWRDWldsamFXNW5NUkF3RGdZRFZRUUhEQWRDWldsamFXNW5NUk13RVFZRApWUVFLREFwTlVWUlVJRUp5YjJ0bGNqQWVGdzB5TkRBeE1ERXdOekF3TURCYUZ3MHlOVEF4TURFd056QXdNREJhCk1FWXhDekFKQmdOVkJBWVRBa05PTVRFME1BNEdBMVVFQ0F3SFFtVnBhbWx1WnpFUU1BNEdBMVVFQnd3SFFtVnAKYW1sdVp6RVRNQkVHQTFVRUNnd0tUVkZVVkNCQ2NtOXJaWEl3Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQgpEd0F3Z2dFS0FvSUJBUURGOGlPdm1kMDdNTUEwR0NTcUdTSWIzRFFFQkN3VUFNRVl4Q3pBSkJnTlZCQVlUQWtOTwpNUkF3RGdZRFZRUUlEQWRDWldsamFXNW5NUkF3RGdZRFZRUUhEQWRDWldsamFXNW5NUk13RVFZRFZRUUtEQXBOClVWUlVJRUp5YjJ0bGNqQWVGdzB5TkRBeE1ERXdOekF3TURCYUZ3MHlOVEF4TURFd056QXdNREJhTUVZeEN6QUoKQmdOVkJBWVRBa05PTVRFME1BNEdBMVVFQ0F3SFFtVnBhbWx1WnpFUU1BNEdBMVVFQnd3SFFtVnBhbWx1WnpFVApNQkVHQTFVRUNnd0tUVkZVVkNCQ2NtOXJaWEl3Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLCkFvSUJBUURGOGlPdm1kMDdNTUEwR0NTcUdTSWIzRFFFQkN3VUFNRVl4Q3pBSkJnTlZCQVlUQWtOT01SQXdEZ1kKRFZRUUlEQWRDWldsamFXNW5NUkF3RGdZRFZRUUhEQWRDWldsamFXNW5NUk13RVFZRFZRUUtEQXBOVVZSVUlFSnkKYjJ0bGNqQWVGdzB5TkRBeE1ERXdOekF3TURCYUZ3MHlOVEF4TURFd056QXdNREJhTUVZeEN6QUpCZ05WQkFZVApBa05PTVRFME1BNEdBMVVFQ0F3SFFtVnBhbWx1WnpFUU1BNEdBMVVFQnd3SFFtVnBhbWx1WnpFVE1CRUdBMVVFCkNnd0tUVkZVVkNCQ2NtOXJaWEl3Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRREYKOGlPdm1kMDdNTUEwR0NTcUdTSWIzRFFFQkN3VUFNRVl4Q3pBSkJnTlZCQVlUQWtOT01SQXdEZ1lEVlFRSURBZEMKWldsamFXNW5NUkF3RGdZRFZRUUhEQWRDWldsamFXNW5NUk13RVFZRFZRUUtEQXBOVVZSVUlFSnliMnRsY2pBZQpGdzB5TkRBeE1ERXdOekF3TURCYUZ3MHlOVEF4TURFd056QXdNREJhTUVZeEN6QUpCZ05WQkFZVEFrTk9NUkF3CkRnWURWUVFJREFkQ1pXbHFhVzVuTVJBd0RnWURWUVFIREFkQ1pXbHFhVzVuTVJNd0VRWURWUVFLREFwTlVWUlUKSUVKeWIydGxjakFlRncweU5EQXhNREV3TnpBd01EQmFGdzB5TlRBeE1ERXdOekF3TURCYQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0t
  
  # TLS 私钥 (base64 编码)
  tls.key: |
    LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0tCk1JSUV2UUlCQURBTkJna3Foa2lHOXcwQkFRRUZBQVNDQktjd2dnU2pBZ0VBQW9JQkFRREY4aU92bWQwN01NQTAKR0NTcUdTSWIzRFFFQkN3VUFNRVl4Q3pBSkJnTlZCQVlUQWtOT01SQXdEZ1lEVlFRSURBZENaV2xxYVc1bk1SQXcKRGdZRFZRUUhEQWRDWldscWFXNW5NUk13RVFZRFZRUUtEQXBOVVZSVUlFSnliMnRsY2pBZUZ3MHlOREF4TURFdwpOekF3TURCYUZ3MHlOVEF4TURFd056QXdNREJhTUVZeEN6QUpCZ05WQkFZVEFrTk9NUkF3RGdZRFZRUUlEQWRDClpXbHFhVzVuTVJBd0RnWURWUVFIREFkQ1pXbHFhVzVuTVJNd0VRWURWUVFLREFwTlVWUlVJRUp5YjJ0bGNqQWcKZ2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRREY4aU92bWQwN01NQTBHQ1NxR1NJYgozRFFFQkN3VUFNRVl4Q3pBSkJnTlZCQVlUQWtOT01SQXdEZ1lEVlFRSURBZENaV2xxYVc1bk1SQXdEZ1lEVlFRSApEQWRDWldscWFXNW5NUk13RVFZRFZRUUtEQXBOVVZSVUlFSnliMnRsY2pBZUZ3MHlOREF4TURFd056QXdNREJhCkZ3MHlOVEF4TURFd056QXdNREJhTUVZeEN6QUpCZ05WQkFZVEFrTk9NUkF3RGdZRFZRUUlEQWRDWldscWFXNW4KTVJBd0RnWURWUVFIREFkQ1pXbHFhVzVuTVJNd0VRWURWUVFLREFwTlVWUlVJRUp5YjJ0bGNqQWdnZ0VpTUEwRwpDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRREY4aU92bWQwN01NQTBHQ1NxR1NJYjNEUUVCQ3dVQQpNRVl4Q3pBSkJnTlZCQVlUQWtOT01SQXdEZ1lEVlFRSURBZENaV2xxYVc1bk1SQXdEZ1lEVlFRSERBZENaV2xxCmFXNW5NUk13RVFZRFZRUUtEQXBOVVZSVUlFSnliMnRsY2pBZ2dnRWlNQTBHQ1NxR1NJYjNEUUVCQVFVQUE0SUIKRHRBZ2dnRUtBb0lCQVFERjhpT3ZtZDA3TU1BMEdDU3FHU0liM0RRRUJDd1VBTUV5eEN6QUpCZ05WQkFZVEFrTk8KTVJBd0RnWURWUVFJREFkQ1pXbHFhVzVuTVJBd0RnWURWUVFIREFkQ1pXbHFhVzVuTVJNd0VRWURWUVFLREFwTgpVVlJVSUVKeWIydGxjakFlRncweU5EQXhNREV3TnpBd01EQmFGdzB5TlRBeE1ERXdOekF3TURCYQotLS0tLUVORCBQUklWQVRFIEtFWS0tLS0t

---
apiVersion: v1
kind: Secret
metadata:
  name: mqtt-broker-registry
  namespace: mqtt-system
  labels:
    app: mqtt-broker
type: kubernetes.io/dockerconfigjson
data:
  # Docker 镜像仓库认证信息 (base64 编码)
  .dockerconfigjson: ************************************************************************************************************************************************************

---
apiVersion: v1
kind: Secret
metadata:
  name: mqtt-broker-monitoring
  namespace: mqtt-system
  labels:
    app: mqtt-broker
    component: monitoring
type: Opaque
data:
  # Grafana 管理员密码 (base64 编码)
  grafana-admin-password: WVdSdGFXNWZZMmhoYm1kbFgyMWw=
  
  # Prometheus 基本认证密码 (base64 编码，可选)
  prometheus-password: Y0hKdmJXVjBhR1YxYzE5amFHRnVaMlZmYldV

---
# 用于生成密钥的脚本注释
# 生成随机密码:
# openssl rand -base64 32
#
# 对字符串进行 base64 编码:
# echo -n "your-password" | base64
#
# 生成 JWT 密钥:
# openssl rand -hex 32 | base64
#
# 创建 Docker 认证配置:
# kubectl create secret docker-registry mqtt-broker-registry \
#   --docker-server=ghcr.io \
#   --docker-username=your-username \
#   --docker-password=your-token \
#   --docker-email=<EMAIL> \
#   --dry-run=client -o yaml
