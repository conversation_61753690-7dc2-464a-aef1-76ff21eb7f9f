﻿using System;
using System.Collections.Generic;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

/// <summary>
/// 简单的MQTT测试客户端，用于验证修复效果
/// </summary>
public class Program
{
    public static async Task Main(string[] args)
    {
        Console.WriteLine("MQTT测试客户端启动...");

        try
        {
            // 测试正常连接
            await TestNormalConnection();

            // 测试恶意数据包（应该被安全处理）
            await TestMaliciousPacket();

            Console.WriteLine("所有测试完成！");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"测试失败: {ex.Message}");
        }
    }

    private static async Task TestNormalConnection()
    {
        Console.WriteLine("测试正常MQTT连接...");

        using var client = new TcpClient();
        await client.ConnectAsync("127.0.0.1", 1883);

        var stream = client.GetStream();

        // 构造正常的CONNECT包
        var connectPacket = CreateValidConnectPacket("TestClient123");
        await stream.WriteAsync(connectPacket);

        // 读取CONNACK响应
        var buffer = new byte[1024];
        var bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);

        if (bytesRead > 0)
        {
            Console.WriteLine($"收到CONNACK响应: {bytesRead} 字节");
            Console.WriteLine($"响应数据: {BitConverter.ToString(buffer, 0, bytesRead)}");

            // 检查CONNACK包
            if (buffer[0] == 0x20) // CONNACK包类型
            {
                var returnCode = buffer[3];
                if (returnCode == 0x00)
                {
                    Console.WriteLine("✓ 正常连接测试成功！");
                }
                else
                {
                    Console.WriteLine($"✗ 连接被拒绝，返回码: {returnCode}");
                }
            }
        }

        client.Close();
    }

    private static async Task TestMaliciousPacket()
    {
        Console.WriteLine("测试恶意数据包处理...");

        using var client = new TcpClient();
        await client.ConnectAsync("127.0.0.1", 1883);

        var stream = client.GetStream();

        // 构造恶意的CONNECT包（字符串长度超出缓冲区）
        var maliciousPacket = new byte[]
        {
            0x10, // CONNECT包类型
            0x0A, // 剩余长度 = 10字节
            0x05, 0x11, // 字符串长度 = 1297 (0x0511) - 这会导致原来的错误
            0x4D, 0x51, 0x54, 0x54, // "MQTT"的前4个字节
            0x04, // 协议版本
            0x00  // 连接标志
        };

        await stream.WriteAsync(maliciousPacket);

        // 等待一下看服务器是否会崩溃或发送响应
        await Task.Delay(1000);

        try
        {
            var buffer = new byte[1024];
            var bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);

            if (bytesRead > 0)
            {
                Console.WriteLine($"收到响应: {bytesRead} 字节");
                Console.WriteLine($"响应数据: {BitConverter.ToString(buffer, 0, bytesRead)}");
            }
            else
            {
                Console.WriteLine("服务器关闭了连接（预期行为）");
            }

            Console.WriteLine("✓ 恶意数据包测试成功！服务器没有崩溃");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✓ 恶意数据包测试成功！连接被安全关闭: {ex.Message}");
        }

        client.Close();
    }

    private static byte[] CreateValidConnectPacket(string clientId)
    {
        var protocolName = "MQTT";
        var protocolNameBytes = Encoding.UTF8.GetBytes(protocolName);
        var clientIdBytes = Encoding.UTF8.GetBytes(clientId);

        var payload = new List<byte>();

        // 协议名称
        payload.Add((byte)(protocolNameBytes.Length >> 8));
        payload.Add((byte)(protocolNameBytes.Length & 0xFF));
        payload.AddRange(protocolNameBytes);

        // 协议版本
        payload.Add(0x04);

        // 连接标志
        payload.Add(0x00);

        // Keep Alive
        payload.Add(0x00);
        payload.Add(0x3C); // 60秒

        // 客户端ID
        payload.Add((byte)(clientIdBytes.Length >> 8));
        payload.Add((byte)(clientIdBytes.Length & 0xFF));
        payload.AddRange(clientIdBytes);

        var packet = new List<byte>();
        packet.Add(0x10); // CONNECT包类型
        packet.Add((byte)payload.Count); // 剩余长度
        packet.AddRange(payload);

        return packet.ToArray();
    }
}
