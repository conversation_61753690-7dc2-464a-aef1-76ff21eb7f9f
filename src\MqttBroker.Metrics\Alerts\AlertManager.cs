using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MqttBroker.Metrics.Configuration;
using MqttBroker.Metrics.Models;
using System.Collections.Concurrent;
using System.Reflection;

namespace MqttBroker.Metrics.Alerts;

/// <summary>
/// 告警管理器实现
/// </summary>
public class AlertManager : IAlertManager, IDisposable
{
    private readonly IAlertStorage _storage;
    private readonly IEnumerable<IAlertRuleEvaluator> _evaluators;
    private readonly ILogger<AlertManager> _logger;
    private readonly AlertOptions _options;

    // 内存缓存
    private readonly ConcurrentDictionary<string, AlertRule> _rulesCache = new();
    private readonly ConcurrentDictionary<string, Alert> _activeAlerts = new();
    private readonly ConcurrentDictionary<string, int> _ruleEvaluationCounts = new();

    // 抑制状态跟踪
    private readonly ConcurrentDictionary<string, DateTime> _suppressedUntil = new();

    private readonly Timer _cleanupTimer;
    private bool _disposed;

    /// <summary>
    /// 告警触发事件
    /// </summary>
    public event EventHandler<AlertTriggeredEventArgs>? AlertTriggered;

    /// <summary>
    /// 告警解决事件
    /// </summary>
    public event EventHandler<AlertResolvedEventArgs>? AlertResolved;

    /// <summary>
    /// 构造函数
    /// </summary>
    public AlertManager(
        IAlertStorage storage,
        IEnumerable<IAlertRuleEvaluator> evaluators,
        ILogger<AlertManager> logger,
        IOptions<AlertOptions> options)
    {
        _storage = storage ?? throw new ArgumentNullException(nameof(storage));
        _evaluators = evaluators ?? throw new ArgumentNullException(nameof(evaluators));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));

        // 启动定期清理定时器
        _cleanupTimer = new Timer(
            CleanupCallback,
            null,
            TimeSpan.FromHours(1), // 1小时后开始第一次清理
            TimeSpan.FromHours(1)  // 每小时清理一次
        );

        // 初始化时加载规则
        _ = Task.Run(LoadRulesAsync);

        _logger.LogInformation("告警管理器已初始化，启用告警: {EnableAlerts}", _options.EnableAlerts);
    }

    /// <summary>
    /// 添加告警规则
    /// </summary>
    public async Task AddRuleAsync(AlertRule rule, CancellationToken cancellationToken = default)
    {
        if (rule == null)
            throw new ArgumentNullException(nameof(rule));

        try
        {
            rule.UpdatedAt = DateTime.UtcNow;
            await _storage.StoreRuleAsync(rule, cancellationToken);
            _rulesCache[rule.Id] = rule;

            _logger.LogInformation("已添加告警规则: {RuleId} - {RuleName}", rule.Id, rule.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加告警规则时发生错误: {RuleId}", rule.Id);
            throw;
        }
    }

    /// <summary>
    /// 更新告警规则
    /// </summary>
    public async Task UpdateRuleAsync(AlertRule rule, CancellationToken cancellationToken = default)
    {
        if (rule == null)
            throw new ArgumentNullException(nameof(rule));

        try
        {
            rule.UpdatedAt = DateTime.UtcNow;
            await _storage.StoreRuleAsync(rule, cancellationToken);
            _rulesCache[rule.Id] = rule;

            _logger.LogInformation("已更新告警规则: {RuleId} - {RuleName}", rule.Id, rule.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新告警规则时发生错误: {RuleId}", rule.Id);
            throw;
        }
    }

    /// <summary>
    /// 删除告警规则
    /// </summary>
    public async Task RemoveRuleAsync(string ruleId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(ruleId))
            throw new ArgumentException("规则 ID 不能为空", nameof(ruleId));

        try
        {
            await _storage.DeleteRuleAsync(ruleId, cancellationToken);
            _rulesCache.TryRemove(ruleId, out _);
            _ruleEvaluationCounts.TryRemove(ruleId, out _);

            _logger.LogInformation("已删除告警规则: {RuleId}", ruleId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除告警规则时发生错误: {RuleId}", ruleId);
            throw;
        }
    }

    /// <summary>
    /// 获取所有告警规则
    /// </summary>
    public Task<IEnumerable<AlertRule>> GetRulesAsync(CancellationToken cancellationToken = default)
    {
        return Task.FromResult<IEnumerable<AlertRule>>(_rulesCache.Values.ToList());
    }

    /// <summary>
    /// 获取指定告警规则
    /// </summary>
    public Task<AlertRule?> GetRuleAsync(string ruleId, CancellationToken cancellationToken = default)
    {
        _rulesCache.TryGetValue(ruleId, out var rule);
        return Task.FromResult(rule);
    }

    /// <summary>
    /// 评估指标并触发告警
    /// </summary>
    public async Task EvaluateMetricsAsync(PerformanceMetrics metrics, CancellationToken cancellationToken = default)
    {
        if (!_options.EnableAlerts || metrics == null)
            return;

        try
        {
            var rules = _rulesCache.Values.Where(r => r.IsEnabled).ToList();
            
            foreach (var rule in rules)
            {
                await EvaluateRuleAsync(rule, metrics, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "评估告警规则时发生错误");
        }
    }

    /// <summary>
    /// 获取活跃告警
    /// </summary>
    public Task<IEnumerable<Alert>> GetActiveAlertsAsync(CancellationToken cancellationToken = default)
    {
        var activeAlerts = _activeAlerts.Values
            .Where(a => a.Status == AlertStatus.Active)
            .OrderByDescending(a => a.LastTriggeredAt)
            .ToList();

        return Task.FromResult<IEnumerable<Alert>>(activeAlerts);
    }

    /// <summary>
    /// 获取告警历史
    /// </summary>
    public async Task<IEnumerable<Alert>> GetAlertHistoryAsync(DateTime startTime, DateTime endTime, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _storage.GetAlertHistoryAsync(startTime, endTime, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取告警历史时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 确认告警
    /// </summary>
    public async Task AcknowledgeAlertAsync(string alertId, string acknowledgedBy, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(alertId))
            throw new ArgumentException("告警 ID 不能为空", nameof(alertId));

        try
        {
            if (_activeAlerts.TryGetValue(alertId, out var alert))
            {
                alert.Status = AlertStatus.Acknowledged;
                alert.AcknowledgedAt = DateTime.UtcNow;
                alert.AcknowledgedBy = acknowledgedBy;

                await _storage.UpdateAlertAsync(alert, cancellationToken);

                _logger.LogInformation("告警已确认: {AlertId}，确认人: {AcknowledgedBy}", alertId, acknowledgedBy);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "确认告警时发生错误: {AlertId}", alertId);
            throw;
        }
    }

    /// <summary>
    /// 解决告警
    /// </summary>
    public async Task ResolveAlertAsync(string alertId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(alertId))
            throw new ArgumentException("告警 ID 不能为空", nameof(alertId));

        try
        {
            if (_activeAlerts.TryGetValue(alertId, out var alert))
            {
                alert.Status = AlertStatus.Resolved;
                alert.ResolvedAt = DateTime.UtcNow;

                await _storage.UpdateAlertAsync(alert, cancellationToken);
                _activeAlerts.TryRemove(alertId, out _);

                if (_rulesCache.TryGetValue(alert.RuleId, out var rule))
                {
                    AlertResolved?.Invoke(this, new AlertResolvedEventArgs(alert, rule));
                }

                _logger.LogInformation("告警已解决: {AlertId}", alertId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解决告警时发生错误: {AlertId}", alertId);
            throw;
        }
    }

    /// <summary>
    /// 获取告警统计信息
    /// </summary>
    public Task<AlertStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default)
    {
        var activeAlerts = _activeAlerts.Values.ToList();
        var today = DateTime.UtcNow.Date;

        var statistics = new AlertStatistics
        {
            ActiveAlerts = activeAlerts.Count,
            ActiveAlertsByLevel = activeAlerts
                .GroupBy(a => a.Level)
                .ToDictionary(g => g.Key, g => g.Count()),
            TodayNewAlerts = activeAlerts.Count(a => a.FirstTriggeredAt >= today),
            // 其他统计信息可以从存储中获取...
        };

        return Task.FromResult(statistics);
    }

    /// <summary>
    /// 评估单个规则
    /// </summary>
    private async Task EvaluateRuleAsync(AlertRule rule, PerformanceMetrics metrics, CancellationToken cancellationToken)
    {
        try
        {
            // 检查是否在抑制期内
            if (_suppressedUntil.TryGetValue(rule.Id, out var suppressedUntil) && DateTime.UtcNow < suppressedUntil)
            {
                return;
            }

            // 查找合适的评估器
            var evaluator = _evaluators.FirstOrDefault(e => e.SupportsRule(rule));
            if (evaluator == null)
            {
                _logger.LogWarning("未找到支持规则 {RuleId} 的评估器", rule.Id);
                return;
            }

            // 评估规则
            var result = evaluator.EvaluateRule(rule, metrics);
            
            // 更新评估计数
            _ruleEvaluationCounts.AddOrUpdate(rule.Id, 1, (key, count) => count + 1);

            // 处理评估结果
            await ProcessEvaluationResultAsync(rule, result, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "评估规则时发生错误: {RuleId}", rule.Id);
        }
    }

    /// <summary>
    /// 处理评估结果
    /// </summary>
    private async Task ProcessEvaluationResultAsync(AlertRule rule, AlertEvaluationResult result, CancellationToken cancellationToken)
    {
        var existingAlert = _activeAlerts.Values.FirstOrDefault(a => a.RuleId == rule.Id && a.Status == AlertStatus.Active);

        if (result.IsTriggered)
        {
            if (existingAlert == null)
            {
                // 创建新告警
                var alert = new Alert
                {
                    RuleId = rule.Id,
                    RuleName = rule.Name,
                    Level = rule.Level,
                    Message = result.Message,
                    Description = rule.Description,
                    MetricPath = rule.MetricPath,
                    CurrentValue = result.CurrentValue,
                    Threshold = result.Threshold,
                    Tags = new Dictionary<string, string>(rule.Tags),
                    AdditionalData = result.AdditionalData
                };

                _activeAlerts[alert.Id] = alert;
                await _storage.StoreAlertAsync(alert, cancellationToken);

                // 设置抑制时间
                _suppressedUntil[rule.Id] = DateTime.UtcNow.AddSeconds(rule.SuppressionSeconds);

                AlertTriggered?.Invoke(this, new AlertTriggeredEventArgs(alert, rule));

                _logger.LogWarning("告警触发: {RuleName} - {Message}", rule.Name, result.Message);
            }
            else
            {
                // 更新现有告警
                existingAlert.LastTriggeredAt = DateTime.UtcNow;
                existingAlert.CurrentValue = result.CurrentValue;
                existingAlert.TriggerCount++;

                await _storage.UpdateAlertAsync(existingAlert, cancellationToken);
            }
        }
        else if (existingAlert != null)
        {
            // 告警条件不再满足，解决告警
            await ResolveAlertAsync(existingAlert.Id, cancellationToken);
        }
    }

    /// <summary>
    /// 加载规则
    /// </summary>
    private async Task LoadRulesAsync()
    {
        try
        {
            var rules = await _storage.GetRulesAsync();
            foreach (var rule in rules)
            {
                _rulesCache[rule.Id] = rule;
            }

            _logger.LogInformation("已加载 {Count} 个告警规则", _rulesCache.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载告警规则时发生错误");
        }
    }

    /// <summary>
    /// 清理回调
    /// </summary>
    private async void CleanupCallback(object? state)
    {
        try
        {
            var retentionPeriod = TimeSpan.FromDays(_options.HistoryRetentionDays);
            var cleanedCount = await _storage.CleanupExpiredAlertsAsync(retentionPeriod);
            
            if (cleanedCount > 0)
            {
                _logger.LogInformation("清理了 {Count} 个过期告警", cleanedCount);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期告警时发生错误");
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _cleanupTimer?.Dispose();
            _disposed = true;
        }
        GC.SuppressFinalize(this);
    }
}
