using System.Diagnostics;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using MqttBroker.Core.Client;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Topic;
using Xunit;
using Xunit.Abstractions;

namespace MqttBroker.Tests.Performance.Topic;

/// <summary>
/// 主题订阅系统性能测试
/// </summary>
public class TopicSubscriptionPerformanceTests
{
    private readonly ITestOutputHelper _output;

    public TopicSubscriptionPerformanceTests(ITestOutputHelper output)
    {
        _output = output;
    }

    [Fact]
    public async Task TopicTree_100K_Subscriptions_Performance_Test()
    {
        // Arrange
        const int subscriptionCount = 100_000;
        var topicTree = new TopicTree();
        var stopwatch = new Stopwatch();

        _output.WriteLine($"开始添加 {subscriptionCount:N0} 个订阅...");

        // Act - 添加订阅
        stopwatch.Start();
        for (int i = 0; i < subscriptionCount; i++)
        {
            var subscriber = CreateTestSubscriber($"client{i}", $"sensors/device{i}/temperature", MqttQoSLevel.AtLeastOnce);
            topicTree.AddSubscriber($"sensors/device{i}/temperature", subscriber);
        }
        stopwatch.Stop();

        var addTime = stopwatch.ElapsedMilliseconds;
        _output.WriteLine($"添加 {subscriptionCount:N0} 个订阅耗时: {addTime:N0} ms");
        _output.WriteLine($"平均每个订阅耗时: {(double)addTime / subscriptionCount:F3} ms");

        // Act - 主题匹配性能测试
        stopwatch.Restart();
        const int matchCount = 1000;
        for (int i = 0; i < matchCount; i++)
        {
            var deviceId = i % subscriptionCount;
            var subscribers = topicTree.GetMatchingSubscribers($"sensors/device{deviceId}/temperature");
            Assert.Single(subscribers);
        }
        stopwatch.Stop();

        var matchTime = stopwatch.ElapsedMilliseconds;
        _output.WriteLine($"执行 {matchCount:N0} 次主题匹配耗时: {matchTime:N0} ms");
        _output.WriteLine($"平均每次匹配耗时: {(double)matchTime / matchCount:F3} ms");

        // Assert - 验证性能目标
        Assert.True((double)matchTime / matchCount < 1.0, "单次主题匹配延迟应小于 1ms");

        // 获取统计信息
        var statistics = topicTree.GetStatistics();
        _output.WriteLine($"主题树统计: 总节点数={statistics.TotalNodes:N0}, 有订阅者的节点数={statistics.NodesWithSubscribers:N0}, 总订阅者数={statistics.TotalSubscribers:N0}");
    }

    [Fact]
    public async Task TopicTree_Wildcard_Matching_Performance_Test()
    {
        // Arrange
        var topicTree = new TopicTree();
        const int subscriberCount = 10_000;

        // 添加各种通配符订阅
        for (int i = 0; i < subscriberCount; i++)
        {
            var subscriber1 = CreateTestSubscriber($"client{i}_single", "sensors/+/temperature", MqttQoSLevel.AtLeastOnce);
            var subscriber2 = CreateTestSubscriber($"client{i}_multi", "sensors/#", MqttQoSLevel.AtMostOnce);
            
            topicTree.AddSubscriber("sensors/+/temperature", subscriber1);
            topicTree.AddSubscriber("sensors/#", subscriber2);
        }

        _output.WriteLine($"添加了 {subscriberCount * 2:N0} 个通配符订阅");

        // Act - 测试通配符匹配性能
        var stopwatch = Stopwatch.StartNew();
        const int testCount = 1000;
        
        for (int i = 0; i < testCount; i++)
        {
            var subscribers = topicTree.GetMatchingSubscribers($"sensors/room{i}/temperature");
            Assert.True(subscribers.Count >= subscriberCount * 2); // 应该匹配所有通配符订阅
        }
        
        stopwatch.Stop();

        var matchTime = stopwatch.ElapsedMilliseconds;
        _output.WriteLine($"执行 {testCount:N0} 次通配符匹配耗时: {matchTime:N0} ms");
        _output.WriteLine($"平均每次匹配耗时: {(double)matchTime / testCount:F3} ms");
        _output.WriteLine($"每次匹配找到 {subscriberCount * 2:N0} 个订阅者");

        // Assert - 验证性能目标
        Assert.True((double)matchTime / testCount < 1.0, "通配符匹配延迟应小于 1ms");
    }

    [Fact]
    public async Task TopicSubscriptionManager_Concurrent_Operations_Test()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Warning));
        services.AddTopicSubscriptionSystem();

        var serviceProvider = services.BuildServiceProvider();
        var subscriptionManager = serviceProvider.GetRequiredService<ITopicSubscriptionManager>();

        const int clientCount = 1000;
        const int subscriptionsPerClient = 10;

        _output.WriteLine($"开始并发订阅测试: {clientCount:N0} 个客户端，每个客户端 {subscriptionsPerClient} 个订阅");

        // Act - 并发订阅测试
        var stopwatch = Stopwatch.StartNew();
        
        var tasks = Enumerable.Range(0, clientCount).Select(async clientIndex =>
        {
            var client = CreateMockClient($"client{clientIndex}");
            var subscriptions = new List<MqttSubscription>();

            for (int i = 0; i < subscriptionsPerClient; i++)
            {
                subscriptions.Add(new MqttSubscription
                {
                    TopicFilter = $"client{clientIndex}/topic{i}",
                    QoSLevel = MqttQoSLevel.AtLeastOnce
                });
            }

            var results = await subscriptionManager.SubscribeAsync(client, subscriptions);
            Assert.All(results, result => Assert.True(result.IsSuccess));
        });

        await Task.WhenAll(tasks);
        stopwatch.Stop();

        var totalSubscriptions = clientCount * subscriptionsPerClient;
        var subscribeTime = stopwatch.ElapsedMilliseconds;
        
        _output.WriteLine($"并发订阅 {totalSubscriptions:N0} 个主题耗时: {subscribeTime:N0} ms");
        _output.WriteLine($"平均每个订阅耗时: {(double)subscribeTime / totalSubscriptions:F3} ms");
        _output.WriteLine($"订阅吞吐量: {totalSubscriptions * 1000.0 / subscribeTime:F0} 订阅/秒");

        // 验证统计信息
        var statistics = await subscriptionManager.GetStatisticsAsync();
        _output.WriteLine($"订阅统计: 总订阅数={statistics.TotalSubscriptions:N0}, 活跃客户端={statistics.ActiveClients:N0}");

        Assert.Equal(totalSubscriptions, statistics.TotalSubscriptions);
        Assert.Equal(clientCount, statistics.ActiveClients);
    }

    [Fact]
    public async Task MessageDispatcher_High_Throughput_Test()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Warning));
        services.AddTopicSubscriptionSystem(configureDispatch: options =>
        {
            options.MaxConcurrentDispatches = 1000;
            options.EnableBatchOptimization = true;
        });

        var serviceProvider = services.BuildServiceProvider();
        var subscriptionManager = serviceProvider.GetRequiredService<ITopicSubscriptionManager>();
        var messageDispatcher = serviceProvider.GetRequiredService<IMessageDispatcher>();

        // 添加订阅者
        const int subscriberCount = 1000;
        for (int i = 0; i < subscriberCount; i++)
        {
            var client = CreateMockClient($"client{i}");
            var subscription = new MqttSubscription
            {
                TopicFilter = "broadcast/message",
                QoSLevel = MqttQoSLevel.AtMostOnce
            };
            await subscriptionManager.SubscribeAsync(client, subscription);
        }

        _output.WriteLine($"添加了 {subscriberCount:N0} 个订阅者到主题 'broadcast/message'");

        // Act - 高吞吐量消息分发测试
        const int messageCount = 100;
        var stopwatch = Stopwatch.StartNew();

        var dispatchTasks = Enumerable.Range(0, messageCount).Select(async messageIndex =>
        {
            var publishPacket = MqttPublishPacket.Create(
                "broadcast/message",
                System.Text.Encoding.UTF8.GetBytes($"Message {messageIndex}"),
                MqttQoSLevel.AtMostOnce);

            var result = await messageDispatcher.DispatchAsync(publishPacket);
            Assert.True(result.IsSuccess);
            Assert.Equal(subscriberCount, result.MatchedSubscribers);
            return result;
        });

        var results = await Task.WhenAll(dispatchTasks);
        stopwatch.Stop();

        var totalDispatches = results.Sum(r => r.SuccessfulDispatches);
        var dispatchTime = stopwatch.ElapsedMilliseconds;

        _output.WriteLine($"分发 {messageCount:N0} 条消息到 {subscriberCount:N0} 个订阅者耗时: {dispatchTime:N0} ms");
        _output.WriteLine($"总分发次数: {totalDispatches:N0}");
        _output.WriteLine($"平均每条消息分发耗时: {(double)dispatchTime / messageCount:F3} ms");
        _output.WriteLine($"消息分发吞吐量: {totalDispatches * 1000.0 / dispatchTime:F0} 分发/秒");

        // 验证分发统计
        var dispatchStats = await messageDispatcher.GetStatisticsAsync();
        _output.WriteLine($"分发统计: 总消息数={dispatchStats.TotalMessages:N0}, 成功率={dispatchStats.SuccessRate:F1}%");

        Assert.Equal(messageCount, dispatchStats.TotalMessages);
        Assert.True(dispatchStats.SuccessRate > 99.0);
    }

    private static TopicSubscriber CreateTestSubscriber(string clientId, string topicFilter, MqttQoSLevel qosLevel)
    {
        return new TopicSubscriber
        {
            ClientId = clientId,
            TopicFilter = topicFilter,
            QoSLevel = qosLevel,
            SubscribedAt = DateTime.UtcNow,
            Options = new MqttSubscriptionOptions(),
            Client = CreateMockClient(clientId)
        };
    }

    private static IMqttClient CreateMockClient(string clientId)
    {
        var mockClient = new Mock<IMqttClient>();
        mockClient.Setup(x => x.ClientId).Returns(clientId);
        mockClient.Setup(x => x.IsAuthenticated).Returns(true);
        mockClient.Setup(x => x.State).Returns(MqttClientState.Authenticated);
        mockClient.Setup(x => x.SendPacketAsync(It.IsAny<IMqttPacket>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);
        return mockClient.Object;
    }
}
