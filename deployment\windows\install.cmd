@echo off
REM MQTT Broker Windows 11 CMD 批处理安装脚本
REM 适用于命令提示符环境的一键安装方案

setlocal enabledelayedexpansion

REM 配置变量
set "INSTALL_PATH=C:\MqttBroker"
set "INSTALL_REDIS=false"
set "INSTALL_POSTGRESQL=false"
set "ENVIRONMENT=Development"

REM 颜色定义
set "COLOR_INFO=0A"
set "COLOR_WARN=0E"
set "COLOR_ERROR=0C"
set "COLOR_SUCCESS=02"

REM 解析命令行参数
:parse_args
if "%~1"=="" goto start_install
if /i "%~1"=="--install-path" (
    set "INSTALL_PATH=%~2"
    shift
    shift
    goto parse_args
)
if /i "%~1"=="--install-redis" (
    set "INSTALL_REDIS=true"
    shift
    goto parse_args
)
if /i "%~1"=="--install-postgresql" (
    set "INSTALL_POSTGRESQL=true"
    shift
    goto parse_args
)
if /i "%~1"=="--environment" (
    set "ENVIRONMENT=%~2"
    shift
    shift
    goto parse_args
)
if /i "%~1"=="--help" (
    goto show_help
)
shift
goto parse_args

:show_help
echo.
echo MQTT Broker Windows 安装脚本
echo.
echo 用法: install.cmd [选项]
echo.
echo 选项:
echo   --install-path PATH     安装路径 (默认: C:\MqttBroker)
echo   --install-redis         安装 Redis 缓存
echo   --install-postgresql    安装 PostgreSQL 数据库
echo   --environment ENV       环境 (Development/Testing/Production)
echo   --help                  显示此帮助信息
echo.
echo 示例:
echo   install.cmd
echo   install.cmd --install-path "D:\MqttBroker" --install-redis
echo.
goto end

:start_install
REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    color %COLOR_ERROR%
    echo [错误] 此脚本需要管理员权限运行
    echo 请以管理员身份运行命令提示符
    pause
    goto end
)

color %COLOR_INFO%
echo ========================================
echo MQTT Broker Windows 安装程序
echo ========================================
echo 安装路径: %INSTALL_PATH%
echo 环境: %ENVIRONMENT%
echo 安装 Redis: %INSTALL_REDIS%
echo 安装 PostgreSQL: %INSTALL_POSTGRESQL%
echo ========================================
echo.

REM 检查 .NET 8 运行时
echo [信息] 检查 .NET 8 运行时...
dotnet --version >nul 2>&1
if %errorLevel% neq 0 (
    echo [警告] .NET 8 运行时未安装，正在下载安装...
    call :install_dotnet
) else (
    for /f "tokens=1 delims=." %%a in ('dotnet --version') do set "DOTNET_MAJOR=%%a"
    if !DOTNET_MAJOR! geq 8 (
        echo [信息] .NET 8 运行时已安装
    ) else (
        echo [警告] .NET 版本过低，正在安装 .NET 8...
        call :install_dotnet
    )
)

REM 创建安装目录
echo [信息] 创建安装目录...
if exist "%INSTALL_PATH%" (
    echo [警告] 安装目录已存在，将清理旧文件
    rmdir /s /q "%INSTALL_PATH%\app" 2>nul
)

mkdir "%INSTALL_PATH%" 2>nul
mkdir "%INSTALL_PATH%\app" 2>nul
mkdir "%INSTALL_PATH%\data" 2>nul
mkdir "%INSTALL_PATH%\logs" 2>nul
mkdir "%INSTALL_PATH%\config" 2>nul
mkdir "%INSTALL_PATH%\scripts" 2>nul
mkdir "%INSTALL_PATH%\backup" 2>nul

REM 安装可选组件
if "%INSTALL_REDIS%"=="true" (
    call :install_redis
)

if "%INSTALL_POSTGRESQL%"=="true" (
    call :install_postgresql
)

REM 构建和发布应用程序
echo [信息] 构建和发布 MQTT Broker 应用程序...
cd /d "%~dp0..\.."

dotnet clean --configuration Release >nul 2>&1
if %errorLevel% neq 0 (
    echo [错误] 清理项目失败
    goto error_exit
)

dotnet restore >nul 2>&1
if %errorLevel% neq 0 (
    echo [错误] 还原依赖失败
    goto error_exit
)

dotnet publish src/MqttBroker.Host -c Release -o "%INSTALL_PATH%\app" --self-contained false --runtime win-x64 >nul 2>&1
if %errorLevel% neq 0 (
    echo [错误] 应用程序发布失败
    goto error_exit
)

REM 创建配置文件
echo [信息] 创建配置文件...
call :create_config_files

REM 创建管理脚本
echo [信息] 创建管理脚本...
call :create_management_scripts

REM 初始化数据库
echo [信息] 初始化数据库...
call :init_database

color %COLOR_SUCCESS%
echo.
echo ========================================
echo MQTT Broker 安装完成！
echo ========================================
echo 安装路径: %INSTALL_PATH%
echo 配置文件: %INSTALL_PATH%\app\appsettings.json
echo.
echo 下一步:
echo 1. 运行: %INSTALL_PATH%\scripts\start.cmd 启动服务
echo 2. 访问: http://localhost:9090/health 检查健康状态
echo 3. MQTT 连接: localhost:1883
echo 4. WebSocket: ws://localhost:8080/mqtt
echo.
pause
goto end

REM 安装 .NET 8 运行时
:install_dotnet
echo [信息] 下载 .NET 8 运行时...
powershell -Command "& {Invoke-WebRequest -Uri 'https://download.microsoft.com/download/6/0/f/60fc8c9b-2e8a-4d6e-b5b7-b1e5b5e5e5e5/dotnet-runtime-8.0.0-win-x64.exe' -OutFile '%TEMP%\dotnet-runtime-8.0-win-x64.exe'}" >nul 2>&1
if %errorLevel% neq 0 (
    echo [错误] 下载 .NET 运行时失败
    echo 请手动下载并安装 .NET 8 运行时
    echo 下载地址: https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    goto error_exit
)

echo [信息] 安装 .NET 8 运行时...
"%TEMP%\dotnet-runtime-8.0-win-x64.exe" /quiet
if %errorLevel% neq 0 (
    echo [错误] .NET 运行时安装失败
    goto error_exit
)

del "%TEMP%\dotnet-runtime-8.0-win-x64.exe" 2>nul
echo [信息] .NET 8 运行时安装成功
goto :eof

REM 安装 Redis
:install_redis
echo [信息] 安装 Redis...
REM 检查是否已安装 Chocolatey
choco --version >nul 2>&1
if %errorLevel% neq 0 (
    echo [信息] 安装 Chocolatey 包管理器...
    powershell -Command "& {Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))}" >nul 2>&1
)

choco install redis-64 -y >nul 2>&1
if %errorLevel% neq 0 (
    echo [警告] Redis 安装失败，将使用内存缓存
) else (
    echo [信息] Redis 安装成功
    net start redis >nul 2>&1
    sc config redis start= auto >nul 2>&1
)
goto :eof

REM 安装 PostgreSQL
:install_postgresql
echo [信息] 安装 PostgreSQL...
choco install postgresql --params "/Password:**********" -y >nul 2>&1
if %errorLevel% neq 0 (
    echo [警告] PostgreSQL 安装失败，将使用 SQLite
) else (
    echo [信息] PostgreSQL 安装成功
    timeout /t 10 /nobreak >nul
    
    REM 创建数据库和用户
    set PGPASSWORD=**********
    "C:\Program Files\PostgreSQL\15\bin\psql.exe" -U postgres -c "CREATE DATABASE mqtt_broker;" >nul 2>&1
    "C:\Program Files\PostgreSQL\15\bin\psql.exe" -U postgres -c "CREATE USER mqtt_user WITH PASSWORD '**********';" >nul 2>&1
    "C:\Program Files\PostgreSQL\15\bin\psql.exe" -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE mqtt_broker TO mqtt_user;" >nul 2>&1
)
goto :eof

REM 创建配置文件
:create_config_files
REM 根据安装的组件选择配置
if "%INSTALL_POSTGRESQL%"=="true" (
    set "CONNECTION_STRING=Host=localhost;Database=mqtt_broker;Username=mqtt_user;Password=**********;Pooling=true;MinPoolSize=5;MaxPoolSize=20"
    set "STORAGE_PROVIDER=PostgreSQL"
) else (
    set "CONNECTION_STRING=Data Source=%INSTALL_PATH:\=\\%\\data\\mqtt_broker.db"
    set "STORAGE_PROVIDER=SQLite"
)

if "%INSTALL_REDIS%"=="true" (
    set "REDIS_CONFIG=      \"Redis\": {        \"ConnectionString\": \"localhost:6379\",        \"Database\": 0,        \"KeyPrefix\": \"mqtt:\",        \"EnableCaching\": true      },"
) else (
    set "REDIS_CONFIG="
)

REM 创建 appsettings.json
(
echo {
echo   "Logging": {
echo     "LogLevel": {
echo       "Default": "Information",
echo       "Microsoft": "Warning",
echo       "MqttBroker": "Information"
echo     },
echo     "Console": {
echo       "IncludeScopes": true
echo     },
echo     "File": {
echo       "Path": "%INSTALL_PATH:\=\\%\\logs\\mqtt-broker-.log",
echo       "RollingInterval": "Day",
echo       "RetainedFileCountLimit": 7
echo     }
echo   },
echo   "MqttBroker": {
echo     "Network": {
echo       "Tcp": {
echo         "Enabled": true,
echo         "Port": 1883,
echo         "Address": "127.0.0.1"
echo       },
echo       "WebSocket": {
echo         "Enabled": true,
echo         "Port": 8080
echo       },
echo       "Connection": {
echo         "MaxConnections": 1000
echo       }
echo     },
echo     "Storage": {
echo       "Provider": "!STORAGE_PROVIDER!",
echo       "ConnectionString": "!CONNECTION_STRING!",
echo       "EnablePersistence": true!REDIS_CONFIG!
echo     },
echo     "Security": {
echo       "AllowAnonymous": true,
echo       "RequireAuthentication": false
echo     },
echo     "HealthChecks": {
echo       "Enabled": true,
echo       "Port": 9090,
echo       "Path": "/health"
echo     }
echo   }
echo }
) > "%INSTALL_PATH%\app\appsettings.json"

goto :eof

REM 创建管理脚本
:create_management_scripts
REM 创建启动脚本
(
echo @echo off
echo echo 启动 MQTT Broker...
echo cd /d "%INSTALL_PATH%\app"
echo start "MQTT Broker" MqttBroker.Host.exe
echo echo MQTT Broker 已启动
echo echo 访问地址:
echo echo   MQTT TCP: localhost:1883
echo echo   WebSocket: ws://localhost:8080/mqtt
echo echo   健康检查: http://localhost:9090/health
echo pause
) > "%INSTALL_PATH%\scripts\start.cmd"

REM 创建停止脚本
(
echo @echo off
echo echo 停止 MQTT Broker...
echo taskkill /f /im MqttBroker.Host.exe >nul 2>&1
echo if %%errorLevel%% equ 0 (
echo     echo MQTT Broker 已停止
echo ^) else (
echo     echo MQTT Broker 未在运行
echo ^)
echo pause
) > "%INSTALL_PATH%\scripts\stop.cmd"

REM 创建状态检查脚本
(
echo @echo off
echo echo 检查 MQTT Broker 状态...
echo tasklist /fi "imagename eq MqttBroker.Host.exe" ^| find /i "MqttBroker.Host.exe" >nul
echo if %%errorLevel%% equ 0 (
echo     echo [状态] MQTT Broker 正在运行
echo     echo [健康检查] 正在检查...
echo     powershell -Command "try { $$response = Invoke-WebRequest -Uri 'http://localhost:9090/health' -UseBasicParsing -TimeoutSec 5; Write-Host '[健康状态]' $$response.StatusCode } catch { Write-Host '[健康状态] 无响应' }"
echo ^) else (
echo     echo [状态] MQTT Broker 未运行
echo ^)
echo pause
) > "%INSTALL_PATH%\scripts\status.cmd"

goto :eof

REM 初始化数据库
:init_database
if "%STORAGE_PROVIDER%"=="SQLite" (
    echo [信息] 初始化 SQLite 数据库...
    copy "%~dp0init-database.sql" "%INSTALL_PATH%\data\" >nul 2>&1
)
goto :eof

:error_exit
color %COLOR_ERROR%
echo [错误] 安装过程中发生错误
pause
goto end

:end
endlocal
