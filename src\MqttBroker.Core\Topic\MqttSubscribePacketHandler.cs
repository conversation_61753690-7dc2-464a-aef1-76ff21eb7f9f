using Microsoft.Extensions.Logging;
using MqttBroker.Core.Client;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Network;

namespace MqttBroker.Core.Topic;

/// <summary>
/// MQTT SUBSCRIBE 数据包处理器
/// </summary>
public class MqttSubscribePacketHandler : IPacketHandler
{
    private readonly ITopicSubscriptionManager _subscriptionManager;
    private readonly IMqttClientManager _clientManager;
    private readonly ILogger<MqttSubscribePacketHandler> _logger;

    /// <summary>
    /// 支持的数据包类型
    /// </summary>
    public MqttPacketType PacketType => MqttPacketType.Subscribe;

    /// <summary>
    /// 初始化 SUBSCRIBE 数据包处理器
    /// </summary>
    /// <param name="subscriptionManager">订阅管理器</param>
    /// <param name="clientManager">客户端管理器</param>
    /// <param name="logger">日志记录器</param>
    public MqttSubscribePacketHandler(
        ITopicSubscriptionManager subscriptionManager,
        IMqttClientManager clientManager,
        ILogger<MqttSubscribePacketHandler> logger)
    {
        _subscriptionManager = subscriptionManager ?? throw new ArgumentNullException(nameof(subscriptionManager));
        _clientManager = clientManager ?? throw new ArgumentNullException(nameof(clientManager));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 处理 SUBSCRIBE 数据包
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="packet">数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    public async Task HandleAsync(IClientConnection connection, IMqttPacket packet, CancellationToken cancellationToken = default)
    {
        if (packet is not MqttSubscribePacket subscribePacket)
        {
            _logger.LogWarning("Invalid packet type for SUBSCRIBE handler: {PacketType}", packet.GetType().Name);
            return;
        }

        try
        {
            // 获取客户端
            var client = _clientManager.GetClientByConnectionId(connection.Id);
            if (client == null)
            {
                _logger.LogWarning("Client not found for connection: {ConnectionId}", connection.Id);
                await SendSubAckAsync(connection, subscribePacket.PacketIdentifier ?? 0,
                    subscribePacket.Subscriptions.Select(_ => MqttReasonCode.NotAuthorized).ToArray(), cancellationToken);
                return;
            }

            // 检查客户端是否已认证
            if (!client.IsAuthenticated)
            {
                _logger.LogWarning("Unauthenticated client {ClientId} attempted to subscribe", client.ClientId);
                await SendSubAckAsync(connection, subscribePacket.PacketIdentifier ?? 0,
                    subscribePacket.Subscriptions.Select(_ => MqttReasonCode.NotAuthorized).ToArray(), cancellationToken);
                return;
            }

            _logger.LogDebug("Processing SUBSCRIBE packet from client {ClientId} with {Count} subscriptions", 
                client.ClientId, subscribePacket.Subscriptions.Count);

            // 处理所有订阅
            var subscriptionResults = await _subscriptionManager.SubscribeAsync(client, subscribePacket.Subscriptions, cancellationToken);

            // 创建 SUBACK 响应
            var reasonCodes = subscriptionResults.Select(r => r.ReasonCode).ToArray();
            await SendSubAckAsync(connection, subscribePacket.PacketIdentifier ?? 0, reasonCodes, cancellationToken);

            // 记录订阅结果
            var successCount = subscriptionResults.Count(r => r.IsSuccess);
            var failureCount = subscriptionResults.Count - successCount;

            _logger.LogInformation("Client {ClientId} subscription results: {SuccessCount} successful, {FailureCount} failed", 
                client.ClientId, successCount, failureCount);

            // 更新客户端活动时间
            client.UpdateLastActivity();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling SUBSCRIBE packet from connection: {ConnectionId}", connection.Id);
            
            // 发送错误响应
            var errorReasonCodes = subscribePacket.Subscriptions.Select(_ => MqttReasonCode.UnspecifiedError).ToArray();
            await SendSubAckAsync(connection, subscribePacket.PacketIdentifier ?? 0, errorReasonCodes, cancellationToken);
        }
    }

    /// <summary>
    /// 发送 SUBACK 数据包
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="packetIdentifier">数据包标识符</param>
    /// <param name="reasonCodes">原因码数组</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    private async Task SendSubAckAsync(IClientConnection connection, ushort packetIdentifier, 
        MqttReasonCode[] reasonCodes, CancellationToken cancellationToken)
    {
        try
        {
            var subAckPacket = MqttSubAckPacket.Create(packetIdentifier, reasonCodes);
            await connection.SendPacketAsync(subAckPacket, cancellationToken);
            
            _logger.LogDebug("Sent SUBACK packet to connection {ConnectionId} with {Count} reason codes", 
                connection.Id, reasonCodes.Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending SUBACK packet to connection: {ConnectionId}", connection.Id);
        }
    }
}

/// <summary>
/// MQTT UNSUBSCRIBE 数据包处理器
/// </summary>
public class MqttUnsubscribePacketHandler : IPacketHandler
{
    private readonly ITopicSubscriptionManager _subscriptionManager;
    private readonly IMqttClientManager _clientManager;
    private readonly ILogger<MqttUnsubscribePacketHandler> _logger;

    /// <summary>
    /// 支持的数据包类型
    /// </summary>
    public MqttPacketType PacketType => MqttPacketType.Unsubscribe;

    /// <summary>
    /// 初始化 UNSUBSCRIBE 数据包处理器
    /// </summary>
    /// <param name="subscriptionManager">订阅管理器</param>
    /// <param name="clientManager">客户端管理器</param>
    /// <param name="logger">日志记录器</param>
    public MqttUnsubscribePacketHandler(
        ITopicSubscriptionManager subscriptionManager,
        IMqttClientManager clientManager,
        ILogger<MqttUnsubscribePacketHandler> logger)
    {
        _subscriptionManager = subscriptionManager ?? throw new ArgumentNullException(nameof(subscriptionManager));
        _clientManager = clientManager ?? throw new ArgumentNullException(nameof(clientManager));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 处理 UNSUBSCRIBE 数据包
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="packet">数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    public async Task HandleAsync(IClientConnection connection, IMqttPacket packet, CancellationToken cancellationToken = default)
    {
        if (packet is not MqttUnsubscribePacket unsubscribePacket)
        {
            _logger.LogWarning("Invalid packet type for UNSUBSCRIBE handler: {PacketType}", packet.GetType().Name);
            return;
        }

        try
        {
            // 获取客户端
            var client = _clientManager.GetClientByConnectionId(connection.Id);
            if (client == null)
            {
                _logger.LogWarning("Client not found for connection: {ConnectionId}", connection.Id);
                await SendUnsubAckAsync(connection, unsubscribePacket.PacketIdentifier ?? 0,
                    unsubscribePacket.TopicFilters.Select(_ => MqttReasonCode.NotAuthorized).ToArray(), cancellationToken);
                return;
            }

            // 检查客户端是否已认证
            if (!client.IsAuthenticated)
            {
                _logger.LogWarning("Unauthenticated client {ClientId} attempted to unsubscribe", client.ClientId);
                await SendUnsubAckAsync(connection, unsubscribePacket.PacketIdentifier ?? 0,
                    unsubscribePacket.TopicFilters.Select(_ => MqttReasonCode.NotAuthorized).ToArray(), cancellationToken);
                return;
            }

            _logger.LogDebug("Processing UNSUBSCRIBE packet from client {ClientId} with {Count} topic filters", 
                client.ClientId, unsubscribePacket.TopicFilters.Count);

            // 处理所有取消订阅
            var unsubscriptionResults = await _subscriptionManager.UnsubscribeAsync(client, unsubscribePacket.TopicFilters, cancellationToken);

            // 创建 UNSUBACK 响应
            var reasonCodes = unsubscriptionResults.Select(r => r.ReasonCode).ToArray();
            await SendUnsubAckAsync(connection, unsubscribePacket.PacketIdentifier ?? 0, reasonCodes, cancellationToken);

            // 记录取消订阅结果
            var successCount = unsubscriptionResults.Count(r => r.IsSuccess);
            var failureCount = unsubscriptionResults.Count - successCount;

            _logger.LogInformation("Client {ClientId} unsubscription results: {SuccessCount} successful, {FailureCount} failed", 
                client.ClientId, successCount, failureCount);

            // 更新客户端活动时间
            client.UpdateLastActivity();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling UNSUBSCRIBE packet from connection: {ConnectionId}", connection.Id);
            
            // 发送错误响应
            var errorReasonCodes = unsubscribePacket.TopicFilters.Select(_ => MqttReasonCode.UnspecifiedError).ToArray();
            await SendUnsubAckAsync(connection, unsubscribePacket.PacketIdentifier ?? 0, errorReasonCodes, cancellationToken);
        }
    }

    /// <summary>
    /// 发送 UNSUBACK 数据包
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="packetIdentifier">数据包标识符</param>
    /// <param name="reasonCodes">原因码数组</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    private async Task SendUnsubAckAsync(IClientConnection connection, ushort packetIdentifier, 
        MqttReasonCode[] reasonCodes, CancellationToken cancellationToken)
    {
        try
        {
            var unsubAckPacket = MqttUnsubAckPacket.Create(packetIdentifier, reasonCodes);
            await connection.SendPacketAsync(unsubAckPacket, cancellationToken);
            
            _logger.LogDebug("Sent UNSUBACK packet to connection {ConnectionId} with {Count} reason codes", 
                connection.Id, reasonCodes.Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending UNSUBACK packet to connection: {ConnectionId}", connection.Id);
        }
    }
}
