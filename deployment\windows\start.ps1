# MQTT Broker Windows 启动和管理脚本
# 提供启动、停止、重启、状态检查等功能

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("start", "stop", "restart", "status", "install-service", "uninstall-service", "logs", "health")]
    [string]$Action = "start",
    
    [Parameter(Mandatory=$false)]
    [string]$InstallPath = "C:\MqttBroker",
    
    [Parameter(Mandatory=$false)]
    [switch]$AsService,
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose
)

# 全局变量
$ServiceName = "MqttBrokerService"
$ProcessName = "MqttBroker.Host"
$AppPath = "$InstallPath\app"
$LogPath = "$InstallPath\logs"

# 日志函数
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warn {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Debug {
    param([string]$Message)
    if ($Verbose) {
        Write-Host "[DEBUG] $Message" -ForegroundColor Blue
    }
}

# 检查管理员权限
function Test-AdminRights {
    return ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
}

# 检查应用程序是否存在
function Test-Application {
    $exePath = "$AppPath\MqttBroker.Host.exe"
    if (-not (Test-Path $exePath)) {
        Write-Error "应用程序未找到: $exePath"
        Write-Error "请先运行 install.ps1 安装应用程序"
        return $false
    }
    return $true
}

# 获取进程状态
function Get-ProcessStatus {
    $processes = Get-Process -Name $ProcessName -ErrorAction SilentlyContinue
    if ($processes) {
        return @{
            IsRunning = $true
            ProcessId = $processes[0].Id
            StartTime = $processes[0].StartTime
            WorkingSet = [math]::Round($processes[0].WorkingSet64 / 1MB, 2)
        }
    } else {
        return @{
            IsRunning = $false
            ProcessId = $null
            StartTime = $null
            WorkingSet = 0
        }
    }
}

# 获取服务状态
function Get-ServiceStatus {
    $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
    if ($service) {
        return @{
            IsInstalled = $true
            Status = $service.Status
            StartType = $service.StartType
        }
    } else {
        return @{
            IsInstalled = $false
            Status = "NotInstalled"
            StartType = "Unknown"
        }
    }
}

# 启动应用程序
function Start-Application {
    if (-not (Test-Application)) {
        return
    }
    
    $status = Get-ProcessStatus
    if ($status.IsRunning) {
        Write-Warn "MQTT Broker 已在运行 (PID: $($status.ProcessId))"
        return
    }
    
    if ($AsService) {
        Start-AsService
    } else {
        Start-AsProcess
    }
}

# 作为进程启动
function Start-AsProcess {
    Write-Info "启动 MQTT Broker 进程..."
    
    try {
        $exePath = "$AppPath\MqttBroker.Host.exe"
        $workingDir = $AppPath
        
        # 设置环境变量
        $env:ASPNETCORE_ENVIRONMENT = "Development"
        $env:DOTNET_ENVIRONMENT = "Development"
        
        # 启动进程
        $process = Start-Process -FilePath $exePath -WorkingDirectory $workingDir -PassThru -WindowStyle Hidden
        
        # 等待启动
        Start-Sleep -Seconds 3
        
        # 检查状态
        $status = Get-ProcessStatus
        if ($status.IsRunning) {
            Write-Info "MQTT Broker 启动成功"
            Write-Info "进程 ID: $($status.ProcessId)"
            Write-Info "内存使用: $($status.WorkingSet) MB"
            Write-Info "MQTT 端口: 1883"
            Write-Info "WebSocket 端口: 8080"
            Write-Info "健康检查: http://localhost:9090/health"
        } else {
            Write-Error "MQTT Broker 启动失败"
        }
    }
    catch {
        Write-Error "启动过程中发生错误: $_"
    }
}

# 作为服务启动
function Start-AsService {
    $serviceStatus = Get-ServiceStatus
    if (-not $serviceStatus.IsInstalled) {
        Write-Error "Windows 服务未安装，请先运行: .\start.ps1 install-service"
        return
    }
    
    Write-Info "启动 MQTT Broker 服务..."
    
    try {
        Start-Service -Name $ServiceName
        Start-Sleep -Seconds 3
        
        $serviceStatus = Get-ServiceStatus
        if ($serviceStatus.Status -eq "Running") {
            Write-Info "MQTT Broker 服务启动成功"
        } else {
            Write-Error "MQTT Broker 服务启动失败，状态: $($serviceStatus.Status)"
        }
    }
    catch {
        Write-Error "启动服务时发生错误: $_"
    }
}

# 停止应用程序
function Stop-Application {
    if ($AsService) {
        Stop-AsService
    } else {
        Stop-AsProcess
    }
}

# 停止进程
function Stop-AsProcess {
    $status = Get-ProcessStatus
    if (-not $status.IsRunning) {
        Write-Info "MQTT Broker 未在运行"
        return
    }
    
    Write-Info "停止 MQTT Broker 进程 (PID: $($status.ProcessId))..."
    
    try {
        Stop-Process -Id $status.ProcessId -Force
        Start-Sleep -Seconds 2
        
        $status = Get-ProcessStatus
        if (-not $status.IsRunning) {
            Write-Info "MQTT Broker 已停止"
        } else {
            Write-Warn "进程可能仍在运行，请检查"
        }
    }
    catch {
        Write-Error "停止进程时发生错误: $_"
    }
}

# 停止服务
function Stop-AsService {
    $serviceStatus = Get-ServiceStatus
    if (-not $serviceStatus.IsInstalled) {
        Write-Error "Windows 服务未安装"
        return
    }
    
    if ($serviceStatus.Status -ne "Running") {
        Write-Info "MQTT Broker 服务未在运行"
        return
    }
    
    Write-Info "停止 MQTT Broker 服务..."
    
    try {
        Stop-Service -Name $ServiceName -Force
        Start-Sleep -Seconds 3
        
        $serviceStatus = Get-ServiceStatus
        Write-Info "MQTT Broker 服务已停止，状态: $($serviceStatus.Status)"
    }
    catch {
        Write-Error "停止服务时发生错误: $_"
    }
}

# 重启应用程序
function Restart-Application {
    Write-Info "重启 MQTT Broker..."
    Stop-Application
    Start-Sleep -Seconds 2
    Start-Application
}

# 显示状态
function Show-Status {
    Write-Info "=== MQTT Broker 状态 ==="
    
    # 进程状态
    $processStatus = Get-ProcessStatus
    Write-Host "进程状态: " -NoNewline
    if ($processStatus.IsRunning) {
        Write-Host "运行中" -ForegroundColor Green
        Write-Host "  进程 ID: $($processStatus.ProcessId)"
        Write-Host "  启动时间: $($processStatus.StartTime)"
        Write-Host "  内存使用: $($processStatus.WorkingSet) MB"
    } else {
        Write-Host "未运行" -ForegroundColor Red
    }
    
    # 服务状态
    $serviceStatus = Get-ServiceStatus
    Write-Host "服务状态: " -NoNewline
    if ($serviceStatus.IsInstalled) {
        if ($serviceStatus.Status -eq "Running") {
            Write-Host "$($serviceStatus.Status)" -ForegroundColor Green
        } else {
            Write-Host "$($serviceStatus.Status)" -ForegroundColor Yellow
        }
        Write-Host "  启动类型: $($serviceStatus.StartType)"
    } else {
        Write-Host "未安装" -ForegroundColor Gray
    }
    
    # 端口状态
    Write-Host "端口状态:"
    $ports = @(1883, 8080, 9090)
    foreach ($port in $ports) {
        $connection = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
        if ($connection) {
            Write-Host "  端口 $port : " -NoNewline
            Write-Host "监听中" -ForegroundColor Green
        } else {
            Write-Host "  端口 $port : " -NoNewline
            Write-Host "未监听" -ForegroundColor Red
        }
    }
    
    # 健康检查
    if ($processStatus.IsRunning) {
        Write-Host "健康检查: " -NoNewline
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:9090/health" -TimeoutSec 5 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Host "健康" -ForegroundColor Green
            } else {
                Write-Host "异常 (HTTP $($response.StatusCode))" -ForegroundColor Yellow
            }
        }
        catch {
            Write-Host "无响应" -ForegroundColor Red
        }
    }
}

# 安装 Windows 服务
function Install-WindowsService {
    if (-not (Test-AdminRights)) {
        Write-Error "安装服务需要管理员权限"
        return
    }
    
    if (-not (Test-Application)) {
        return
    }
    
    $serviceStatus = Get-ServiceStatus
    if ($serviceStatus.IsInstalled) {
        Write-Warn "Windows 服务已安装"
        return
    }
    
    Write-Info "安装 MQTT Broker Windows 服务..."
    
    try {
        $exePath = "$AppPath\MqttBroker.Host.exe"
        $serviceName = $ServiceName
        $displayName = "MQTT Broker Service"
        $description = "High-performance MQTT Broker for IoT applications"
        
        # 使用 sc.exe 创建服务
        $result = & sc.exe create $serviceName binPath= $exePath DisplayName= $displayName start= auto
        
        if ($LASTEXITCODE -eq 0) {
            # 设置服务描述
            & sc.exe description $serviceName $description
            
            Write-Info "Windows 服务安装成功"
            Write-Info "服务名称: $serviceName"
            Write-Info "显示名称: $displayName"
            Write-Info "启动类型: 自动"
        } else {
            Write-Error "服务安装失败"
        }
    }
    catch {
        Write-Error "安装服务时发生错误: $_"
    }
}

# 卸载 Windows 服务
function Uninstall-WindowsService {
    if (-not (Test-AdminRights)) {
        Write-Error "卸载服务需要管理员权限"
        return
    }
    
    $serviceStatus = Get-ServiceStatus
    if (-not $serviceStatus.IsInstalled) {
        Write-Info "Windows 服务未安装"
        return
    }
    
    # 先停止服务
    if ($serviceStatus.Status -eq "Running") {
        Write-Info "停止服务..."
        Stop-Service -Name $ServiceName -Force
        Start-Sleep -Seconds 3
    }
    
    Write-Info "卸载 MQTT Broker Windows 服务..."
    
    try {
        $result = & sc.exe delete $ServiceName
        
        if ($LASTEXITCODE -eq 0) {
            Write-Info "Windows 服务卸载成功"
        } else {
            Write-Error "服务卸载失败"
        }
    }
    catch {
        Write-Error "卸载服务时发生错误: $_"
    }
}

# 查看日志
function Show-Logs {
    $logFiles = Get-ChildItem -Path $LogPath -Filter "*.log" | Sort-Object LastWriteTime -Descending
    
    if ($logFiles.Count -eq 0) {
        Write-Warn "未找到日志文件"
        return
    }
    
    $latestLog = $logFiles[0]
    Write-Info "显示最新日志文件: $($latestLog.Name)"
    Write-Info "文件大小: $([math]::Round($latestLog.Length / 1KB, 2)) KB"
    Write-Info "修改时间: $($latestLog.LastWriteTime)"
    Write-Info "=" * 50
    
    # 显示最后 50 行
    Get-Content -Path $latestLog.FullName -Tail 50
}

# 健康检查
function Test-Health {
    Write-Info "执行健康检查..."
    
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:9090/health" -TimeoutSec 10
        
        Write-Info "健康检查结果:"
        Write-Host "  状态: " -NoNewline
        if ($response.status -eq "Healthy") {
            Write-Host $response.status -ForegroundColor Green
        } else {
            Write-Host $response.status -ForegroundColor Yellow
        }
        
        if ($response.checks) {
            Write-Host "  详细检查:"
            foreach ($check in $response.checks) {
                Write-Host "    $($check.name): " -NoNewline
                if ($check.status -eq "Healthy") {
                    Write-Host $check.status -ForegroundColor Green
                } else {
                    Write-Host $check.status -ForegroundColor Red
                }
            }
        }
    }
    catch {
        Write-Error "健康检查失败: $_"
    }
}

# 主函数
function Main {
    Write-Info "MQTT Broker Windows 管理工具"
    
    switch ($Action.ToLower()) {
        "start" { Start-Application }
        "stop" { Stop-Application }
        "restart" { Restart-Application }
        "status" { Show-Status }
        "install-service" { Install-WindowsService }
        "uninstall-service" { Uninstall-WindowsService }
        "logs" { Show-Logs }
        "health" { Test-Health }
        default {
            Write-Error "未知操作: $Action"
            Write-Info "可用操作: start, stop, restart, status, install-service, uninstall-service, logs, health"
        }
    }
}

# 执行主函数
Main
