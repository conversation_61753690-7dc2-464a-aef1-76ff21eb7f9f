using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;

namespace MqttBroker.Core.Message;

/// <summary>
/// 内存消息持久化服务实现
/// </summary>
public class InMemoryMessagePersistenceService : IMessagePersistenceService
{
    private readonly ILogger<InMemoryMessagePersistenceService> _logger;
    private readonly ConcurrentDictionary<string, ConcurrentQueue<OfflineMessage>> _offlineMessages;
    private readonly ConcurrentDictionary<string, OfflineMessage> _messageIndex;
    private readonly object _statsLock = new();
    private long _totalStoredMessages;
    private long _totalDeliveredMessages;

    /// <summary>
    /// 初始化内存消息持久化服务
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public InMemoryMessagePersistenceService(ILogger<InMemoryMessagePersistenceService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _offlineMessages = new ConcurrentDictionary<string, ConcurrentQueue<OfflineMessage>>();
        _messageIndex = new ConcurrentDictionary<string, OfflineMessage>();
    }

    /// <summary>
    /// 存储离线消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="publishPacket">发布数据包</param>
    /// <param name="qosLevel">QoS级别</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>存储结果</returns>
    public Task<MessageStorageResult> StoreOfflineMessageAsync(string clientId, MqttPublishPacket publishPacket, MqttQoSLevel qosLevel, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(clientId))
            throw new ArgumentException("Client ID cannot be null or empty", nameof(clientId));

        if (publishPacket == null)
            throw new ArgumentNullException(nameof(publishPacket));

        try
        {
            var messageId = Guid.NewGuid().ToString();
            var now = DateTime.UtcNow;

            var offlineMessage = new OfflineMessage
            {
                MessageId = messageId,
                ClientId = clientId,
                TopicName = publishPacket.Topic,
                Payload = publishPacket.Payload,
                QoSLevel = qosLevel,
                Retain = publishPacket.Retain,
                PacketIdentifier = publishPacket.PacketIdentifier,
                StoredAt = now,
                ExpiresAt = now.AddHours(24), // 默认24小时过期
                RetryCount = 0
            };

            // 获取或创建客户端消息队列
            var clientQueue = _offlineMessages.GetOrAdd(clientId, _ => new ConcurrentQueue<OfflineMessage>());
            clientQueue.Enqueue(offlineMessage);

            // 添加到消息索引
            _messageIndex[messageId] = offlineMessage;

            lock (_statsLock)
            {
                _totalStoredMessages++;
            }

            _logger.LogTrace("Stored offline message {MessageId} for client: {ClientId}, topic: {Topic}", 
                messageId, clientId, publishPacket.Topic);

            return Task.FromResult(MessageStorageResult.Success(messageId, now));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error storing offline message for client: {ClientId}", clientId);
            return Task.FromResult(MessageStorageResult.Failure(ex.Message));
        }
    }

    /// <summary>
    /// 获取离线消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="maxMessages">最大消息数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>离线消息列表</returns>
    public Task<IList<OfflineMessage>> GetOfflineMessagesAsync(string clientId, int maxMessages = 100, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(clientId))
            return Task.FromResult<IList<OfflineMessage>>(new List<OfflineMessage>());

        try
        {
            if (!_offlineMessages.TryGetValue(clientId, out var clientQueue))
                return Task.FromResult<IList<OfflineMessage>>(new List<OfflineMessage>());

            var messages = new List<OfflineMessage>();
            var count = 0;

            while (count < maxMessages && clientQueue.TryPeek(out var message))
            {
                // 检查消息是否过期
                if (message.ExpiresAt.HasValue && message.ExpiresAt.Value < DateTime.UtcNow)
                {
                    // 移除过期消息
                    if (clientQueue.TryDequeue(out var expiredMessage))
                    {
                        _messageIndex.TryRemove(expiredMessage.MessageId, out _);
                        _logger.LogTrace("Removed expired offline message {MessageId} for client: {ClientId}", 
                            expiredMessage.MessageId, clientId);
                    }
                    continue;
                }

                messages.Add(message);
                count++;

                // 移动到下一个消息（不出队，等待确认后再删除）
                break;
            }

            _logger.LogTrace("Retrieved {Count} offline messages for client: {ClientId}", messages.Count, clientId);
            return Task.FromResult<IList<OfflineMessage>>(messages);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting offline messages for client: {ClientId}", clientId);
            return Task.FromResult<IList<OfflineMessage>>(new List<OfflineMessage>());
        }
    }

    /// <summary>
    /// 删除离线消息
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功删除</returns>
    public Task<bool> DeleteOfflineMessageAsync(string messageId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(messageId))
            return Task.FromResult(false);

        try
        {
            if (!_messageIndex.TryRemove(messageId, out var message))
                return Task.FromResult(false);

            // 从客户端队列中移除（这里简化处理，实际实现可能需要更复杂的逻辑）
            if (_offlineMessages.TryGetValue(message.ClientId, out var clientQueue))
            {
                // 注意：ConcurrentQueue不支持直接删除特定元素，这里简化处理
                // 实际实现中可能需要使用其他数据结构
            }

            lock (_statsLock)
            {
                _totalDeliveredMessages++;
            }

            _logger.LogTrace("Deleted offline message {MessageId} for client: {ClientId}", messageId, message.ClientId);
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting offline message: {MessageId}", messageId);
            return Task.FromResult(false);
        }
    }

    /// <summary>
    /// 批量删除离线消息
    /// </summary>
    /// <param name="messageIds">消息ID列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除结果</returns>
    public async Task<BatchDeleteResult> DeleteOfflineMessagesAsync(IList<string> messageIds, CancellationToken cancellationToken = default)
    {
        if (messageIds == null || messageIds.Count == 0)
            return new BatchDeleteResult { TotalCount = 0, SuccessCount = 0, FailedCount = 0 };

        var result = new BatchDeleteResult
        {
            TotalCount = messageIds.Count,
            FailedMessageIds = new List<string>()
        };

        foreach (var messageId in messageIds)
        {
            var deleted = await DeleteOfflineMessageAsync(messageId, cancellationToken);
            if (deleted)
            {
                result.SuccessCount++;
            }
            else
            {
                result.FailedCount++;
                result.FailedMessageIds.Add(messageId);
            }
        }

        return result;
    }

    /// <summary>
    /// 清理过期消息
    /// </summary>
    /// <param name="expiredBefore">过期时间点</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理结果</returns>
    public Task<MessageCleanupResult> CleanupExpiredMessagesAsync(DateTime expiredBefore, CancellationToken cancellationToken = default)
    {
        var cleanedCount = 0;
        var startTime = DateTime.UtcNow;

        try
        {
            var expiredMessageIds = new List<string>();

            // 查找过期消息
            foreach (var kvp in _messageIndex)
            {
                var message = kvp.Value;
                if (message.ExpiresAt.HasValue && message.ExpiresAt.Value < expiredBefore)
                {
                    expiredMessageIds.Add(message.MessageId);
                }
            }

            // 删除过期消息
            foreach (var messageId in expiredMessageIds)
            {
                if (_messageIndex.TryRemove(messageId, out var message))
                {
                    cleanedCount++;
                    _logger.LogTrace("Cleaned up expired message {MessageId} for client: {ClientId}", 
                        messageId, message.ClientId);
                }
            }

            var elapsed = DateTime.UtcNow - startTime;

            _logger.LogInformation("Cleaned up {Count} expired messages in {ElapsedMs}ms", 
                cleanedCount, elapsed.TotalMilliseconds);

            return Task.FromResult(new MessageCleanupResult
            {
                IsSuccess = true,
                CleanedCount = cleanedCount,
                ElapsedMilliseconds = (long)elapsed.TotalMilliseconds
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up expired messages");
            
            return Task.FromResult(new MessageCleanupResult
            {
                IsSuccess = false,
                CleanedCount = cleanedCount,
                ErrorMessage = ex.Message
            });
        }
    }

    /// <summary>
    /// 获取客户端离线消息统计
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>统计信息</returns>
    public Task<ClientMessageStatistics> GetClientMessageStatisticsAsync(string clientId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(clientId))
            return Task.FromResult(new ClientMessageStatistics { ClientId = clientId });

        try
        {
            var stats = new ClientMessageStatistics { ClientId = clientId };

            if (_offlineMessages.TryGetValue(clientId, out var clientQueue))
            {
                var messages = clientQueue.ToArray();
                stats.OfflineMessageCount = messages.Length;
                stats.TotalMessageSize = messages.Sum(m => m.Payload.Length);

                if (messages.Length > 0)
                {
                    stats.EarliestMessageTime = messages.Min(m => m.StoredAt);
                    stats.LatestMessageTime = messages.Max(m => m.StoredAt);

                    // 按QoS级别分组统计
                    stats.MessagesByQoS = messages
                        .GroupBy(m => m.QoSLevel)
                        .ToDictionary(g => g.Key, g => g.Count());
                }
            }

            return Task.FromResult(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting client message statistics for: {ClientId}", clientId);
            return Task.FromResult(new ClientMessageStatistics { ClientId = clientId });
        }
    }

    /// <summary>
    /// 获取全局消息统计
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>全局统计信息</returns>
    public Task<GlobalMessageStatistics> GetGlobalMessageStatisticsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var stats = new GlobalMessageStatistics();
            var today = DateTime.UtcNow.Date;

            lock (_statsLock)
            {
                stats.TodayStoredMessages = _totalStoredMessages;
                stats.TodayDeliveredMessages = _totalDeliveredMessages;
            }

            stats.ClientsWithOfflineMessages = _offlineMessages.Count;
            stats.TotalOfflineMessages = _messageIndex.Count;

            if (_messageIndex.Count > 0)
            {
                var allMessages = _messageIndex.Values.ToArray();
                stats.TotalMessageSize = allMessages.Sum(m => m.Payload.Length);
                stats.AverageMessagesPerClient = stats.ClientsWithOfflineMessages > 0 
                    ? (double)stats.TotalOfflineMessages / stats.ClientsWithOfflineMessages 
                    : 0;

                // 按QoS级别分组统计
                stats.MessagesByQoS = allMessages
                    .GroupBy(m => m.QoSLevel)
                    .ToDictionary(g => g.Key, g => (long)g.Count());
            }

            return Task.FromResult(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting global message statistics");
            return Task.FromResult(new GlobalMessageStatistics());
        }
    }
}
