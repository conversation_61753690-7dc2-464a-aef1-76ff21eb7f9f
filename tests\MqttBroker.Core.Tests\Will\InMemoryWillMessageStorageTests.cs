using Microsoft.Extensions.Logging;
using Moq;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Will;
using Xunit;

namespace MqttBroker.Core.Tests.Will;

/// <summary>
/// 内存遗嘱消息存储测试
/// </summary>
public class InMemoryWillMessageStorageTests : IDisposable
{
    private readonly Mock<ILogger<InMemoryWillMessageStorage>> _mockLogger;
    private readonly InMemoryWillMessageStorage _storage;

    public InMemoryWillMessageStorageTests()
    {
        _mockLogger = new Mock<ILogger<InMemoryWillMessageStorage>>();
        _storage = new InMemoryWillMessageStorage(_mockLogger.Object);
    }

    [Fact]
    public async Task StoreWillMessageAsync_ValidRegistration_ShouldReturnSuccess()
    {
        // Arrange
        var registration = CreateTestRegistration("test-client", "test/topic");

        // Act
        var result = await _storage.StoreWillMessageAsync(registration);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal("test-client", result.ClientId);
        Assert.Equal(WillMessageStorageOperation.Store, result.Operation);
        Assert.Null(result.ErrorMessage);
    }

    [Fact]
    public async Task StoreWillMessageAsync_NullRegistration_ShouldReturnFailure()
    {
        // Act
        var result = await _storage.StoreWillMessageAsync(null!);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotNull(result.ErrorMessage);
    }

    [Fact]
    public async Task StoreWillMessageAsync_EmptyClientId_ShouldReturnFailure()
    {
        // Arrange
        var registration = CreateTestRegistration("", "test/topic");

        // Act
        var result = await _storage.StoreWillMessageAsync(registration);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotNull(result.ErrorMessage);
    }

    [Fact]
    public async Task GetWillMessageAsync_ExistingMessage_ShouldReturnRegistration()
    {
        // Arrange
        var registration = CreateTestRegistration("test-client", "test/topic");
        await _storage.StoreWillMessageAsync(registration);

        // Act
        var result = await _storage.GetWillMessageAsync("test-client");

        // Assert
        Assert.NotNull(result);
        Assert.Equal("test-client", result.ClientId);
        Assert.Equal("test/topic", result.Topic);
        Assert.Equal(MqttQoSLevel.AtLeastOnce, result.QoSLevel);
        Assert.True(result.Retain);
    }

    [Fact]
    public async Task GetWillMessageAsync_NonExistingMessage_ShouldReturnNull()
    {
        // Act
        var result = await _storage.GetWillMessageAsync("non-existing-client");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetWillMessageAsync_EmptyClientId_ShouldReturnNull()
    {
        // Act
        var result = await _storage.GetWillMessageAsync("");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task DeleteWillMessageAsync_ExistingMessage_ShouldReturnSuccess()
    {
        // Arrange
        var registration = CreateTestRegistration("test-client", "test/topic");
        await _storage.StoreWillMessageAsync(registration);

        // Act
        var result = await _storage.DeleteWillMessageAsync("test-client");

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal("test-client", result.ClientId);
        Assert.Equal(WillMessageStorageOperation.Delete, result.Operation);

        // Verify deletion
        var getResult = await _storage.GetWillMessageAsync("test-client");
        Assert.Null(getResult);
    }

    [Fact]
    public async Task DeleteWillMessageAsync_NonExistingMessage_ShouldReturnSuccess()
    {
        // Act
        var result = await _storage.DeleteWillMessageAsync("non-existing-client");

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal("non-existing-client", result.ClientId);
        Assert.Equal(WillMessageStorageOperation.Delete, result.Operation);
    }

    [Fact]
    public async Task ExistsAsync_ExistingMessage_ShouldReturnTrue()
    {
        // Arrange
        var registration = CreateTestRegistration("test-client", "test/topic");
        await _storage.StoreWillMessageAsync(registration);

        // Act
        var result = await _storage.ExistsAsync("test-client");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task ExistsAsync_NonExistingMessage_ShouldReturnFalse()
    {
        // Act
        var result = await _storage.ExistsAsync("non-existing-client");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task GetAllWillMessagesAsync_MultipleMessages_ShouldReturnAll()
    {
        // Arrange
        var registration1 = CreateTestRegistration("client1", "topic1");
        var registration2 = CreateTestRegistration("client2", "topic2");
        var registration3 = CreateTestRegistration("client3", "topic3");

        await _storage.StoreWillMessageAsync(registration1);
        await _storage.StoreWillMessageAsync(registration2);
        await _storage.StoreWillMessageAsync(registration3);

        // Act
        var result = await _storage.GetAllWillMessagesAsync();

        // Assert
        Assert.Equal(3, result.Count);
        Assert.Contains(result, r => r.ClientId == "client1");
        Assert.Contains(result, r => r.ClientId == "client2");
        Assert.Contains(result, r => r.ClientId == "client3");
    }

    [Fact]
    public async Task GetAllWillMessagesAsync_NoMessages_ShouldReturnEmpty()
    {
        // Act
        var result = await _storage.GetAllWillMessagesAsync();

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetExpiredWillMessagesAsync_ExpiredMessages_ShouldReturnExpired()
    {
        // Arrange
        var expiredRegistration = CreateTestRegistration("expired-client", "expired/topic");
        expiredRegistration.ExpiresAt = DateTime.UtcNow.AddHours(-1); // 1 hour ago

        var activeRegistration = CreateTestRegistration("active-client", "active/topic");
        activeRegistration.ExpiresAt = DateTime.UtcNow.AddHours(1); // 1 hour from now

        await _storage.StoreWillMessageAsync(expiredRegistration);
        await _storage.StoreWillMessageAsync(activeRegistration);

        // Act
        var result = await _storage.GetExpiredWillMessagesAsync(DateTime.UtcNow);

        // Assert
        Assert.Single(result);
        Assert.Equal("expired-client", result[0].ClientId);
    }

    [Fact]
    public async Task DeleteWillMessagesAsync_MultipleClients_ShouldDeleteAll()
    {
        // Arrange
        var registration1 = CreateTestRegistration("client1", "topic1");
        var registration2 = CreateTestRegistration("client2", "topic2");
        var registration3 = CreateTestRegistration("client3", "topic3");

        await _storage.StoreWillMessageAsync(registration1);
        await _storage.StoreWillMessageAsync(registration2);
        await _storage.StoreWillMessageAsync(registration3);

        var clientIds = new[] { "client1", "client2" };

        // Act
        var result = await _storage.DeleteWillMessagesAsync(clientIds);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(2, result.SuccessfulClientIds.Count);
        Assert.Empty(result.FailedClientIds);

        // Verify deletions
        Assert.Null(await _storage.GetWillMessageAsync("client1"));
        Assert.Null(await _storage.GetWillMessageAsync("client2"));
        Assert.NotNull(await _storage.GetWillMessageAsync("client3"));
    }

    [Fact]
    public async Task DeleteWillMessagesAsync_SomeNonExisting_ShouldReturnPartialSuccess()
    {
        // Arrange
        var registration1 = CreateTestRegistration("client1", "topic1");
        await _storage.StoreWillMessageAsync(registration1);

        var clientIds = new[] { "client1", "non-existing-client" };

        // Act
        var result = await _storage.DeleteWillMessagesAsync(clientIds);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Single(result.SuccessfulClientIds);
        Assert.Single(result.FailedClientIds);
        Assert.Equal("client1", result.SuccessfulClientIds[0]);
        Assert.Equal("non-existing-client", result.FailedClientIds[0]);
    }

    [Fact]
    public async Task ClearAllAsync_MultipleMessages_ShouldClearAll()
    {
        // Arrange
        var registration1 = CreateTestRegistration("client1", "topic1");
        var registration2 = CreateTestRegistration("client2", "topic2");

        await _storage.StoreWillMessageAsync(registration1);
        await _storage.StoreWillMessageAsync(registration2);

        // Act
        var result = await _storage.ClearAllAsync();

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(WillMessageStorageOperation.ClearAll, result.Operation);

        // Verify all cleared
        var allMessages = await _storage.GetAllWillMessagesAsync();
        Assert.Empty(allMessages);
    }

    [Fact]
    public async Task GetStatisticsAsync_WithMessages_ShouldReturnCorrectStatistics()
    {
        // Arrange
        var registration1 = CreateTestRegistration("client1", "topic1");
        var registration2 = CreateTestRegistration("client2", "topic2");

        await _storage.StoreWillMessageAsync(registration1);
        await _storage.StoreWillMessageAsync(registration2);

        // Act
        var statistics = await _storage.GetStatisticsAsync();

        // Assert
        Assert.Equal(2, statistics.TotalWillMessages);
        Assert.True(statistics.TotalStoreOperations >= 2);
        Assert.Equal(2, statistics.WillMessagesByClientId.Count);
        Assert.Equal(2, statistics.WillMessagesByTopic.Count);
    }

    [Fact]
    public async Task StoreWillMessageAsync_UpdateExisting_ShouldOverwrite()
    {
        // Arrange
        var registration1 = CreateTestRegistration("test-client", "topic1");
        var registration2 = CreateTestRegistration("test-client", "topic2");

        // Act
        await _storage.StoreWillMessageAsync(registration1);
        await _storage.StoreWillMessageAsync(registration2);

        // Assert
        var result = await _storage.GetWillMessageAsync("test-client");
        Assert.NotNull(result);
        Assert.Equal("topic2", result.Topic); // Should be updated to topic2
    }

    private static WillMessageRegistration CreateTestRegistration(string clientId, string topic)
    {
        var willMessage = new MqttWillMessage
        {
            Topic = topic,
            Payload = System.Text.Encoding.UTF8.GetBytes("offline"),
            QoSLevel = MqttQoSLevel.AtLeastOnce,
            Retain = true
        };

        return WillMessageRegistration.FromMqttWillMessage(clientId, willMessage, MqttProtocolVersion.Version311);
    }

    public void Dispose()
    {
        _storage?.Dispose();
    }
}
