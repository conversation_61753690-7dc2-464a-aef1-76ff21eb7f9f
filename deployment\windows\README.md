# MQTT Broker Windows 11 原生部署指南

## 📋 概述

本指南提供了在 Windows 11 系统上原生部署 MQTT Broker 的完整方案，专门针对个人开发环境优化，无需 Docker 或 Kubernetes 等容器化技术。

## 🎯 特点

- ✅ **Windows 11 原生运行**：直接在 Windows 系统上运行，无需虚拟化
- ✅ **轻量级数据库**：默认使用 SQLite，可选 PostgreSQL
- ✅ **可选 Redis 缓存**：支持 Redis 缓存，也可使用内存缓存
- ✅ **Windows 服务支持**：可作为 Windows 服务运行
- ✅ **简化安全配置**：适合开发环境的安全设置
- ✅ **完整管理工具**：PowerShell 脚本管理和监控

## 🔧 系统要求

### 最低要求
- **操作系统**: Windows 11 (或 Windows 10 1909+)
- **处理器**: 2 核心 CPU
- **内存**: 4GB RAM
- **存储**: 2GB 可用空间
- **网络**: 本地网络访问

### 推荐配置
- **操作系统**: Windows 11 最新版本
- **处理器**: 4+ 核心 CPU
- **内存**: 8GB+ RAM
- **存储**: 10GB+ SSD 空间
- **网络**: 千兆网络

## 🚀 快速安装

### 1. 一键安装（推荐）

以管理员身份运行 PowerShell，执行以下命令：

```powershell
# 下载并运行安装脚本
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\deployment\windows\install.ps1
```

### 2. 自定义安装

```powershell
# 安装到自定义路径，包含 Redis 和 PostgreSQL
.\deployment\windows\install.ps1 -InstallPath "D:\MqttBroker" -InstallRedis -InstallPostgreSQL

# 仅安装基础版本（SQLite + 内存缓存）
.\deployment\windows\install.ps1 -Environment Development
```

### 3. 安装参数说明

| 参数 | 描述 | 默认值 | 示例 |
|------|------|--------|------|
| `-Environment` | 部署环境 | Development | Development, Testing, Production |
| `-InstallPath` | 安装路径 | C:\MqttBroker | D:\MyMqttBroker |
| `-InstallRedis` | 安装 Redis | 否 | -InstallRedis |
| `-InstallPostgreSQL` | 安装 PostgreSQL | 否 | -InstallPostgreSQL |
| `-UseWindowsService` | 安装为 Windows 服务 | 否 | -UseWindowsService |
| `-SkipDependencies` | 跳过依赖安装 | 否 | -SkipDependencies |

## 🎮 服务管理

### 启动和停止

```powershell
# 启动 MQTT Broker
.\deployment\windows\start.ps1 start

# 停止 MQTT Broker
.\deployment\windows\start.ps1 stop

# 重启 MQTT Broker
.\deployment\windows\start.ps1 restart

# 查看状态
.\deployment\windows\start.ps1 status
```

### Windows 服务管理

```powershell
# 安装为 Windows 服务
.\deployment\windows\start.ps1 install-service

# 启动服务
.\deployment\windows\start.ps1 start -AsService

# 卸载服务
.\deployment\windows\start.ps1 uninstall-service
```

### 日志和健康检查

```powershell
# 查看最新日志
.\deployment\windows\start.ps1 logs

# 健康检查
.\deployment\windows\start.ps1 health
```

## 📊 监控和管理

### 实时监控

```powershell
# 启动监控仪表板
.\deployment\windows\monitor.ps1 -Mode dashboard -Continuous

# 性能监控
.\deployment\windows\monitor.ps1 -Mode performance -Continuous

# 连接监控
.\deployment\windows\monitor.ps1 -Mode connections -Continuous

# 日志监控
.\deployment\windows\monitor.ps1 -Mode logs -Continuous
```

### 监控功能

- **仪表板模式**: 综合状态显示
- **性能模式**: CPU、内存使用率图表
- **连接模式**: 网络连接状态
- **日志模式**: 实时日志查看

## 🔧 配置说明

### 默认配置

安装完成后，系统将使用以下默认配置：

```json
{
  "MqttBroker": {
    "Network": {
      "Tcp": {
        "Port": 1883,
        "Address": "127.0.0.1"
      },
      "WebSocket": {
        "Port": 8080
      }
    },
    "Storage": {
      "Provider": "SQLite",
      "ConnectionString": "Data Source=C:\\MqttBroker\\data\\mqtt_broker.db"
    },
    "Security": {
      "AllowAnonymous": true,
      "RequireAuthentication": false
    }
  }
}
```

### 访问地址

安装完成后，可通过以下地址访问服务：

- **MQTT TCP**: `localhost:1883`
- **MQTT WebSocket**: `ws://localhost:8080/mqtt`
- **健康检查**: `http://localhost:9090/health`
- **管理接口**: `http://localhost:9090`

### 性能调优

针对个人开发环境的性能参数：

```json
{
  "Network": {
    "Connection": {
      "MaxConnections": 1000,
      "ReceiveBufferSize": 8192,
      "SendBufferSize": 8192
    },
    "Performance": {
      "BatchSize": 50,
      "UseMemoryPool": true
    }
  }
}
```

## 🗂️ 目录结构

安装完成后的目录结构：

```
C:\MqttBroker\
├── app\                    # 应用程序文件
│   ├── MqttBroker.Host.exe # 主程序
│   ├── appsettings.json    # 配置文件
│   └── *.dll              # 依赖库
├── data\                   # 数据文件
│   └── mqtt_broker.db     # SQLite 数据库
├── logs\                   # 日志文件
│   ├── mqtt-broker-*.log  # 应用日志
│   └── metrics\           # 性能指标
├── config\                 # 配置文件
├── scripts\               # 管理脚本
└── backup\                # 备份文件
```

## 🔒 安全配置

### 开发环境安全设置

默认配置适合开发环境，包含以下安全设置：

- **匿名访问**: 允许匿名连接（开发环境）
- **本地绑定**: 仅绑定到 127.0.0.1
- **简化认证**: 无需复杂的认证机制
- **明文传输**: 使用 TCP 而非 TLS（开发环境）

### 生产环境安全加固

如需用于生产环境，请参考以下安全配置：

```json
{
  "Security": {
    "AllowAnonymous": false,
    "RequireAuthentication": true,
    "EnableTls": true
  },
  "Network": {
    "Tcp": {
      "Address": "0.0.0.0"
    },
    "Tls": {
      "Enabled": true,
      "Port": 8883
    }
  }
}
```

## 🛠️ 故障排查

### 常见问题

#### 1. 端口被占用
```powershell
# 检查端口占用
netstat -ano | findstr :1883
netstat -ano | findstr :8080
netstat -ano | findstr :9090

# 终止占用进程
taskkill /PID <进程ID> /F
```

#### 2. 权限问题
```powershell
# 以管理员身份运行 PowerShell
# 检查文件夹权限
icacls C:\MqttBroker /grant Users:F
```

#### 3. .NET 运行时问题
```powershell
# 检查 .NET 版本
dotnet --version

# 重新安装 .NET 8 运行时
winget install Microsoft.DotNet.Runtime.8
```

#### 4. 服务启动失败
```powershell
# 查看 Windows 事件日志
Get-EventLog -LogName Application -Source "MqttBrokerService" -Newest 10

# 检查服务配置
sc query MqttBrokerService
sc qc MqttBrokerService
```

### 诊断工具

```powershell
# 运行完整诊断
.\deployment\windows\start.ps1 status
.\deployment\windows\start.ps1 health
.\deployment\windows\monitor.ps1 -Mode dashboard
```

## 📈 性能优化

### Windows 系统优化

```powershell
# 增加文件句柄限制（注册表）
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\SubSystems" /v Windows /t REG_EXPAND_SZ /d "%SystemRoot%\system32\csrss.exe ObjectDirectory=\Windows SharedSection=1024,20480,768 Windows=On SubSystemType=Windows ServerDll=basesrv,1 ServerDll=winsrv:UserServerDllInitialization,3 ServerDll=winsrv:ConServerDllInitialization,2 ServerDll=sxssrv,4 ProfileControl=Off MaxRequestThreads=16" /f

# 优化网络设置
netsh int tcp set global autotuninglevel=normal
netsh int tcp set global chimney=enabled
netsh int tcp set global rss=enabled
```

### 应用程序优化

编辑 `C:\MqttBroker\app\appsettings.json`：

```json
{
  "MqttBroker": {
    "Network": {
      "Performance": {
        "UseMemoryPool": true,
        "EnableZeroCopy": true,
        "BatchSize": 100
      }
    }
  }
}
```

## 🔄 更新和维护

### 应用程序更新

```powershell
# 停止服务
.\deployment\windows\start.ps1 stop

# 备份当前版本
Copy-Item "C:\MqttBroker\app" "C:\MqttBroker\backup\app_$(Get-Date -Format 'yyyyMMdd')" -Recurse

# 部署新版本
.\deployment\windows\install.ps1 -SkipDependencies

# 启动服务
.\deployment\windows\start.ps1 start
```

### 数据备份

```powershell
# 创建备份脚本
$backupScript = @"
# 备份数据库
Copy-Item "C:\MqttBroker\data\mqtt_broker.db" "C:\MqttBroker\backup\mqtt_broker_$(Get-Date -Format 'yyyyMMdd_HHmmss').db"

# 备份配置
Copy-Item "C:\MqttBroker\app\appsettings.json" "C:\MqttBroker\backup\appsettings_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"

# 清理旧备份（保留30天）
Get-ChildItem "C:\MqttBroker\backup" -File | Where-Object { $_.CreationTime -lt (Get-Date).AddDays(-30) } | Remove-Item
"@

$backupScript | Out-File "C:\MqttBroker\scripts\backup.ps1"

# 设置定时任务
schtasks /create /tn "MqttBroker Backup" /tr "powershell.exe -File C:\MqttBroker\scripts\backup.ps1" /sc daily /st 02:00
```

## 📞 技术支持

### 获取帮助

- **查看日志**: `.\deployment\windows\start.ps1 logs`
- **健康检查**: `.\deployment\windows\start.ps1 health`
- **状态监控**: `.\deployment\windows\monitor.ps1`

### 联系方式

- **GitHub Issues**: 报告问题和功能请求
- **技术文档**: 查看详细的 API 文档
- **社区支持**: 参与开发者社区讨论

---

**注意**: 本部署方案专为 Windows 11 个人开发环境设计，如需用于生产环境，请参考企业级部署方案并进行相应的安全加固。
