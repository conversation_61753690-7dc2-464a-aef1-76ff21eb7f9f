using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;

namespace MqttBroker.Core.QoS;

/// <summary>
/// 消息去重服务接口，负责 QoS 2 消息的去重处理
/// </summary>
public interface IMessageDeduplicationService
{
    /// <summary>
    /// 检查消息是否为重复消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="messageId">消息ID</param>
    /// <param name="publishPacket">发布数据包</param>
    /// <returns>去重检查结果</returns>
    Task<DeduplicationResult> CheckDuplicateAsync(string clientId, ushort messageId, MqttPublishPacket publishPacket);

    /// <summary>
    /// 记录已处理的消息（用于去重）
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="messageId">消息ID</param>
    /// <param name="publishPacket">发布数据包</param>
    /// <param name="expirationTime">过期时间</param>
    /// <returns>是否记录成功</returns>
    Task<bool> RecordProcessedMessageAsync(string clientId, ushort messageId, MqttPublishPacket publishPacket, TimeSpan? expirationTime = null);

    /// <summary>
    /// 移除已处理的消息记录
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="messageId">消息ID</param>
    /// <returns>是否移除成功</returns>
    Task<bool> RemoveProcessedMessageAsync(string clientId, ushort messageId);

    /// <summary>
    /// 清理客户端的所有已处理消息记录
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>清理的记录数量</returns>
    Task<int> ClearClientProcessedMessagesAsync(string clientId);

    /// <summary>
    /// 清理过期的消息记录
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理的记录数量</returns>
    Task<int> CleanupExpiredRecordsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取去重统计信息
    /// </summary>
    /// <returns>去重统计信息</returns>
    Task<DeduplicationStatistics> GetStatisticsAsync();

    /// <summary>
    /// 获取客户端的已处理消息记录
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>已处理消息记录列表</returns>
    Task<IList<ProcessedMessageRecord>> GetClientProcessedMessagesAsync(string clientId);

    /// <summary>
    /// 启动去重服务（开始清理任务）
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止去重服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    Task StopAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 去重检查结果
/// </summary>
public class DeduplicationResult
{
    /// <summary>
    /// 是否为重复消息
    /// </summary>
    public bool IsDuplicate { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 消息ID
    /// </summary>
    public ushort MessageId { get; set; }

    /// <summary>
    /// 原始处理时间
    /// </summary>
    public DateTime? OriginalProcessedTime { get; set; }

    /// <summary>
    /// 重复检测时间
    /// </summary>
    public DateTime DetectedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 消息哈希值（用于内容比较）
    /// </summary>
    public string? MessageHash { get; set; }

    /// <summary>
    /// 额外信息
    /// </summary>
    public Dictionary<string, object>? AdditionalInfo { get; set; }

    /// <summary>
    /// 创建非重复结果
    /// </summary>
    public static DeduplicationResult NotDuplicate(string clientId, ushort messageId)
    {
        return new DeduplicationResult
        {
            IsDuplicate = false,
            ClientId = clientId,
            MessageId = messageId
        };
    }

    /// <summary>
    /// 创建重复结果
    /// </summary>
    public static DeduplicationResult Duplicate(string clientId, ushort messageId, DateTime originalProcessedTime, string? messageHash = null)
    {
        return new DeduplicationResult
        {
            IsDuplicate = true,
            ClientId = clientId,
            MessageId = messageId,
            OriginalProcessedTime = originalProcessedTime,
            MessageHash = messageHash
        };
    }
}

/// <summary>
/// 已处理消息记录
/// </summary>
public class ProcessedMessageRecord
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 消息ID
    /// </summary>
    public ushort MessageId { get; set; }

    /// <summary>
    /// 消息哈希值
    /// </summary>
    public string MessageHash { get; set; } = string.Empty;

    /// <summary>
    /// 主题
    /// </summary>
    public string Topic { get; set; } = string.Empty;

    /// <summary>
    /// QoS级别
    /// </summary>
    public MqttQoSLevel QoSLevel { get; set; }

    /// <summary>
    /// 处理时间
    /// </summary>
    public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// 重复检测次数
    /// </summary>
    public int DuplicateDetectionCount { get; set; }

    /// <summary>
    /// 最后一次重复检测时间
    /// </summary>
    public DateTime? LastDuplicateDetectedAt { get; set; }

    /// <summary>
    /// 是否已过期
    /// </summary>
    public bool IsExpired => ExpiresAt.HasValue && DateTime.UtcNow > ExpiresAt.Value;

    /// <summary>
    /// 记录年龄
    /// </summary>
    public TimeSpan Age => DateTime.UtcNow - ProcessedAt;

    /// <summary>
    /// 生成消息哈希值
    /// </summary>
    public static string GenerateMessageHash(MqttPublishPacket publishPacket)
    {
        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var hashInput = $"{publishPacket.Topic}:{Convert.ToBase64String(publishPacket.Payload)}:{publishPacket.QoSLevel}:{publishPacket.Retain}";
        var hashBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(hashInput));
        return Convert.ToBase64String(hashBytes);
    }
}

/// <summary>
/// 去重统计信息
/// </summary>
public class DeduplicationStatistics
{
    /// <summary>
    /// 总检查的消息数量
    /// </summary>
    public long TotalCheckedMessages { get; set; }

    /// <summary>
    /// 检测到的重复消息数量
    /// </summary>
    public long TotalDuplicateMessages { get; set; }

    /// <summary>
    /// 当前存储的已处理消息记录数量
    /// </summary>
    public int CurrentProcessedMessageRecords { get; set; }

    /// <summary>
    /// 有已处理消息记录的客户端数量
    /// </summary>
    public int ClientsWithProcessedMessages { get; set; }

    /// <summary>
    /// 总清理的过期记录数量
    /// </summary>
    public long TotalExpiredRecordsCleanedUp { get; set; }

    /// <summary>
    /// 平均记录年龄（毫秒）
    /// </summary>
    public double AverageRecordAge { get; set; }

    /// <summary>
    /// 最老的记录年龄（毫秒）
    /// </summary>
    public double OldestRecordAge { get; set; }

    /// <summary>
    /// 重复检测率
    /// </summary>
    public double DuplicateDetectionRate => TotalCheckedMessages > 0 ? (double)TotalDuplicateMessages / TotalCheckedMessages * 100 : 0;

    /// <summary>
    /// 最后一次清理时间
    /// </summary>
    public DateTime? LastCleanupTime { get; set; }

    /// <summary>
    /// 按客户端分组的去重统计
    /// </summary>
    public Dictionary<string, ClientDeduplicationStatistics> ClientStatistics { get; set; } = new();

    /// <summary>
    /// 按主题分组的去重统计
    /// </summary>
    public Dictionary<string, TopicDeduplicationStatistics> TopicStatistics { get; set; } = new();
}

/// <summary>
/// 客户端去重统计信息
/// </summary>
public class ClientDeduplicationStatistics
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 检查的消息数量
    /// </summary>
    public long CheckedMessages { get; set; }

    /// <summary>
    /// 重复消息数量
    /// </summary>
    public long DuplicateMessages { get; set; }

    /// <summary>
    /// 当前已处理消息记录数量
    /// </summary>
    public int CurrentProcessedRecords { get; set; }

    /// <summary>
    /// 重复检测率
    /// </summary>
    public double DuplicateRate => CheckedMessages > 0 ? (double)DuplicateMessages / CheckedMessages * 100 : 0;

    /// <summary>
    /// 最后一次检查时间
    /// </summary>
    public DateTime? LastCheckTime { get; set; }
}

/// <summary>
/// 主题去重统计信息
/// </summary>
public class TopicDeduplicationStatistics
{
    /// <summary>
    /// 主题名称
    /// </summary>
    public string Topic { get; set; } = string.Empty;

    /// <summary>
    /// 检查的消息数量
    /// </summary>
    public long CheckedMessages { get; set; }

    /// <summary>
    /// 重复消息数量
    /// </summary>
    public long DuplicateMessages { get; set; }

    /// <summary>
    /// 重复检测率
    /// </summary>
    public double DuplicateRate => CheckedMessages > 0 ? (double)DuplicateMessages / CheckedMessages * 100 : 0;

    /// <summary>
    /// 最后一次检查时间
    /// </summary>
    public DateTime? LastCheckTime { get; set; }
}
