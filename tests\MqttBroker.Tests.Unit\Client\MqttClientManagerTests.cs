using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using MqttBroker.Core.Client;
using MqttBroker.Core.Network;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using System.Net;
using Xunit;

namespace MqttBroker.Tests.Unit.Client;

/// <summary>
/// MQTT 客户端管理器单元测试
/// </summary>
public class MqttClientManagerTests
{
    private readonly Mock<ILogger<MqttClientManager>> _loggerMock;
    private readonly Mock<ILoggerFactory> _loggerFactoryMock;
    private readonly Mock<IMqttClientAuthenticator> _authenticatorMock;
    private readonly Mock<IClientConnection> _connectionMock;
    private readonly MqttClientManagerOptions _options;
    private readonly MqttClientManager _clientManager;

    public MqttClientManagerTests()
    {
        _loggerMock = new Mock<ILogger<MqttClientManager>>();
        _loggerFactoryMock = new Mock<ILoggerFactory>();
        _authenticatorMock = new Mock<IMqttClientAuthenticator>();
        _connectionMock = new Mock<IClientConnection>();
        
        _options = new MqttClientManagerOptions
        {
            MaxConnections = 1000,
            AllowClientIdReuse = true
        };

        var optionsMock = new Mock<IOptions<MqttClientManagerOptions>>();
        optionsMock.Setup(x => x.Value).Returns(_options);

        _loggerFactoryMock.Setup(x => x.CreateLogger(typeof(MqttClient).FullName))
            .Returns(new Mock<ILogger<MqttClient>>().Object);

        _clientManager = new MqttClientManager(
            _loggerMock.Object,
            _loggerFactoryMock.Object,
            _authenticatorMock.Object,
            optionsMock.Object);

        // 设置连接模拟
        _connectionMock.Setup(x => x.Id).Returns("conn_001");
        _connectionMock.SetupProperty(x => x.ClientId); // 设置为可设置的属性
        _connectionMock.Setup(x => x.RemoteEndPoint).Returns(new IPEndPoint(IPAddress.Loopback, 12345));
        _connectionMock.Setup(x => x.LocalEndPoint).Returns(new IPEndPoint(IPAddress.Loopback, 1883));
        _connectionMock.Setup(x => x.State).Returns(ConnectionState.Connected);
        _connectionMock.Setup(x => x.ConnectedAt).Returns(DateTime.UtcNow);
        _connectionMock.Setup(x => x.LastActivity).Returns(DateTime.UtcNow);
        _connectionMock.Setup(x => x.Properties).Returns(new Dictionary<string, object>());
        _connectionMock.Setup(x => x.GetStatistics()).Returns(new ClientConnectionStatistics
        {
            ConnectionId = "conn_001",
            RemoteEndPoint = "127.0.0.1:12345",
            ConnectedAt = DateTime.UtcNow,
            LastActivity = DateTime.UtcNow
        });
    }

    [Fact]
    public async Task HandleConnectAsync_ValidConnect_ShouldReturnSuccess()
    {
        // Arrange
        var connectPacket = new MqttConnectPacket
        {
            ClientId = "test_client",
            CleanSession = true,
            KeepAlive = 60,
            Username = "admin",
            Password = "admin123",
            ProtocolVersion = MqttProtocolVersion.Version311
        };

        _authenticatorMock.Setup(x => x.AuthenticateAsync(It.IsAny<MqttConnectPacket>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(MqttAuthenticationResult.Success("admin"));

        // Act
        var result = await _clientManager.HandleConnectAsync(_connectionMock.Object, connectPacket);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(MqttConnectReturnCode.ConnectionAccepted, result.ReturnCode);
        Assert.Equal(MqttReasonCode.Success, result.ReasonCode);
        Assert.False(result.SessionPresent);
        Assert.Equal(1, _clientManager.ConnectedClientCount);
    }

    [Fact]
    public async Task HandleConnectAsync_AuthenticationFailed_ShouldReturnFailure()
    {
        // Arrange
        var connectPacket = new MqttConnectPacket
        {
            ClientId = "test_client",
            CleanSession = true,
            KeepAlive = 60,
            Username = "invalid_user",
            Password = "invalid_pass",
            ProtocolVersion = MqttProtocolVersion.Version311
        };

        _authenticatorMock.Setup(x => x.AuthenticateAsync(It.IsAny<MqttConnectPacket>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(MqttAuthenticationResult.BadCredentials("Invalid credentials"));

        // Act
        var result = await _clientManager.HandleConnectAsync(_connectionMock.Object, connectPacket);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(MqttConnectReturnCode.BadUsernameOrPassword, result.ReturnCode);
        Assert.Equal(MqttReasonCode.BadUserNameOrPassword, result.ReasonCode);
        Assert.Equal("Invalid credentials", result.ErrorMessage);
        Assert.Equal(0, _clientManager.ConnectedClientCount);
    }

    [Fact]
    public async Task HandleConnectAsync_EmptyClientIdWithCleanSession_ShouldGenerateClientId()
    {
        // Arrange
        var connectPacket = new MqttConnectPacket
        {
            ClientId = "", // 空客户端 ID
            CleanSession = true,
            KeepAlive = 60,
            ProtocolVersion = MqttProtocolVersion.Version311
        };

        _authenticatorMock.Setup(x => x.GenerateClientId())
            .Returns("auto_generated_001");

        _authenticatorMock.Setup(x => x.AuthenticateAsync(It.IsAny<MqttConnectPacket>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(MqttAuthenticationResult.Success());

        // Act
        var result = await _clientManager.HandleConnectAsync(_connectionMock.Object, connectPacket);

        // Assert
        Assert.True(result.IsSuccess);
        _authenticatorMock.Verify(x => x.GenerateClientId(), Times.Once);

        // 验证生成的客户端 ID 被设置到连接上
        _connectionMock.VerifySet(x => x.ClientId = "auto_generated_001", Times.Once);
    }

    [Fact]
    public async Task HandleConnectAsync_EmptyClientIdWithoutCleanSession_ShouldReturnFailure()
    {
        // Arrange
        var connectPacket = new MqttConnectPacket
        {
            ClientId = "", // 空客户端 ID
            CleanSession = false, // 非 Clean Session
            KeepAlive = 60,
            ProtocolVersion = MqttProtocolVersion.Version311
        };

        // Act
        var result = await _clientManager.HandleConnectAsync(_connectionMock.Object, connectPacket);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(MqttConnectReturnCode.IdentifierRejected, result.ReturnCode);
        Assert.Equal(MqttReasonCode.ClientIdentifierNotValid, result.ReasonCode);
    }

    [Fact]
    public void GetClient_ExistingClient_ShouldReturnClient()
    {
        // Arrange
        var connectPacket = new MqttConnectPacket
        {
            ClientId = "test_client",
            CleanSession = true,
            ProtocolVersion = MqttProtocolVersion.Version311
        };

        _authenticatorMock.Setup(x => x.AuthenticateAsync(It.IsAny<MqttConnectPacket>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(MqttAuthenticationResult.Success());

        // Act
        var connectTask = _clientManager.HandleConnectAsync(_connectionMock.Object, connectPacket);
        connectTask.Wait();

        var client = _clientManager.GetClient("test_client");

        // Assert
        Assert.NotNull(client);
        Assert.Equal("test_client", client.ClientId);
    }

    [Fact]
    public void GetClient_NonExistingClient_ShouldReturnNull()
    {
        // Act
        var client = _clientManager.GetClient("non_existing_client");

        // Assert
        Assert.Null(client);
    }

    [Fact]
    public void IsClientConnected_ExistingClient_ShouldReturnTrue()
    {
        // Arrange
        var connectPacket = new MqttConnectPacket
        {
            ClientId = "test_client",
            CleanSession = true,
            ProtocolVersion = MqttProtocolVersion.Version311
        };

        _authenticatorMock.Setup(x => x.AuthenticateAsync(It.IsAny<MqttConnectPacket>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(MqttAuthenticationResult.Success());

        // Act
        var connectTask = _clientManager.HandleConnectAsync(_connectionMock.Object, connectPacket);
        connectTask.Wait();

        var isConnected = _clientManager.IsClientConnected("test_client");

        // Assert
        Assert.True(isConnected);
    }

    [Fact]
    public void IsClientConnected_NonExistingClient_ShouldReturnFalse()
    {
        // Act
        var isConnected = _clientManager.IsClientConnected("non_existing_client");

        // Assert
        Assert.False(isConnected);
    }

    [Fact]
    public void GetStatistics_ShouldReturnCorrectStatistics()
    {
        // Act
        var stats = _clientManager.GetStatistics();

        // Assert
        Assert.NotNull(stats);
        Assert.Equal(0, stats.ConnectedClients);
        Assert.Equal(_options.MaxConnections, stats.MaxConnections);
        Assert.Equal(0, stats.TotalConnections);
        Assert.Equal(0, stats.TotalDisconnections);
    }

    [Fact]
    public async Task HandleDisconnectAsync_ExistingClient_ShouldRemoveClient()
    {
        // Arrange
        var connectPacket = new MqttConnectPacket
        {
            ClientId = "test_client",
            CleanSession = true,
            ProtocolVersion = MqttProtocolVersion.Version311
        };

        _authenticatorMock.Setup(x => x.AuthenticateAsync(It.IsAny<MqttConnectPacket>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(MqttAuthenticationResult.Success());

        await _clientManager.HandleConnectAsync(_connectionMock.Object, connectPacket);
        Assert.Equal(1, _clientManager.ConnectedClientCount);

        // Act
        await _clientManager.HandleDisconnectAsync("test_client", DisconnectionReason.ClientDisconnected);

        // Assert
        Assert.Equal(0, _clientManager.ConnectedClientCount);
        Assert.Null(_clientManager.GetClient("test_client"));
    }

    [Fact]
    public async Task CleanupTimeoutClientsAsync_ShouldRemoveTimeoutClients()
    {
        // Arrange
        var connectPacket = new MqttConnectPacket
        {
            ClientId = "test_client",
            CleanSession = true,
            KeepAlive = 1, // 1 秒保活间隔
            ProtocolVersion = MqttProtocolVersion.Version311
        };

        _authenticatorMock.Setup(x => x.AuthenticateAsync(It.IsAny<MqttConnectPacket>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(MqttAuthenticationResult.Success());

        await _clientManager.HandleConnectAsync(_connectionMock.Object, connectPacket);
        
        // 模拟客户端超时
        var client = _clientManager.GetClient("test_client");
        Assert.NotNull(client);

        // 模拟超时（通过反射设置最后活动时间为过去）
        var lastActivityField = typeof(MqttClient).GetField("_lastActivity", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        lastActivityField?.SetValue(client, DateTime.UtcNow.AddSeconds(-10)); // 10 秒前

        // Act
        await _clientManager.CleanupTimeoutClientsAsync();

        // Assert
        Assert.Equal(0, _clientManager.ConnectedClientCount);
    }
}
