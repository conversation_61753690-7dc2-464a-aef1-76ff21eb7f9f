using System.Diagnostics;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace MqttBroker.Core.Message.Filters;

/// <summary>
/// 消息大小过滤器
/// </summary>
public class MessageSizeFilter : IMessageFilter
{
    private readonly ILogger<MessageSizeFilter> _logger;
    private readonly MessageSizeFilterOptions _options;

    public string Name => "MessageSizeFilter";
    public int Priority => 100;
    public bool IsEnabled { get; set; } = true;

    public MessageSizeFilter(ILogger<MessageSizeFilter> logger, IOptions<MessageSizeFilterOptions> options)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? new MessageSizeFilterOptions();
    }

    public Task<MessageFilterResult> FilterAsync(MessageFilterContext context, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var messageSize = context.PublishPacket.Payload.Length;
            
            if (messageSize > _options.MaxMessageSize)
            {
                _logger.LogWarning("Message size {MessageSize} bytes exceeds maximum allowed size {MaxSize} bytes for topic: {TopicName}", 
                    messageSize, _options.MaxMessageSize, context.PublishPacket.Topic);
                
                var result = MessageFilterResult.Deny($"Message size {messageSize} bytes exceeds maximum allowed size {_options.MaxMessageSize} bytes");
                result.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
                return Task.FromResult(result);
            }

            _logger.LogTrace("Message size {MessageSize} bytes is within allowed limit for topic: {TopicName}", 
                messageSize, context.PublishPacket.Topic);

            var allowResult = MessageFilterResult.Allow();
            allowResult.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
            allowResult.Properties["MessageSize"] = messageSize;
            
            return Task.FromResult(allowResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in MessageSizeFilter");
            return Task.FromResult(MessageFilterResult.Allow());
        }
    }
}

/// <summary>
/// 主题黑名单过滤器
/// </summary>
public class TopicBlacklistFilter : IMessageFilter
{
    private readonly ILogger<TopicBlacklistFilter> _logger;
    private readonly TopicBlacklistFilterOptions _options;

    public string Name => "TopicBlacklistFilter";
    public int Priority => 200;
    public bool IsEnabled { get; set; } = true;

    public TopicBlacklistFilter(ILogger<TopicBlacklistFilter> logger, IOptions<TopicBlacklistFilterOptions> options)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? new TopicBlacklistFilterOptions();
    }

    public Task<MessageFilterResult> FilterAsync(MessageFilterContext context, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var topicName = context.PublishPacket.Topic;
            
            // 检查精确匹配
            if (_options.BlacklistedTopics.Contains(topicName))
            {
                _logger.LogWarning("Topic {TopicName} is blacklisted (exact match)", topicName);
                
                var result = MessageFilterResult.Deny($"Topic {topicName} is blacklisted");
                result.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
                return Task.FromResult(result);
            }

            // 检查模式匹配
            foreach (var pattern in _options.BlacklistedPatterns)
            {
                if (IsTopicMatch(topicName, pattern))
                {
                    _logger.LogWarning("Topic {TopicName} matches blacklisted pattern: {Pattern}", topicName, pattern);
                    
                    var result = MessageFilterResult.Deny($"Topic {topicName} matches blacklisted pattern: {pattern}");
                    result.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
                    return Task.FromResult(result);
                }
            }

            var allowResult = MessageFilterResult.Allow();
            allowResult.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
            
            return Task.FromResult(allowResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in TopicBlacklistFilter");
            return Task.FromResult(MessageFilterResult.Allow());
        }
    }

    private bool IsTopicMatch(string topicName, string pattern)
    {
        // 简单的通配符匹配实现
        if (pattern.Contains("*"))
        {
            var regex = pattern.Replace("*", ".*");
            return System.Text.RegularExpressions.Regex.IsMatch(topicName, regex);
        }
        
        return topicName.Equals(pattern, StringComparison.OrdinalIgnoreCase);
    }
}

/// <summary>
/// 内容验证过滤器
/// </summary>
public class ContentValidationFilter : IMessageFilter
{
    private readonly ILogger<ContentValidationFilter> _logger;
    private readonly ContentValidationFilterOptions _options;

    public string Name => "ContentValidationFilter";
    public int Priority => 300;
    public bool IsEnabled { get; set; } = true;

    public ContentValidationFilter(ILogger<ContentValidationFilter> logger, IOptions<ContentValidationFilterOptions> options)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? new ContentValidationFilterOptions();
    }

    public Task<MessageFilterResult> FilterAsync(MessageFilterContext context, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            if (!_options.ValidateContent)
            {
                return Task.FromResult(MessageFilterResult.Allow());
            }

            var payload = context.PublishPacket.Payload;
            
            // 检查是否为空消息
            if (payload.Length == 0 && !_options.AllowEmptyMessages)
            {
                _logger.LogWarning("Empty message not allowed for topic: {TopicName}", context.PublishPacket.Topic);
                
                var result = MessageFilterResult.Deny("Empty messages are not allowed");
                result.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
                return Task.FromResult(result);
            }

            // 验证JSON格式（如果配置要求）
            if (_options.RequireValidJson && payload.Length > 0)
            {
                try
                {
                    var jsonString = Encoding.UTF8.GetString(payload);
                    JsonDocument.Parse(jsonString);
                    
                    _logger.LogTrace("Valid JSON content for topic: {TopicName}", context.PublishPacket.Topic);
                }
                catch (JsonException)
                {
                    _logger.LogWarning("Invalid JSON content for topic: {TopicName}", context.PublishPacket.Topic);
                    
                    var result = MessageFilterResult.Deny("Message content is not valid JSON");
                    result.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
                    return Task.FromResult(result);
                }
            }

            // 检查禁用词汇
            if (_options.ForbiddenWords.Count > 0 && payload.Length > 0)
            {
                var content = Encoding.UTF8.GetString(payload).ToLowerInvariant();
                
                foreach (var forbiddenWord in _options.ForbiddenWords)
                {
                    if (content.Contains(forbiddenWord.ToLowerInvariant()))
                    {
                        _logger.LogWarning("Message contains forbidden word '{ForbiddenWord}' for topic: {TopicName}", 
                            forbiddenWord, context.PublishPacket.Topic);
                        
                        var result = MessageFilterResult.Deny($"Message contains forbidden content");
                        result.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
                        return Task.FromResult(result);
                    }
                }
            }

            var allowResult = MessageFilterResult.Allow();
            allowResult.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
            allowResult.Properties["ContentValidated"] = true;
            
            return Task.FromResult(allowResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ContentValidationFilter");
            return Task.FromResult(MessageFilterResult.Allow());
        }
    }
}

/// <summary>
/// 频率限制过滤器
/// </summary>
public class RateLimitFilter : IMessageFilter
{
    private readonly ILogger<RateLimitFilter> _logger;
    private readonly RateLimitFilterOptions _options;
    private readonly Dictionary<string, ClientRateLimit> _clientLimits;
    private readonly object _limitsLock = new();

    public string Name => "RateLimitFilter";
    public int Priority => 400;
    public bool IsEnabled { get; set; } = true;

    public RateLimitFilter(ILogger<RateLimitFilter> logger, IOptions<RateLimitFilterOptions> options)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? new RateLimitFilterOptions();
        _clientLimits = new Dictionary<string, ClientRateLimit>();
    }

    public Task<MessageFilterResult> FilterAsync(MessageFilterContext context, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            if (!_options.EnableRateLimit || string.IsNullOrEmpty(context.PublisherClientId))
            {
                return Task.FromResult(MessageFilterResult.Allow());
            }

            var clientId = context.PublisherClientId;
            var now = DateTime.UtcNow;

            lock (_limitsLock)
            {
                if (!_clientLimits.TryGetValue(clientId, out var rateLimit))
                {
                    rateLimit = new ClientRateLimit
                    {
                        ClientId = clientId,
                        WindowStart = now,
                        MessageCount = 0
                    };
                    _clientLimits[clientId] = rateLimit;
                }

                // 检查是否需要重置窗口
                if (now - rateLimit.WindowStart >= TimeSpan.FromSeconds(_options.WindowSizeSeconds))
                {
                    rateLimit.WindowStart = now;
                    rateLimit.MessageCount = 0;
                }

                rateLimit.MessageCount++;

                if (rateLimit.MessageCount > _options.MaxMessagesPerWindow)
                {
                    _logger.LogWarning("Rate limit exceeded for client {ClientId}: {MessageCount}/{MaxMessages} in {WindowSize}s", 
                        clientId, rateLimit.MessageCount, _options.MaxMessagesPerWindow, _options.WindowSizeSeconds);
                    
                    var result = MessageFilterResult.Deny($"Rate limit exceeded: {rateLimit.MessageCount}/{_options.MaxMessagesPerWindow} messages per {_options.WindowSizeSeconds}s");
                    result.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
                    return Task.FromResult(result);
                }
            }

            var allowResult = MessageFilterResult.Allow();
            allowResult.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
            
            return Task.FromResult(allowResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in RateLimitFilter");
            return Task.FromResult(MessageFilterResult.Allow());
        }
    }

    private class ClientRateLimit
    {
        public string ClientId { get; set; } = string.Empty;
        public DateTime WindowStart { get; set; }
        public int MessageCount { get; set; }
    }
}

#region 配置选项类

/// <summary>
/// 消息大小过滤器配置选项
/// </summary>
public class MessageSizeFilterOptions
{
    /// <summary>
    /// 最大消息大小（字节）
    /// </summary>
    public int MaxMessageSize { get; set; } = 1024 * 1024; // 1MB
}

/// <summary>
/// 主题黑名单过滤器配置选项
/// </summary>
public class TopicBlacklistFilterOptions
{
    /// <summary>
    /// 黑名单主题列表
    /// </summary>
    public HashSet<string> BlacklistedTopics { get; set; } = new();

    /// <summary>
    /// 黑名单模式列表（支持通配符）
    /// </summary>
    public List<string> BlacklistedPatterns { get; set; } = new();
}

/// <summary>
/// 内容验证过滤器配置选项
/// </summary>
public class ContentValidationFilterOptions
{
    /// <summary>
    /// 是否验证内容
    /// </summary>
    public bool ValidateContent { get; set; } = true;

    /// <summary>
    /// 是否允许空消息
    /// </summary>
    public bool AllowEmptyMessages { get; set; } = true;

    /// <summary>
    /// 是否要求有效的JSON格式
    /// </summary>
    public bool RequireValidJson { get; set; } = false;

    /// <summary>
    /// 禁用词汇列表
    /// </summary>
    public List<string> ForbiddenWords { get; set; } = new();
}

/// <summary>
/// 频率限制过滤器配置选项
/// </summary>
public class RateLimitFilterOptions
{
    /// <summary>
    /// 是否启用频率限制
    /// </summary>
    public bool EnableRateLimit { get; set; } = true;

    /// <summary>
    /// 时间窗口大小（秒）
    /// </summary>
    public int WindowSizeSeconds { get; set; } = 60;

    /// <summary>
    /// 每个时间窗口内的最大消息数
    /// </summary>
    public int MaxMessagesPerWindow { get; set; } = 1000;
}

#endregion
