using Microsoft.Extensions.Logging;
using MqttBroker.Core.Session;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Topic;
using MqttBroker.Core.QoS;
using Xunit;

namespace MqttBroker.Core.Tests.Session;

/// <summary>
/// 会话持久化测试
/// </summary>
public class SessionPersistenceTests
{
    private readonly ISessionPersistence _sessionPersistence;
    private readonly ILogger<InMemorySessionPersistence> _logger;

    public SessionPersistenceTests()
    {
        _logger = new LoggerFactory().CreateLogger<InMemorySessionPersistence>();
        _sessionPersistence = new InMemorySessionPersistence(_logger);
    }

    [Fact]
    public async Task CreateOrUpdateSessionAsync_NewSession_ShouldReturnSuccess()
    {
        // Arrange
        var session = new MqttSession
        {
            ClientId = "test-client-001",
            IsCleanSession = false,
            ProtocolVersion = MqttProtocolVersion.Version311,
            KeepAliveInterval = 60,
            Username = "test-user"
        };

        // Act
        var result = await _sessionPersistence.CreateOrUpdateSessionAsync(session);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(SessionOperationType.Create, result.OperationType);
        Assert.Equal("test-client-001", result.ClientId);
    }

    [Fact]
    public async Task GetSessionAsync_ExistingSession_ShouldReturnSession()
    {
        // Arrange
        var session = new MqttSession
        {
            ClientId = "test-client-002",
            IsCleanSession = false,
            ProtocolVersion = MqttProtocolVersion.Version311,
            KeepAliveInterval = 60,
            Username = "test-user"
        };

        await _sessionPersistence.CreateOrUpdateSessionAsync(session);

        // Act
        var retrievedSession = await _sessionPersistence.GetSessionAsync("test-client-002");

        // Assert
        Assert.NotNull(retrievedSession);
        Assert.Equal("test-client-002", retrievedSession.ClientId);
        Assert.Equal("test-user", retrievedSession.Username);
        Assert.Equal(60, retrievedSession.KeepAliveInterval);
    }

    [Fact]
    public async Task GetSessionAsync_NonExistentSession_ShouldReturnNull()
    {
        // Act
        var retrievedSession = await _sessionPersistence.GetSessionAsync("non-existent-client");

        // Assert
        Assert.Null(retrievedSession);
    }

    [Fact]
    public async Task DeleteSessionAsync_ExistingSession_ShouldReturnSuccess()
    {
        // Arrange
        var session = new MqttSession
        {
            ClientId = "test-client-003",
            IsCleanSession = false
        };

        await _sessionPersistence.CreateOrUpdateSessionAsync(session);

        // Act
        var result = await _sessionPersistence.DeleteSessionAsync("test-client-003");

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(1, result.AffectedRecords);

        // Verify session is deleted
        var retrievedSession = await _sessionPersistence.GetSessionAsync("test-client-003");
        Assert.Null(retrievedSession);
    }

    [Fact]
    public async Task SaveSubscriptionsAsync_ValidSubscriptions_ShouldReturnSuccess()
    {
        // Arrange
        var clientId = "test-client-004";
        var subscriptions = new List<ClientSubscription>
        {
            new ClientSubscription
            {
                ClientId = clientId,
                TopicFilter = "sensors/+/temperature",
                QoSLevel = MqttQoSLevel.AtLeastOnce,
                SubscribedAt = DateTime.UtcNow,
                Options = new MqttSubscriptionOptions()
            },
            new ClientSubscription
            {
                ClientId = clientId,
                TopicFilter = "devices/+/status",
                QoSLevel = MqttQoSLevel.ExactlyOnce,
                SubscribedAt = DateTime.UtcNow,
                Options = new MqttSubscriptionOptions()
            }
        };

        // Act
        var result = await _sessionPersistence.SaveSubscriptionsAsync(clientId, subscriptions);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(2, result.AffectedRecords);
    }

    [Fact]
    public async Task GetSubscriptionsAsync_ExistingSubscriptions_ShouldReturnSubscriptions()
    {
        // Arrange
        var clientId = "test-client-005";
        var subscriptions = new List<ClientSubscription>
        {
            new ClientSubscription
            {
                ClientId = clientId,
                TopicFilter = "test/topic",
                QoSLevel = MqttQoSLevel.AtLeastOnce,
                SubscribedAt = DateTime.UtcNow,
                Options = new MqttSubscriptionOptions()
            }
        };

        await _sessionPersistence.SaveSubscriptionsAsync(clientId, subscriptions);

        // Act
        var retrievedSubscriptions = await _sessionPersistence.GetSubscriptionsAsync(clientId);

        // Assert
        Assert.Single(retrievedSubscriptions);
        var subscription = retrievedSubscriptions.First();
        Assert.Equal("test/topic", subscription.TopicFilter);
        Assert.Equal(MqttQoSLevel.AtLeastOnce, subscription.QoSLevel);
    }

    [Fact]
    public async Task SavePendingMessageAsync_ValidMessage_ShouldReturnSuccess()
    {
        // Arrange
        var clientId = "test-client-006";
        var publishPacket = MqttPublishPacket.Create(
            "test/topic",
            System.Text.Encoding.UTF8.GetBytes("test message"),
            MqttQoSLevel.AtLeastOnce,
            false,
            1001);

        var pendingMessage = new PendingMessage
        {
            ClientId = clientId,
            MessageId = 1001,
            PublishPacket = publishPacket,
            QoSLevel = MqttQoSLevel.AtLeastOnce,
            CreatedAt = DateTime.UtcNow
        };

        // Act
        var result = await _sessionPersistence.SavePendingMessageAsync(clientId, pendingMessage);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task GetPendingMessagesAsync_ExistingMessages_ShouldReturnMessages()
    {
        // Arrange
        var clientId = "test-client-007";
        var publishPacket = MqttPublishPacket.Create(
            "test/topic",
            System.Text.Encoding.UTF8.GetBytes("test message"),
            MqttQoSLevel.AtLeastOnce,
            false,
            1002);

        var pendingMessage = new PendingMessage
        {
            ClientId = clientId,
            MessageId = 1002,
            PublishPacket = publishPacket,
            QoSLevel = MqttQoSLevel.AtLeastOnce,
            CreatedAt = DateTime.UtcNow
        };

        await _sessionPersistence.SavePendingMessageAsync(clientId, pendingMessage);

        // Act
        var retrievedMessages = await _sessionPersistence.GetPendingMessagesAsync(clientId);

        // Assert
        Assert.Single(retrievedMessages);
        var message = retrievedMessages.First();
        Assert.Equal(1002, message.MessageId);
        Assert.Equal("test/topic", message.PublishPacket.Topic);
    }

    [Fact]
    public async Task DeletePendingMessageAsync_ExistingMessage_ShouldReturnSuccess()
    {
        // Arrange
        var clientId = "test-client-008";
        var publishPacket = MqttPublishPacket.Create(
            "test/topic",
            System.Text.Encoding.UTF8.GetBytes("test message"),
            MqttQoSLevel.AtLeastOnce,
            false,
            1003);

        var pendingMessage = new PendingMessage
        {
            ClientId = clientId,
            MessageId = 1003,
            PublishPacket = publishPacket,
            QoSLevel = MqttQoSLevel.AtLeastOnce,
            CreatedAt = DateTime.UtcNow
        };

        await _sessionPersistence.SavePendingMessageAsync(clientId, pendingMessage);

        // Act
        var result = await _sessionPersistence.DeletePendingMessageAsync(clientId, 1003);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(1, result.AffectedRecords);

        // Verify message is deleted
        var retrievedMessages = await _sessionPersistence.GetPendingMessagesAsync(clientId);
        Assert.Empty(retrievedMessages);
    }

    [Fact]
    public async Task CleanupExpiredSessionsAsync_ExpiredSessions_ShouldCleanup()
    {
        // Arrange
        var expiredSession = new MqttSession
        {
            ClientId = "expired-client",
            IsCleanSession = false,
            CreatedAt = DateTime.UtcNow.AddDays(-2),
            ExpiresAt = DateTime.UtcNow.AddHours(-1) // 1 hour ago
        };

        var activeSession = new MqttSession
        {
            ClientId = "active-client",
            IsCleanSession = false,
            CreatedAt = DateTime.UtcNow,
            ExpiresAt = DateTime.UtcNow.AddHours(1) // 1 hour from now
        };

        await _sessionPersistence.CreateOrUpdateSessionAsync(expiredSession);
        await _sessionPersistence.CreateOrUpdateSessionAsync(activeSession);

        // Act
        var result = await _sessionPersistence.CleanupExpiredSessionsAsync(DateTime.UtcNow);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(1, result.CleanedSessionsCount);

        // Verify expired session is deleted and active session remains
        var expiredRetrieved = await _sessionPersistence.GetSessionAsync("expired-client");
        var activeRetrieved = await _sessionPersistence.GetSessionAsync("active-client");

        Assert.Null(expiredRetrieved);
        Assert.NotNull(activeRetrieved);
    }

    [Fact]
    public async Task GetStatisticsAsync_WithSessions_ShouldReturnCorrectStatistics()
    {
        // Arrange
        var session1 = new MqttSession
        {
            ClientId = "stats-client-1",
            IsCleanSession = true,
            State = SessionState.Active
        };

        var session2 = new MqttSession
        {
            ClientId = "stats-client-2",
            IsCleanSession = false,
            State = SessionState.Offline
        };

        await _sessionPersistence.CreateOrUpdateSessionAsync(session1);
        await _sessionPersistence.CreateOrUpdateSessionAsync(session2);

        // Act
        var statistics = await _sessionPersistence.GetStatisticsAsync();

        // Assert
        Assert.Equal(2, statistics.TotalSessions);
        Assert.Equal(1, statistics.ActiveSessions);
        Assert.Equal(1, statistics.OfflineSessions);
        Assert.Equal(1, statistics.CleanSessions);
        Assert.Equal(1, statistics.PersistentSessions);
    }
}
