using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Options;

namespace MqttBroker.Core.Will;

/// <summary>
/// 遗嘱消息处理服务注册扩展
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加遗嘱消息处理服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddWillMessageProcessing(this IServiceCollection services)
    {
        return services.AddWillMessageProcessing(options => { });
    }

    /// <summary>
    /// 添加遗嘱消息处理服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureOptions">配置选项</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddWillMessageProcessing(
        this IServiceCollection services,
        Action<WillMessageOptions> configureOptions)
    {
        if (services == null)
            throw new ArgumentNullException(nameof(services));

        if (configureOptions == null)
            throw new ArgumentNullException(nameof(configureOptions));

        // 配置选项
        services.Configure(configureOptions);

        // 验证配置选项
        services.AddSingleton<IValidateOptions<WillMessageOptions>, WillMessageOptionsValidator>();

        // 注册核心服务
        services.TryAddSingleton<IWillMessageStorage, InMemoryWillMessageStorage>();
        services.TryAddSingleton<IWillMessageManager, WillMessageManager>();

        // 注册后台清理服务
        services.AddHostedService<WillMessageCleanupService>();

        return services;
    }

    /// <summary>
    /// 添加高性能遗嘱消息处理服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddHighPerformanceWillMessageProcessing(this IServiceCollection services)
    {
        return services.AddWillMessageProcessing(options =>
        {
            var highPerfOptions = WillMessageOptions.CreateHighPerformance();
            options.MaxConcurrentWillMessageProcessing = highPerfOptions.MaxConcurrentWillMessageProcessing;
            options.WillMessageProcessingTimeoutMs = highPerfOptions.WillMessageProcessingTimeoutMs;
            options.EnableBatchOptimization = highPerfOptions.EnableBatchOptimization;
            options.BatchSize = highPerfOptions.BatchSize;
            options.CleanupIntervalMinutes = highPerfOptions.CleanupIntervalMinutes;
            options.TriggerDelayMs = highPerfOptions.TriggerDelayMs;
            options.EnableCompression = highPerfOptions.EnableCompression;
            options.CompressionThreshold = highPerfOptions.CompressionThreshold;
        });
    }

    /// <summary>
    /// 添加低资源遗嘱消息处理服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddLowResourceWillMessageProcessing(this IServiceCollection services)
    {
        return services.AddWillMessageProcessing(options =>
        {
            var lowResourceOptions = WillMessageOptions.CreateLowResource();
            options.MaxConcurrentWillMessageProcessing = lowResourceOptions.MaxConcurrentWillMessageProcessing;
            options.WillMessageProcessingTimeoutMs = lowResourceOptions.WillMessageProcessingTimeoutMs;
            options.MaxPayloadSize = lowResourceOptions.MaxPayloadSize;
            options.EnableBatchOptimization = lowResourceOptions.EnableBatchOptimization;
            options.BatchSize = lowResourceOptions.BatchSize;
            options.CleanupIntervalMinutes = lowResourceOptions.CleanupIntervalMinutes;
            options.EnablePerformanceMonitoring = lowResourceOptions.EnablePerformanceMonitoring;
            options.EnableCompression = lowResourceOptions.EnableCompression;
        });
    }

    /// <summary>
    /// 配置遗嘱消息处理选项
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureOptions">配置选项</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection ConfigureWillMessageProcessing(
        this IServiceCollection services,
        Action<WillMessageOptions> configureOptions)
    {
        if (services == null)
            throw new ArgumentNullException(nameof(services));

        if (configureOptions == null)
            throw new ArgumentNullException(nameof(configureOptions));

        services.Configure(configureOptions);
        return services;
    }

    /// <summary>
    /// 添加自定义遗嘱消息存储
    /// </summary>
    /// <typeparam name="TStorage">存储实现类型</typeparam>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddWillMessageStorage<TStorage>(this IServiceCollection services)
        where TStorage : class, IWillMessageStorage
    {
        services.Replace(ServiceDescriptor.Singleton<IWillMessageStorage, TStorage>());
        return services;
    }

    /// <summary>
    /// 添加自定义遗嘱消息管理器
    /// </summary>
    /// <typeparam name="TManager">管理器实现类型</typeparam>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddWillMessageManager<TManager>(this IServiceCollection services)
        where TManager : class, IWillMessageManager
    {
        services.Replace(ServiceDescriptor.Singleton<IWillMessageManager, TManager>());
        return services;
    }
}

/// <summary>
/// 遗嘱消息配置选项验证器
/// </summary>
internal class WillMessageOptionsValidator : IValidateOptions<WillMessageOptions>
{
    /// <summary>
    /// 验证配置选项
    /// </summary>
    /// <param name="name">配置名称</param>
    /// <param name="options">配置选项</param>
    /// <returns>验证结果</returns>
    public ValidateOptionsResult Validate(string? name, WillMessageOptions options)
    {
        if (options == null)
        {
            return ValidateOptionsResult.Fail("WillMessageOptions cannot be null");
        }

        var validationResult = options.Validate();
        if (!validationResult.IsValid)
        {
            return ValidateOptionsResult.Fail(validationResult.GetErrorMessage());
        }

        return ValidateOptionsResult.Success;
    }
}
