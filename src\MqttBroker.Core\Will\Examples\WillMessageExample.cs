using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Message;

namespace MqttBroker.Core.Will.Examples;

/// <summary>
/// 遗嘱消息处理使用示例
/// </summary>
public class WillMessageExample
{
    /// <summary>
    /// 运行示例
    /// </summary>
    public static async Task RunAsync()
    {
        // 创建服务容器
        var services = new ServiceCollection();
        
        // 添加日志服务
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        
        // 添加必要的依赖服务（模拟）
        services.AddSingleton<MockMessageRoutingEngine>();
        services.AddSingleton<IMessageRoutingEngine>(provider => provider.GetRequiredService<MockMessageRoutingEngine>());
        
        // 添加遗嘱消息处理服务
        services.AddWillMessageProcessing(options =>
        {
            options.EnableWillMessageProcessing = true;
            options.MaxWillMessagesPerClient = 1;
            options.WillMessageExpirationHours = 24;
            options.MaxTopicLength = 1024;
            options.MaxPayloadSize = 256 * 1024; // 256KB
            options.EnableAutomaticCleanup = true;
            options.CleanupIntervalMinutes = 60;
            options.MaxConcurrentWillMessageProcessing = 1000;
            options.WillMessageProcessingTimeoutMs = 5000;
            options.EnableStatistics = true;
            options.EnablePerformanceMonitoring = true;
            options.TriggerDelayMs = 1000;
        });

        // 构建服务提供者
        var serviceProvider = services.BuildServiceProvider();
        var logger = serviceProvider.GetRequiredService<ILogger<WillMessageExample>>();

        logger.LogInformation("=== MQTT Broker 遗嘱消息处理示例 ===");

        // 演示遗嘱消息功能
        await DemonstrateWillMessageFunctionalityAsync(serviceProvider);
    }

    /// <summary>
    /// 演示遗嘱消息功能
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    public static async Task DemonstrateWillMessageFunctionalityAsync(IServiceProvider serviceProvider)
    {
        var willMessageManager = serviceProvider.GetRequiredService<IWillMessageManager>();
        var logger = serviceProvider.GetRequiredService<ILogger<WillMessageExample>>();

        // 订阅事件
        willMessageManager.WillMessageRegistered += (sender, args) =>
        {
            logger.LogInformation("Will message registered: ClientId={ClientId}, Success={Success}",
                args.Registration.ClientId, args.Result.IsSuccess);
        };

        willMessageManager.WillMessageTriggered += (sender, args) =>
        {
            logger.LogInformation("Will message triggered: ClientId={ClientId}, Success={Success}, Published={Published}, Topic={Topic}",
                args.Result.ClientId, args.Result.IsSuccess, args.Result.WillMessagePublished, args.Result.WillTopic);
        };

        willMessageManager.WillMessageCleared += (sender, args) =>
        {
            logger.LogInformation("Will message cleared: ClientId={ClientId}, Success={Success}, Existed={Existed}",
                args.Result.ClientId, args.Result.IsSuccess, args.Result.WillMessageExisted);
        };

        // 1. 注册遗嘱消息
        logger.LogInformation("=== 1. 注册遗嘱消息 ===");
        
        var willMessage1 = new MqttWillMessage
        {
            Topic = "devices/sensor1/status",
            Payload = System.Text.Encoding.UTF8.GetBytes("offline"),
            QoSLevel = MqttQoSLevel.AtLeastOnce,
            Retain = true
        };

        var willMessage2 = new MqttWillMessage
        {
            Topic = "devices/sensor2/status",
            Payload = System.Text.Encoding.UTF8.GetBytes("disconnected"),
            QoSLevel = MqttQoSLevel.ExactlyOnce,
            Retain = false
        };

        var registerResult1 = await willMessageManager.RegisterWillMessageAsync("client1", willMessage1);
        var registerResult2 = await willMessageManager.RegisterWillMessageAsync("client2", willMessage2);

        logger.LogInformation("Registration results: Client1={Success1}, Client2={Success2}",
            registerResult1.IsSuccess, registerResult2.IsSuccess);

        // 2. 查询遗嘱消息
        logger.LogInformation("=== 2. 查询遗嘱消息 ===");
        
        var hasWill1 = await willMessageManager.HasWillMessageAsync("client1");
        var hasWill2 = await willMessageManager.HasWillMessageAsync("client2");
        var hasWill3 = await willMessageManager.HasWillMessageAsync("client3");

        logger.LogInformation("Has will message: Client1={HasWill1}, Client2={HasWill2}, Client3={HasWill3}",
            hasWill1, hasWill2, hasWill3);

        var willReg1 = await willMessageManager.GetWillMessageAsync("client1");
        if (willReg1 != null)
        {
            logger.LogInformation("Client1 will message: Topic={Topic}, QoS={QoS}, Retain={Retain}",
                willReg1.Topic, willReg1.QoSLevel, willReg1.Retain);
        }

        // 3. 触发遗嘱消息
        logger.LogInformation("=== 3. 触发遗嘱消息 ===");
        
        var triggerResult1 = await willMessageManager.TriggerWillMessageAsync("client1", WillMessageTriggerCondition.NetworkFailure);
        var triggerResult2 = await willMessageManager.TriggerWillMessageAsync("client2", WillMessageTriggerCondition.KeepAliveTimeout);
        var triggerResult3 = await willMessageManager.TriggerWillMessageAsync("client3", WillMessageTriggerCondition.UnexpectedDisconnection);

        logger.LogInformation("Trigger results: Client1={Success1}, Client2={Success2}, Client3={Success3}",
            triggerResult1.IsSuccess, triggerResult2.IsSuccess, triggerResult3.IsSuccess);

        // 4. 清除遗嘱消息
        logger.LogInformation("=== 4. 清除遗嘱消息 ===");
        
        var clearResult1 = await willMessageManager.ClearWillMessageAsync("client1");
        var clearResult2 = await willMessageManager.ClearWillMessageAsync("client2");

        logger.LogInformation("Clear results: Client1={Success1}, Client2={Success2}",
            clearResult1.IsSuccess, clearResult2.IsSuccess);

        // 5. 批量操作测试
        logger.LogInformation("=== 5. 批量操作测试 ===");
        
        // 注册多个遗嘱消息
        var batchClients = new[] { "batch1", "batch2", "batch3", "batch4", "batch5" };
        foreach (var clientId in batchClients)
        {
            var willMessage = new MqttWillMessage
            {
                Topic = $"devices/{clientId}/status",
                Payload = System.Text.Encoding.UTF8.GetBytes("offline"),
                QoSLevel = MqttQoSLevel.AtMostOnce,
                Retain = false
            };

            await willMessageManager.RegisterWillMessageAsync(clientId, willMessage);
        }

        // 获取所有遗嘱消息
        var allWillMessages = await willMessageManager.GetAllWillMessagesAsync();
        logger.LogInformation("Total will messages: {Count}", allWillMessages.Count);

        // 6. 统计信息
        logger.LogInformation("=== 6. 统计信息 ===");
        
        var statistics = await willMessageManager.GetStatisticsAsync();
        logger.LogInformation("Statistics: Total={Total}, Active={Active}, Triggered={Triggered}, " +
                             "Registrations={Registrations}, Triggers={Triggers}, Clears={Clears}, " +
                             "Failures={Failures}, AvgProcessingTime={AvgProcessingTime}ms",
            statistics.TotalWillMessages,
            statistics.ActiveWillMessages,
            statistics.TriggeredWillMessages,
            statistics.TotalRegistrations,
            statistics.TotalTriggers,
            statistics.TotalClears,
            statistics.TotalFailures,
            statistics.AverageProcessingTimeMs);

        // 7. 清理测试
        logger.LogInformation("=== 7. 清理测试 ===");
        
        var cleanupResult = await willMessageManager.CleanupExpiredWillMessagesAsync();
        logger.LogInformation("Cleanup result: Success={Success}, CleanedCount={CleanedCount}, ProcessingTime={ProcessingTime}ms",
            cleanupResult.IsSuccess, cleanupResult.CleanedCount, cleanupResult.ProcessingTimeMs);

        logger.LogInformation("=== 遗嘱消息处理示例完成 ===");
    }
}

/// <summary>
/// 模拟消息路由引擎（用于示例）
/// </summary>
internal class MockMessageRoutingEngine : IMessageRoutingEngine
{
    private readonly ILogger<MockMessageRoutingEngine> _logger;

    public MockMessageRoutingEngine(ILogger<MockMessageRoutingEngine> logger)
    {
        _logger = logger;
    }

    public event EventHandler<MessageRoutedEventArgs>? MessageRouted;
    public event EventHandler<MessageRoutingFailedEventArgs>? MessageRoutingFailed;
    public event EventHandler<OfflineMessageStoredEventArgs>? OfflineMessageStored;

    public Task<MessageRoutingResult> RouteMessageAsync(MqttPublishPacket publishPacket, string? publisherClientId = null, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Routing will message: Topic={Topic}, Publisher={Publisher}", 
            publishPacket.Topic, publisherClientId ?? "system");

        // 模拟成功路由
        var result = MessageRoutingResult.Success(publishPacket.Topic, 1, 0, 0, 0, 1);
        MessageRouted?.Invoke(this, new MessageRoutedEventArgs(result, publishPacket, publisherClientId));

        return Task.FromResult(result);
    }

    public Task<IList<MessageRoutingResult>> RouteMessagesAsync(IList<MessageRoutingRequest> messages, CancellationToken cancellationToken = default)
    {
        var results = new List<MessageRoutingResult>();
        foreach (var message in messages)
        {
            var result = MessageRoutingResult.Success(message.PublishPacket.Topic, 1, 0, 0, 0, 1);
            results.Add(result);
        }
        return Task.FromResult<IList<MessageRoutingResult>>(results);
    }

    public Task<OfflineMessageResult> ProcessOfflineMessagesAsync(string clientId, CancellationToken cancellationToken = default)
    {
        return Task.FromResult(new OfflineMessageResult
        {
            ClientId = clientId,
            IsSuccess = true,
            ProcessedMessages = 0,
            DeliveredMessages = 0,
            FailedMessages = 0,
            ElapsedMilliseconds = 1
        });
    }

    public Task<MessageRoutingStatistics> GetStatisticsAsync()
    {
        return Task.FromResult(new MessageRoutingStatistics());
    }
}
