using MqttBroker.Metrics.Models;

namespace MqttBroker.Metrics.Storage;

/// <summary>
/// 指标存储接口
/// </summary>
public interface IMetricsStorage
{
    /// <summary>
    /// 存储性能指标
    /// </summary>
    /// <param name="metrics">性能指标数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task StoreMetricsAsync(PerformanceMetrics metrics, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量存储性能指标
    /// </summary>
    /// <param name="metricsList">性能指标数据列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task StoreMetricsBatchAsync(IEnumerable<PerformanceMetrics> metricsList, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取指定时间范围内的性能指标
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>性能指标数据列表</returns>
    Task<IEnumerable<PerformanceMetrics>> GetMetricsAsync(DateTime startTime, DateTime endTime, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取最新的性能指标
    /// </summary>
    /// <param name="count">获取数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>性能指标数据列表</returns>
    Task<IEnumerable<PerformanceMetrics>> GetLatestMetricsAsync(int count = 1, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取聚合指标数据
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="aggregationInterval">聚合间隔（秒）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>聚合指标数据</returns>
    Task<IEnumerable<AggregatedMetrics>> GetAggregatedMetricsAsync(DateTime startTime, DateTime endTime, int aggregationInterval, CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理过期的指标数据
    /// </summary>
    /// <param name="retentionPeriod">保留期间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理的记录数</returns>
    Task<int> CleanupExpiredMetricsAsync(TimeSpan retentionPeriod, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取存储统计信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>存储统计信息</returns>
    Task<MetricsStorageStatistics> GetStorageStatisticsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 聚合指标数据
/// </summary>
public class AggregatedMetrics
{
    /// <summary>
    /// 时间窗口开始时间
    /// </summary>
    public DateTime WindowStart { get; set; }

    /// <summary>
    /// 时间窗口结束时间
    /// </summary>
    public DateTime WindowEnd { get; set; }

    /// <summary>
    /// 聚合间隔（秒）
    /// </summary>
    public int IntervalSeconds { get; set; }

    /// <summary>
    /// 数据点数量
    /// </summary>
    public int DataPointCount { get; set; }

    /// <summary>
    /// 连接指标聚合
    /// </summary>
    public ConnectionMetricsAggregation Connection { get; set; } = new();

    /// <summary>
    /// 消息指标聚合
    /// </summary>
    public MessageMetricsAggregation Message { get; set; } = new();

    /// <summary>
    /// 订阅指标聚合
    /// </summary>
    public SubscriptionMetricsAggregation Subscription { get; set; } = new();

    /// <summary>
    /// QoS 指标聚合
    /// </summary>
    public QoSMetricsAggregation QoS { get; set; } = new();

    /// <summary>
    /// 系统指标聚合
    /// </summary>
    public SystemMetricsAggregation System { get; set; } = new();

    /// <summary>
    /// 网络指标聚合
    /// </summary>
    public NetworkMetricsAggregation Network { get; set; } = new();
}

/// <summary>
/// 连接指标聚合
/// </summary>
public class ConnectionMetricsAggregation
{
    /// <summary>
    /// 平均活跃连接数
    /// </summary>
    public double AverageActiveConnections { get; set; }

    /// <summary>
    /// 最大活跃连接数
    /// </summary>
    public long MaxActiveConnections { get; set; }

    /// <summary>
    /// 最小活跃连接数
    /// </summary>
    public long MinActiveConnections { get; set; }

    /// <summary>
    /// 总连接数增量
    /// </summary>
    public long TotalConnectionsIncrement { get; set; }

    /// <summary>
    /// 平均连接速率
    /// </summary>
    public double AverageConnectionRate { get; set; }

    /// <summary>
    /// 平均断开速率
    /// </summary>
    public double AverageDisconnectionRate { get; set; }

    /// <summary>
    /// 认证失败总数
    /// </summary>
    public long AuthenticationFailures { get; set; }

    /// <summary>
    /// 连接超时总数
    /// </summary>
    public long ConnectionTimeouts { get; set; }
}

/// <summary>
/// 消息指标聚合
/// </summary>
public class MessageMetricsAggregation
{
    /// <summary>
    /// 发送消息总数
    /// </summary>
    public long MessagesSent { get; set; }

    /// <summary>
    /// 接收消息总数
    /// </summary>
    public long MessagesReceived { get; set; }

    /// <summary>
    /// 平均消息发送速率
    /// </summary>
    public double AverageMessageSendRate { get; set; }

    /// <summary>
    /// 平均消息接收速率
    /// </summary>
    public double AverageMessageReceiveRate { get; set; }

    /// <summary>
    /// 平均消息大小
    /// </summary>
    public double AverageMessageSize { get; set; }

    /// <summary>
    /// 路由延迟聚合
    /// </summary>
    public LatencyAggregation RoutingLatency { get; set; } = new();

    /// <summary>
    /// 离线消息总数
    /// </summary>
    public long OfflineMessages { get; set; }

    /// <summary>
    /// 死信消息总数
    /// </summary>
    public long DeadLetterMessages { get; set; }

    /// <summary>
    /// 保留消息总数
    /// </summary>
    public long RetainedMessages { get; set; }
}

/// <summary>
/// 订阅指标聚合
/// </summary>
public class SubscriptionMetricsAggregation
{
    /// <summary>
    /// 平均订阅总数
    /// </summary>
    public double AverageTotalSubscriptions { get; set; }

    /// <summary>
    /// 平均活跃主题数
    /// </summary>
    public double AverageActiveTopics { get; set; }

    /// <summary>
    /// 平均通配符订阅数
    /// </summary>
    public double AverageWildcardSubscriptions { get; set; }

    /// <summary>
    /// 匹配延迟聚合
    /// </summary>
    public LatencyAggregation MatchingLatency { get; set; } = new();

    /// <summary>
    /// 平均订阅速率
    /// </summary>
    public double AverageSubscriptionRate { get; set; }

    /// <summary>
    /// 平均取消订阅速率
    /// </summary>
    public double AverageUnsubscriptionRate { get; set; }
}

/// <summary>
/// QoS 指标聚合
/// </summary>
public class QoSMetricsAggregation
{
    /// <summary>
    /// QoS 0 消息总数
    /// </summary>
    public long QoS0Messages { get; set; }

    /// <summary>
    /// QoS 1 消息总数
    /// </summary>
    public long QoS1Messages { get; set; }

    /// <summary>
    /// QoS 2 消息总数
    /// </summary>
    public long QoS2Messages { get; set; }

    /// <summary>
    /// 平均待确认消息数
    /// </summary>
    public double AveragePendingAcknowledgments { get; set; }

    /// <summary>
    /// 处理延迟聚合
    /// </summary>
    public LatencyAggregation ProcessingLatency { get; set; } = new();

    /// <summary>
    /// 消息重传总数
    /// </summary>
    public long MessageRetransmissions { get; set; }

    /// <summary>
    /// 平均成功率
    /// </summary>
    public double AverageSuccessRate { get; set; }
}

/// <summary>
/// 系统指标聚合
/// </summary>
public class SystemMetricsAggregation
{
    /// <summary>
    /// 平均 CPU 使用率
    /// </summary>
    public double AverageCpuUsage { get; set; }

    /// <summary>
    /// 最大 CPU 使用率
    /// </summary>
    public double MaxCpuUsage { get; set; }

    /// <summary>
    /// 平均内存使用量
    /// </summary>
    public double AverageMemoryUsage { get; set; }

    /// <summary>
    /// 最大内存使用量
    /// </summary>
    public long MaxMemoryUsage { get; set; }

    /// <summary>
    /// GC 收集总次数
    /// </summary>
    public int TotalGCCollections { get; set; }
}

/// <summary>
/// 网络指标聚合
/// </summary>
public class NetworkMetricsAggregation
{
    /// <summary>
    /// 发送字节总数
    /// </summary>
    public long BytesSent { get; set; }

    /// <summary>
    /// 接收字节总数
    /// </summary>
    public long BytesReceived { get; set; }

    /// <summary>
    /// 平均发送速率
    /// </summary>
    public double AverageSendRate { get; set; }

    /// <summary>
    /// 平均接收速率
    /// </summary>
    public double AverageReceiveRate { get; set; }

    /// <summary>
    /// 网络错误总数
    /// </summary>
    public long NetworkErrors { get; set; }

    /// <summary>
    /// 连接重置总数
    /// </summary>
    public long ConnectionResets { get; set; }
}

/// <summary>
/// 延迟聚合数据
/// </summary>
public class LatencyAggregation
{
    /// <summary>
    /// 平均延迟
    /// </summary>
    public double Average { get; set; }

    /// <summary>
    /// 最小延迟
    /// </summary>
    public double Min { get; set; }

    /// <summary>
    /// 最大延迟
    /// </summary>
    public double Max { get; set; }

    /// <summary>
    /// P50 延迟
    /// </summary>
    public double P50 { get; set; }

    /// <summary>
    /// P95 延迟
    /// </summary>
    public double P95 { get; set; }

    /// <summary>
    /// P99 延迟
    /// </summary>
    public double P99 { get; set; }
}

/// <summary>
/// 指标存储统计信息
/// </summary>
public class MetricsStorageStatistics
{
    /// <summary>
    /// 总记录数
    /// </summary>
    public long TotalRecords { get; set; }

    /// <summary>
    /// 存储大小（字节）
    /// </summary>
    public long StorageSize { get; set; }

    /// <summary>
    /// 最早记录时间
    /// </summary>
    public DateTime? EarliestRecord { get; set; }

    /// <summary>
    /// 最新记录时间
    /// </summary>
    public DateTime? LatestRecord { get; set; }

    /// <summary>
    /// 平均写入延迟（毫秒）
    /// </summary>
    public double AverageWriteLatency { get; set; }

    /// <summary>
    /// 平均读取延迟（毫秒）
    /// </summary>
    public double AverageReadLatency { get; set; }
}
