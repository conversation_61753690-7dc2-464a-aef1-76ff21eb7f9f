using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using MqttBroker.Metrics.Configuration;
using MqttBroker.Metrics.Models;
using MqttBroker.Metrics.Storage;
using Xunit;

namespace MqttBroker.Metrics.Tests.Storage;

/// <summary>
/// 内存指标存储测试
/// </summary>
public class InMemoryMetricsStorageTests
{
    private readonly Mock<ILogger<InMemoryMetricsStorage>> _mockLogger;
    private readonly IOptions<MetricsOptions> _options;

    public InMemoryMetricsStorageTests()
    {
        _mockLogger = new Mock<ILogger<InMemoryMetricsStorage>>();
        _options = Options.Create(new MetricsOptions
        {
            MaxInMemoryDataPoints = 100,
            DataRetentionHours = 24
        });
    }

    [Fact]
    public async Task StoreMetricsAsync_ShouldStoreMetrics()
    {
        // Arrange
        var storage = new InMemoryMetricsStorage(_mockLogger.Object, _options);
        var metrics = new PerformanceMetrics
        {
            Timestamp = DateTime.UtcNow,
            Connection = new ConnectionMetrics { ActiveConnections = 10 }
        };

        // Act
        await storage.StoreMetricsAsync(metrics);

        // Assert
        var latest = await storage.GetLatestMetricsAsync(1);
        var latestMetrics = latest.FirstOrDefault();
        Assert.NotNull(latestMetrics);
        Assert.Equal(10, latestMetrics.Connection.ActiveConnections);
    }

    [Fact]
    public async Task StoreMetricsBatchAsync_ShouldStoreBatchMetrics()
    {
        // Arrange
        var storage = new InMemoryMetricsStorage(_mockLogger.Object, _options);
        var metricsList = new List<PerformanceMetrics>
        {
            new() { Timestamp = DateTime.UtcNow.AddMinutes(-2), Connection = new ConnectionMetrics { ActiveConnections = 5 } },
            new() { Timestamp = DateTime.UtcNow.AddMinutes(-1), Connection = new ConnectionMetrics { ActiveConnections = 10 } },
            new() { Timestamp = DateTime.UtcNow, Connection = new ConnectionMetrics { ActiveConnections = 15 } }
        };

        // Act
        await storage.StoreMetricsBatchAsync(metricsList);

        // Assert
        var latest = await storage.GetLatestMetricsAsync(3);
        var latestList = latest.ToList();
        Assert.Equal(3, latestList.Count);
        Assert.Equal(5, latestList[0].Connection.ActiveConnections);
        Assert.Equal(10, latestList[1].Connection.ActiveConnections);
        Assert.Equal(15, latestList[2].Connection.ActiveConnections);
    }

    [Fact]
    public async Task GetMetricsAsync_WithTimeRange_ShouldReturnFilteredMetrics()
    {
        // Arrange
        var storage = new InMemoryMetricsStorage(_mockLogger.Object, _options);
        var baseTime = DateTime.UtcNow;
        
        var metricsList = new List<PerformanceMetrics>
        {
            new() { Timestamp = baseTime.AddMinutes(-10), Connection = new ConnectionMetrics { ActiveConnections = 5 } },
            new() { Timestamp = baseTime.AddMinutes(-5), Connection = new ConnectionMetrics { ActiveConnections = 10 } },
            new() { Timestamp = baseTime, Connection = new ConnectionMetrics { ActiveConnections = 15 } }
        };

        await storage.StoreMetricsBatchAsync(metricsList);

        // Act
        var result = await storage.GetMetricsAsync(baseTime.AddMinutes(-7), baseTime.AddMinutes(-3));

        // Assert
        var resultList = result.ToList();
        Assert.Single(resultList);
        Assert.Equal(10, resultList[0].Connection.ActiveConnections);
    }

    [Fact]
    public async Task GetLatestMetricsAsync_ShouldReturnLatestMetrics()
    {
        // Arrange
        var storage = new InMemoryMetricsStorage(_mockLogger.Object, _options);
        var baseTime = DateTime.UtcNow;
        
        var metricsList = new List<PerformanceMetrics>
        {
            new() { Timestamp = baseTime.AddMinutes(-2), Connection = new ConnectionMetrics { ActiveConnections = 5 } },
            new() { Timestamp = baseTime.AddMinutes(-1), Connection = new ConnectionMetrics { ActiveConnections = 10 } },
            new() { Timestamp = baseTime, Connection = new ConnectionMetrics { ActiveConnections = 15 } }
        };

        await storage.StoreMetricsBatchAsync(metricsList);

        // Act
        var result = await storage.GetLatestMetricsAsync(2);

        // Assert
        var resultList = result.ToList();
        Assert.Equal(2, resultList.Count);
        Assert.Equal(10, resultList[0].Connection.ActiveConnections); // 第二新的
        Assert.Equal(15, resultList[1].Connection.ActiveConnections); // 最新的
    }

    [Fact]
    public async Task GetAggregatedMetricsAsync_ShouldReturnAggregatedData()
    {
        // Arrange
        var storage = new InMemoryMetricsStorage(_mockLogger.Object, _options);
        var baseTime = DateTime.UtcNow.Date; // 使用整点时间便于测试
        
        var metricsList = new List<PerformanceMetrics>
        {
            new() { Timestamp = baseTime.AddMinutes(1), Connection = new ConnectionMetrics { ActiveConnections = 5 } },
            new() { Timestamp = baseTime.AddMinutes(2), Connection = new ConnectionMetrics { ActiveConnections = 10 } },
            new() { Timestamp = baseTime.AddMinutes(61), Connection = new ConnectionMetrics { ActiveConnections = 15 } },
            new() { Timestamp = baseTime.AddMinutes(62), Connection = new ConnectionMetrics { ActiveConnections = 20 } }
        };

        await storage.StoreMetricsBatchAsync(metricsList);

        // Act
        var result = await storage.GetAggregatedMetricsAsync(baseTime, baseTime.AddHours(2), 3600); // 1小时间隔

        // Assert
        var resultList = result.ToList();
        Assert.Equal(2, resultList.Count); // 两个小时窗口
        
        var firstWindow = resultList[0];
        Assert.Equal(2, firstWindow.DataPointCount);
        Assert.Equal(7.5, firstWindow.Connection.AverageActiveConnections); // (5+10)/2
        Assert.Equal(10, firstWindow.Connection.MaxActiveConnections);
        Assert.Equal(5, firstWindow.Connection.MinActiveConnections);
    }

    [Fact]
    public async Task CleanupExpiredMetricsAsync_ShouldRemoveExpiredData()
    {
        // Arrange
        var storage = new InMemoryMetricsStorage(_mockLogger.Object, _options);
        var baseTime = DateTime.UtcNow;
        
        var metricsList = new List<PerformanceMetrics>
        {
            new() { Timestamp = baseTime.AddHours(-25), Connection = new ConnectionMetrics { ActiveConnections = 5 } }, // 过期
            new() { Timestamp = baseTime.AddHours(-1), Connection = new ConnectionMetrics { ActiveConnections = 10 } }, // 未过期
            new() { Timestamp = baseTime, Connection = new ConnectionMetrics { ActiveConnections = 15 } } // 未过期
        };

        await storage.StoreMetricsBatchAsync(metricsList);

        // Act
        var removedCount = await storage.CleanupExpiredMetricsAsync(TimeSpan.FromHours(24));

        // Assert
        Assert.Equal(1, removedCount);
        
        var remaining = await storage.GetLatestMetricsAsync(10);
        var remainingList = remaining.ToList();
        Assert.Equal(2, remainingList.Count);
        Assert.All(remainingList, m => Assert.True(m.Timestamp >= baseTime.AddHours(-24)));
    }

    [Fact]
    public async Task GetStorageStatisticsAsync_ShouldReturnValidStatistics()
    {
        // Arrange
        var storage = new InMemoryMetricsStorage(_mockLogger.Object, _options);
        var metrics = new PerformanceMetrics
        {
            Timestamp = DateTime.UtcNow,
            Connection = new ConnectionMetrics { ActiveConnections = 10 }
        };

        await storage.StoreMetricsAsync(metrics);

        // Act
        var statistics = await storage.GetStorageStatisticsAsync();

        // Assert
        Assert.NotNull(statistics);
        Assert.Equal(1, statistics.TotalRecords);
        Assert.True(statistics.StorageSize > 0);
        Assert.NotNull(statistics.EarliestRecord);
        Assert.NotNull(statistics.LatestRecord);
        Assert.True(statistics.AverageWriteLatency >= 0);
    }

    [Fact]
    public async Task StoreMetricsAsync_WhenExceedsMaxDataPoints_ShouldRemoveOldest()
    {
        // Arrange
        var smallOptions = Options.Create(new MetricsOptions
        {
            MaxInMemoryDataPoints = 2 // 只保留2个数据点
        });
        
        var storage = new InMemoryMetricsStorage(_mockLogger.Object, smallOptions);
        
        var metricsList = new List<PerformanceMetrics>
        {
            new() { Timestamp = DateTime.UtcNow.AddMinutes(-2), Connection = new ConnectionMetrics { ActiveConnections = 5 } },
            new() { Timestamp = DateTime.UtcNow.AddMinutes(-1), Connection = new ConnectionMetrics { ActiveConnections = 10 } },
            new() { Timestamp = DateTime.UtcNow, Connection = new ConnectionMetrics { ActiveConnections = 15 } }
        };

        // Act
        foreach (var metrics in metricsList)
        {
            await storage.StoreMetricsAsync(metrics);
        }

        // Assert
        var all = await storage.GetLatestMetricsAsync(10);
        var allList = all.ToList();
        Assert.Equal(2, allList.Count); // 只保留最新的2个
        Assert.Equal(10, allList[0].Connection.ActiveConnections);
        Assert.Equal(15, allList[1].Connection.ActiveConnections);
    }

    [Fact]
    public async Task StoreMetricsAsync_WithNullMetrics_ShouldThrowArgumentNullException()
    {
        // Arrange
        var storage = new InMemoryMetricsStorage(_mockLogger.Object, _options);

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => storage.StoreMetricsAsync(null!));
    }
}
