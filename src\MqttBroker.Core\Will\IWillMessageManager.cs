using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Client;

namespace MqttBroker.Core.Will;

/// <summary>
/// 遗嘱消息管理器接口
/// </summary>
public interface IWillMessageManager : IDisposable
{
    /// <summary>
    /// 注册客户端遗嘱消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="willMessage">遗嘱消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>注册结果</returns>
    Task<WillMessageRegistrationResult> RegisterWillMessageAsync(string clientId, MqttWillMessage willMessage, CancellationToken cancellationToken = default);

    /// <summary>
    /// 清除客户端遗嘱消息（正常断开连接时调用）
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清除结果</returns>
    Task<WillMessageClearResult> ClearWillMessageAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 触发遗嘱消息发布（异常断开连接时调用）
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="triggerCondition">触发条件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>触发结果</returns>
    Task<WillMessageTriggerResult> TriggerWillMessageAsync(string clientId, WillMessageTriggerCondition triggerCondition, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取客户端遗嘱消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>遗嘱消息注册信息，如果不存在则返回null</returns>
    Task<WillMessageRegistration?> GetWillMessageAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查客户端是否有遗嘱消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>如果有遗嘱消息则返回true，否则返回false</returns>
    Task<bool> HasWillMessageAsync(string clientId);

    /// <summary>
    /// 获取所有遗嘱消息注册信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>所有遗嘱消息注册信息</returns>
    Task<IList<WillMessageRegistration>> GetAllWillMessagesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理过期的遗嘱消息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理结果</returns>
    Task<WillMessageCleanupResult> CleanupExpiredWillMessagesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取遗嘱消息管理器统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    Task<WillMessageStatistics> GetStatisticsAsync();

    /// <summary>
    /// 遗嘱消息注册事件
    /// </summary>
    event EventHandler<WillMessageRegisteredEventArgs>? WillMessageRegistered;

    /// <summary>
    /// 遗嘱消息触发事件
    /// </summary>
    event EventHandler<WillMessageTriggeredEventArgs>? WillMessageTriggered;

    /// <summary>
    /// 遗嘱消息清除事件
    /// </summary>
    event EventHandler<WillMessageClearedEventArgs>? WillMessageCleared;
}

/// <summary>
/// 遗嘱消息触发条件
/// </summary>
public enum WillMessageTriggerCondition
{
    /// <summary>
    /// 异常断开连接
    /// </summary>
    UnexpectedDisconnection,

    /// <summary>
    /// Keep-Alive 超时
    /// </summary>
    KeepAliveTimeout,

    /// <summary>
    /// 网络故障
    /// </summary>
    NetworkFailure,

    /// <summary>
    /// 协议错误
    /// </summary>
    ProtocolError,

    /// <summary>
    /// 服务器关闭
    /// </summary>
    ServerShutdown,

    /// <summary>
    /// 认证失败
    /// </summary>
    AuthenticationFailure,

    /// <summary>
    /// 手动触发（用于测试）
    /// </summary>
    ManualTrigger
}

/// <summary>
/// 遗嘱消息注册结果
/// </summary>
public class WillMessageRegistrationResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 注册时间
    /// </summary>
    public DateTime RegisteredAt { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    public static WillMessageRegistrationResult Success(string clientId, DateTime registeredAt)
    {
        return new WillMessageRegistrationResult
        {
            IsSuccess = true,
            ClientId = clientId,
            RegisteredAt = registeredAt
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    public static WillMessageRegistrationResult Failure(string clientId, string errorMessage)
    {
        return new WillMessageRegistrationResult
        {
            IsSuccess = false,
            ClientId = clientId,
            ErrorMessage = errorMessage,
            RegisteredAt = DateTime.UtcNow
        };
    }
}

/// <summary>
/// 遗嘱消息清除结果
/// </summary>
public class WillMessageClearResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 清除时间
    /// </summary>
    public DateTime ClearedAt { get; set; }

    /// <summary>
    /// 是否存在遗嘱消息
    /// </summary>
    public bool WillMessageExisted { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    public static WillMessageClearResult Success(string clientId, bool willMessageExisted, DateTime clearedAt)
    {
        return new WillMessageClearResult
        {
            IsSuccess = true,
            ClientId = clientId,
            WillMessageExisted = willMessageExisted,
            ClearedAt = clearedAt
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    public static WillMessageClearResult Failure(string clientId, string errorMessage)
    {
        return new WillMessageClearResult
        {
            IsSuccess = false,
            ClientId = clientId,
            ErrorMessage = errorMessage,
            ClearedAt = DateTime.UtcNow
        };
    }
}

/// <summary>
/// 遗嘱消息触发结果
/// </summary>
public class WillMessageTriggerResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 触发条件
    /// </summary>
    public WillMessageTriggerCondition TriggerCondition { get; set; }

    /// <summary>
    /// 触发时间
    /// </summary>
    public DateTime TriggeredAt { get; set; }

    /// <summary>
    /// 是否存在遗嘱消息
    /// </summary>
    public bool WillMessageExisted { get; set; }

    /// <summary>
    /// 是否已发布遗嘱消息
    /// </summary>
    public bool WillMessagePublished { get; set; }

    /// <summary>
    /// 遗嘱消息主题
    /// </summary>
    public string? WillTopic { get; set; }

    /// <summary>
    /// 处理延迟（毫秒）
    /// </summary>
    public long ProcessingTimeMs { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    public static WillMessageTriggerResult Success(string clientId, WillMessageTriggerCondition triggerCondition, 
        bool willMessageExisted, bool willMessagePublished, string? willTopic, long processingTimeMs)
    {
        return new WillMessageTriggerResult
        {
            IsSuccess = true,
            ClientId = clientId,
            TriggerCondition = triggerCondition,
            TriggeredAt = DateTime.UtcNow,
            WillMessageExisted = willMessageExisted,
            WillMessagePublished = willMessagePublished,
            WillTopic = willTopic,
            ProcessingTimeMs = processingTimeMs
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    public static WillMessageTriggerResult Failure(string clientId, WillMessageTriggerCondition triggerCondition, string errorMessage)
    {
        return new WillMessageTriggerResult
        {
            IsSuccess = false,
            ClientId = clientId,
            TriggerCondition = triggerCondition,
            TriggeredAt = DateTime.UtcNow,
            ErrorMessage = errorMessage
        };
    }
}
