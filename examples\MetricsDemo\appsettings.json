{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Metrics": {"EnableMetrics": true, "CollectionIntervalMs": 1000, "DataRetentionHours": 24, "MaxInMemoryDataPoints": 86400, "EnableDetailedMetrics": true, "EnableSystemMetrics": true, "EnableNetworkMetrics": true, "EnableGCMetrics": true, "Aggregation": {"EnableAggregation": true, "AggregationIntervalSeconds": 60, "WindowSize": 60, "KeepRawData": false, "Strategies": ["Average", "Max", "Min"]}, "Storage": {"Type": "Memory", "ConnectionString": "", "DatabaseName": "MqttBrokerMetrics", "TablePrefix": "metrics_", "BatchSize": 1000, "WriteTimeoutMs": 5000, "EnableCompression": true}, "Export": {"EnableRestApi": true, "ApiPort": 8080, "ApiPathPrefix": "/api/metrics", "EnablePrometheus": false, "PrometheusEndpoint": "/metrics", "EnableFileExport": true, "FileExportPath": "./metrics_export", "FileExportFormats": ["Json", "Csv"], "ExportIntervalSeconds": 300}}, "Alerts": {"EnableAlerts": true, "EvaluationIntervalSeconds": 30, "HistoryRetentionDays": 30, "MaxActiveAlerts": 1000, "DefaultRules": [{"Name": "高连接数告警", "MetricPath": "Connection.ActiveConnections", "Operator": "GreaterThan", "Threshold": 1000, "Level": "Warning", "IsEnabled": true}, {"Name": "高消息发送速率告警", "MetricPath": "Message.MessageSendRate", "Operator": "GreaterThan", "Threshold": 10000, "Level": "Error", "IsEnabled": true}, {"Name": "高CPU使用率告警", "MetricPath": "System.CpuUsage", "Operator": "GreaterThan", "Threshold": 80, "Level": "Critical", "IsEnabled": true}, {"Name": "高内存使用告警", "MetricPath": "System.Memory.TotalMemory", "Operator": "GreaterThan", "Threshold": 1073741824, "Level": "Warning", "IsEnabled": true}], "Notification": {"EnableNotifications": false, "Channels": [], "Templates": {"AlertTriggered": "告警触发: {<PERSON><PERSON><PERSON><PERSON>} - {Message}", "AlertResolved": "告警解决: {<PERSON><PERSON><PERSON><PERSON>} - {Message}", "AlertEscalated": "告警升级: {<PERSON><PERSON><PERSON><PERSON>} - {Message}"}}}}