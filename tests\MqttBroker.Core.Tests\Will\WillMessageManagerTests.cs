using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Message;
using MqttBroker.Core.Will;
using Xunit;

namespace MqttBroker.Core.Tests.Will;

/// <summary>
/// 遗嘱消息管理器测试
/// </summary>
public class WillMessageManagerTests : IDisposable
{
    private readonly Mock<IWillMessageStorage> _mockStorage;
    private readonly Mock<IMessageRoutingEngine> _mockRoutingEngine;
    private readonly Mock<ILogger<WillMessageManager>> _mockLogger;
    private readonly WillMessageOptions _options;
    private readonly WillMessageManager _willMessageManager;

    public WillMessageManagerTests()
    {
        _mockStorage = new Mock<IWillMessageStorage>();
        _mockRoutingEngine = new Mock<IMessageRoutingEngine>();
        _mockLogger = new Mock<ILogger<WillMessageManager>>();
        
        _options = new WillMessageOptions
        {
            EnableWillMessageProcessing = true,
            MaxWillMessagesPerClient = 1,
            WillMessageExpirationHours = 24,
            MaxTopicLength = 1024,
            MaxPayloadSize = 256 * 1024,
            DefaultProtocolVersion = MqttProtocolVersion.Version311
        };

        var optionsWrapper = Options.Create(_options);
        _willMessageManager = new WillMessageManager(_mockStorage.Object, _mockRoutingEngine.Object, optionsWrapper, _mockLogger.Object);
    }

    [Fact]
    public async Task RegisterWillMessageAsync_ValidWillMessage_ShouldReturnSuccess()
    {
        // Arrange
        var clientId = "test-client";
        var willMessage = new MqttWillMessage
        {
            Topic = "test/topic",
            Payload = System.Text.Encoding.UTF8.GetBytes("offline"),
            QoSLevel = MqttQoSLevel.AtLeastOnce,
            Retain = true
        };

        _mockStorage.Setup(s => s.StoreWillMessageAsync(It.IsAny<WillMessageRegistration>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(WillMessageStorageResult.Success(clientId, WillMessageStorageOperation.Store));

        // Act
        var result = await _willMessageManager.RegisterWillMessageAsync(clientId, willMessage);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(clientId, result.ClientId);
        Assert.Null(result.ErrorMessage);

        _mockStorage.Verify(s => s.StoreWillMessageAsync(It.IsAny<WillMessageRegistration>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task RegisterWillMessageAsync_EmptyClientId_ShouldReturnFailure()
    {
        // Arrange
        var willMessage = new MqttWillMessage
        {
            Topic = "test/topic",
            Payload = System.Text.Encoding.UTF8.GetBytes("offline"),
            QoSLevel = MqttQoSLevel.AtLeastOnce,
            Retain = true
        };

        // Act
        var result = await _willMessageManager.RegisterWillMessageAsync("", willMessage);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotNull(result.ErrorMessage);
    }

    [Fact]
    public async Task RegisterWillMessageAsync_NullWillMessage_ShouldReturnFailure()
    {
        // Arrange
        var clientId = "test-client";

        // Act
        var result = await _willMessageManager.RegisterWillMessageAsync(clientId, null!);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotNull(result.ErrorMessage);
    }

    [Fact]
    public async Task RegisterWillMessageAsync_EmptyTopic_ShouldReturnFailure()
    {
        // Arrange
        var clientId = "test-client";
        var willMessage = new MqttWillMessage
        {
            Topic = "",
            Payload = System.Text.Encoding.UTF8.GetBytes("offline"),
            QoSLevel = MqttQoSLevel.AtLeastOnce,
            Retain = true
        };

        // Act
        var result = await _willMessageManager.RegisterWillMessageAsync(clientId, willMessage);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(clientId, result.ClientId);
        Assert.NotNull(result.ErrorMessage);
        Assert.Contains("topic cannot be null or empty", result.ErrorMessage);
    }

    [Fact]
    public async Task RegisterWillMessageAsync_TopicTooLong_ShouldReturnFailure()
    {
        // Arrange
        var clientId = "test-client";
        var longTopic = new string('a', _options.MaxTopicLength + 1);
        var willMessage = new MqttWillMessage
        {
            Topic = longTopic,
            Payload = System.Text.Encoding.UTF8.GetBytes("offline"),
            QoSLevel = MqttQoSLevel.AtLeastOnce,
            Retain = true
        };

        // Act
        var result = await _willMessageManager.RegisterWillMessageAsync(clientId, willMessage);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(clientId, result.ClientId);
        Assert.NotNull(result.ErrorMessage);
        Assert.Contains("topic length exceeds maximum", result.ErrorMessage);
    }

    [Fact]
    public async Task RegisterWillMessageAsync_PayloadTooLarge_ShouldReturnFailure()
    {
        // Arrange
        var clientId = "test-client";
        var largePayload = new byte[_options.MaxPayloadSize + 1];
        var willMessage = new MqttWillMessage
        {
            Topic = "test/topic",
            Payload = largePayload,
            QoSLevel = MqttQoSLevel.AtLeastOnce,
            Retain = true
        };

        // Act
        var result = await _willMessageManager.RegisterWillMessageAsync(clientId, willMessage);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(clientId, result.ClientId);
        Assert.NotNull(result.ErrorMessage);
        Assert.Contains("payload size exceeds maximum", result.ErrorMessage);
    }

    [Fact]
    public async Task TriggerWillMessageAsync_ExistingWillMessage_ShouldTriggerAndPublish()
    {
        // Arrange
        var clientId = "test-client";
        var triggerCondition = WillMessageTriggerCondition.NetworkFailure;
        
        var registration = new WillMessageRegistration
        {
            ClientId = clientId,
            Topic = "test/topic",
            Payload = System.Text.Encoding.UTF8.GetBytes("offline"),
            QoSLevel = MqttQoSLevel.AtLeastOnce,
            Retain = true,
            IsTriggered = false
        };

        _mockStorage.Setup(s => s.GetWillMessageAsync(clientId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(registration);

        _mockStorage.Setup(s => s.StoreWillMessageAsync(It.IsAny<WillMessageRegistration>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(WillMessageStorageResult.Success(clientId, WillMessageStorageOperation.Store));

        _mockRoutingEngine.Setup(r => r.RouteMessageAsync(It.IsAny<MqttPublishPacket>(), clientId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(MessageRoutingResult.Success("test/topic", 1, 0, 0, 0, 10));

        // Act
        var result = await _willMessageManager.TriggerWillMessageAsync(clientId, triggerCondition);

        // Assert
        Assert.True(result.IsSuccess, $"Expected success but got: {result.ErrorMessage}");
        Assert.Equal(clientId, result.ClientId);
        Assert.Equal(triggerCondition, result.TriggerCondition);
        Assert.True(result.WillMessageExisted, "Expected will message to exist");
        Assert.True(result.WillMessagePublished, $"Expected will message to be published but got: WillMessagePublished={result.WillMessagePublished}");
        Assert.Equal("test/topic", result.WillTopic);

        _mockRoutingEngine.Verify(r => r.RouteMessageAsync(It.IsAny<MqttPublishPacket>(), clientId, It.IsAny<CancellationToken>()), Times.Once);
        _mockStorage.Verify(s => s.StoreWillMessageAsync(It.IsAny<WillMessageRegistration>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task TriggerWillMessageAsync_NoWillMessage_ShouldReturnSuccessWithoutPublishing()
    {
        // Arrange
        var clientId = "test-client";
        var triggerCondition = WillMessageTriggerCondition.NetworkFailure;

        _mockStorage.Setup(s => s.GetWillMessageAsync(clientId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((WillMessageRegistration?)null);

        // Act
        var result = await _willMessageManager.TriggerWillMessageAsync(clientId, triggerCondition);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(clientId, result.ClientId);
        Assert.Equal(triggerCondition, result.TriggerCondition);
        Assert.False(result.WillMessageExisted);
        Assert.False(result.WillMessagePublished);
        Assert.Null(result.WillTopic);

        _mockRoutingEngine.Verify(r => r.RouteMessageAsync(It.IsAny<MqttPublishPacket>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task TriggerWillMessageAsync_AlreadyTriggered_ShouldReturnSuccessWithoutPublishing()
    {
        // Arrange
        var clientId = "test-client";
        var triggerCondition = WillMessageTriggerCondition.NetworkFailure;
        
        var registration = new WillMessageRegistration
        {
            ClientId = clientId,
            Topic = "test/topic",
            Payload = System.Text.Encoding.UTF8.GetBytes("offline"),
            QoSLevel = MqttQoSLevel.AtLeastOnce,
            Retain = true,
            IsTriggered = true,
            TriggeredAt = DateTime.UtcNow.AddMinutes(-5)
        };

        _mockStorage.Setup(s => s.GetWillMessageAsync(clientId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(registration);

        // Act
        var result = await _willMessageManager.TriggerWillMessageAsync(clientId, triggerCondition);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(clientId, result.ClientId);
        Assert.Equal(triggerCondition, result.TriggerCondition);
        Assert.True(result.WillMessageExisted);
        Assert.False(result.WillMessagePublished);
        Assert.Equal("test/topic", result.WillTopic);

        _mockRoutingEngine.Verify(r => r.RouteMessageAsync(It.IsAny<MqttPublishPacket>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task ClearWillMessageAsync_ExistingWillMessage_ShouldReturnSuccess()
    {
        // Arrange
        var clientId = "test-client";
        
        var registration = new WillMessageRegistration
        {
            ClientId = clientId,
            Topic = "test/topic",
            Payload = System.Text.Encoding.UTF8.GetBytes("offline"),
            QoSLevel = MqttQoSLevel.AtLeastOnce,
            Retain = true
        };

        _mockStorage.Setup(s => s.GetWillMessageAsync(clientId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(registration);

        _mockStorage.Setup(s => s.DeleteWillMessageAsync(clientId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(WillMessageStorageResult.Success(clientId, WillMessageStorageOperation.Delete));

        // Act
        var result = await _willMessageManager.ClearWillMessageAsync(clientId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(clientId, result.ClientId);
        Assert.True(result.WillMessageExisted);
        Assert.Null(result.ErrorMessage);

        _mockStorage.Verify(s => s.DeleteWillMessageAsync(clientId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ClearWillMessageAsync_NoWillMessage_ShouldReturnSuccessWithoutExisting()
    {
        // Arrange
        var clientId = "test-client";

        _mockStorage.Setup(s => s.GetWillMessageAsync(clientId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((WillMessageRegistration?)null);

        _mockStorage.Setup(s => s.DeleteWillMessageAsync(clientId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(WillMessageStorageResult.Success(clientId, WillMessageStorageOperation.Delete));

        // Act
        var result = await _willMessageManager.ClearWillMessageAsync(clientId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(clientId, result.ClientId);
        Assert.False(result.WillMessageExisted);
        Assert.Null(result.ErrorMessage);

        _mockStorage.Verify(s => s.DeleteWillMessageAsync(clientId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task HasWillMessageAsync_ExistingWillMessage_ShouldReturnTrue()
    {
        // Arrange
        var clientId = "test-client";

        _mockStorage.Setup(s => s.ExistsAsync(clientId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _willMessageManager.HasWillMessageAsync(clientId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task HasWillMessageAsync_NoWillMessage_ShouldReturnFalse()
    {
        // Arrange
        var clientId = "test-client";

        _mockStorage.Setup(s => s.ExistsAsync(clientId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        var result = await _willMessageManager.HasWillMessageAsync(clientId);

        // Assert
        Assert.False(result);
    }

    public void Dispose()
    {
        _willMessageManager?.Dispose();
    }
}
