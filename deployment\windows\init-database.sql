-- MQTT Broker SQLite 数据库初始化脚本
-- 适用于 Windows 原生部署的轻量级数据库方案

-- 启用外键约束
PRAGMA foreign_keys = ON;

-- 客户端连接表
CREATE TABLE IF NOT EXISTS Clients (
    Id TEXT PRIMARY KEY,
    Username TEXT,
    Password TEXT,
    IsConnected BOOLEAN DEFAULT 0,
    ConnectedAt DATETIME,
    DisconnectedAt DATETIME,
    LastActivity DATETIME,
    IpAddress TEXT,
    UserAgent TEXT,
    ProtocolVersion INTEGER DEFAULT 4,
    KeepAlive INTEGER DEFAULT 60,
    CleanSession BOOLEAN DEFAULT 1,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 客户端连接索引
CREATE INDEX IF NOT EXISTS idx_clients_username ON Clients(Username);
CREATE INDEX IF NOT EXISTS idx_clients_connected ON Clients(IsConnected);
CREATE INDEX IF NOT EXISTS idx_clients_last_activity ON Clients(LastActivity);

-- 主题订阅表
CREATE TABLE IF NOT EXISTS Subscriptions (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    ClientId TEXT NOT NULL,
    Topic TEXT NOT NULL,
    QoS INTEGER NOT NULL DEFAULT 0,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ClientId) REFERENCES Clients(Id) ON DELETE CASCADE,
    UNIQUE(ClientId, Topic)
);

-- 订阅索引
CREATE INDEX IF NOT EXISTS idx_subscriptions_client ON Subscriptions(ClientId);
CREATE INDEX IF NOT EXISTS idx_subscriptions_topic ON Subscriptions(Topic);

-- 保留消息表
CREATE TABLE IF NOT EXISTS RetainedMessages (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Topic TEXT UNIQUE NOT NULL,
    Payload BLOB,
    QoS INTEGER NOT NULL DEFAULT 0,
    PublishedBy TEXT,
    PublishedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    ExpiresAt DATETIME
);

-- 保留消息索引
CREATE INDEX IF NOT EXISTS idx_retained_topic ON RetainedMessages(Topic);
CREATE INDEX IF NOT EXISTS idx_retained_expires ON RetainedMessages(ExpiresAt);

-- 会话状态表
CREATE TABLE IF NOT EXISTS Sessions (
    ClientId TEXT PRIMARY KEY,
    CleanSession BOOLEAN DEFAULT 1,
    ExpiresAt DATETIME,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ClientId) REFERENCES Clients(Id) ON DELETE CASCADE
);

-- 会话索引
CREATE INDEX IF NOT EXISTS idx_sessions_expires ON Sessions(ExpiresAt);

-- 未确认消息表 (QoS 1 和 QoS 2)
CREATE TABLE IF NOT EXISTS PendingMessages (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    ClientId TEXT NOT NULL,
    MessageId INTEGER NOT NULL,
    Topic TEXT NOT NULL,
    Payload BLOB,
    QoS INTEGER NOT NULL,
    Direction TEXT NOT NULL, -- 'INBOUND' 或 'OUTBOUND'
    State TEXT NOT NULL DEFAULT 'PENDING', -- 'PENDING', 'PUBREC', 'PUBREL', 'ACKNOWLEDGED'
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    ExpiresAt DATETIME,
    FOREIGN KEY (ClientId) REFERENCES Clients(Id) ON DELETE CASCADE,
    UNIQUE(ClientId, MessageId, Direction)
);

-- 未确认消息索引
CREATE INDEX IF NOT EXISTS idx_pending_client ON PendingMessages(ClientId);
CREATE INDEX IF NOT EXISTS idx_pending_state ON PendingMessages(State);
CREATE INDEX IF NOT EXISTS idx_pending_expires ON PendingMessages(ExpiresAt);

-- 遗嘱消息表
CREATE TABLE IF NOT EXISTS WillMessages (
    ClientId TEXT PRIMARY KEY,
    Topic TEXT NOT NULL,
    Payload BLOB,
    QoS INTEGER NOT NULL DEFAULT 0,
    Retain BOOLEAN DEFAULT 0,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ClientId) REFERENCES Clients(Id) ON DELETE CASCADE
);

-- 用户认证表 (可选，用于基本认证)
CREATE TABLE IF NOT EXISTS Users (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Username TEXT UNIQUE NOT NULL,
    PasswordHash TEXT NOT NULL,
    Salt TEXT NOT NULL,
    IsActive BOOLEAN DEFAULT 1,
    Roles TEXT, -- JSON 格式存储角色信息
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    LastLoginAt DATETIME
);

-- 用户索引
CREATE INDEX IF NOT EXISTS idx_users_username ON Users(Username);
CREATE INDEX IF NOT EXISTS idx_users_active ON Users(IsActive);

-- 权限控制表 (可选，用于主题级别授权)
CREATE TABLE IF NOT EXISTS Permissions (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Username TEXT NOT NULL,
    Topic TEXT NOT NULL,
    Action TEXT NOT NULL, -- 'PUBLISH', 'SUBSCRIBE', 'BOTH'
    Allow BOOLEAN DEFAULT 1,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (Username) REFERENCES Users(Username) ON DELETE CASCADE
);

-- 权限索引
CREATE INDEX IF NOT EXISTS idx_permissions_user ON Permissions(Username);
CREATE INDEX IF NOT EXISTS idx_permissions_topic ON Permissions(Topic);

-- 统计信息表
CREATE TABLE IF NOT EXISTS Statistics (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    MetricName TEXT NOT NULL,
    MetricValue REAL NOT NULL,
    Timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    Tags TEXT -- JSON 格式存储标签信息
);

-- 统计索引
CREATE INDEX IF NOT EXISTS idx_statistics_name ON Statistics(MetricName);
CREATE INDEX IF NOT EXISTS idx_statistics_timestamp ON Statistics(Timestamp);

-- 系统配置表
CREATE TABLE IF NOT EXISTS SystemConfig (
    Key TEXT PRIMARY KEY,
    Value TEXT NOT NULL,
    Description TEXT,
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 插入默认配置
INSERT OR IGNORE INTO SystemConfig (Key, Value, Description) VALUES
('broker.version', '1.0.0', 'MQTT Broker 版本'),
('broker.start_time', datetime('now'), 'Broker 启动时间'),
('max_connections', '1000', '最大连接数'),
('message_retention_days', '7', '消息保留天数'),
('session_expiry_hours', '24', '会话过期时间（小时）');

-- 创建默认管理员用户 (密码: admin123)
INSERT OR IGNORE INTO Users (Username, PasswordHash, Salt, Roles) VALUES
('admin', 'a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3', 'salt123', '["admin"]');

-- 创建触发器：自动更新 UpdatedAt 字段
CREATE TRIGGER IF NOT EXISTS update_clients_timestamp 
    AFTER UPDATE ON Clients
BEGIN
    UPDATE Clients SET UpdatedAt = CURRENT_TIMESTAMP WHERE Id = NEW.Id;
END;

CREATE TRIGGER IF NOT EXISTS update_sessions_timestamp 
    AFTER UPDATE ON Sessions
BEGIN
    UPDATE Sessions SET UpdatedAt = CURRENT_TIMESTAMP WHERE ClientId = NEW.ClientId;
END;

CREATE TRIGGER IF NOT EXISTS update_pending_messages_timestamp 
    AFTER UPDATE ON PendingMessages
BEGIN
    UPDATE PendingMessages SET UpdatedAt = CURRENT_TIMESTAMP WHERE Id = NEW.Id;
END;

CREATE TRIGGER IF NOT EXISTS update_users_timestamp 
    AFTER UPDATE ON Users
BEGIN
    UPDATE Users SET UpdatedAt = CURRENT_TIMESTAMP WHERE Id = NEW.Id;
END;

-- 创建视图：活跃连接统计
CREATE VIEW IF NOT EXISTS ActiveConnectionsView AS
SELECT 
    COUNT(*) as TotalConnections,
    COUNT(CASE WHEN IsConnected = 1 THEN 1 END) as ActiveConnections,
    COUNT(CASE WHEN IsConnected = 0 THEN 1 END) as DisconnectedConnections,
    AVG(KeepAlive) as AverageKeepAlive
FROM Clients;

-- 创建视图：主题订阅统计
CREATE VIEW IF NOT EXISTS SubscriptionStatsView AS
SELECT 
    Topic,
    COUNT(*) as SubscriberCount,
    AVG(QoS) as AverageQoS
FROM Subscriptions
GROUP BY Topic
ORDER BY SubscriberCount DESC;

-- 创建视图：消息统计
CREATE VIEW IF NOT EXISTS MessageStatsView AS
SELECT 
    DATE(CreatedAt) as Date,
    COUNT(*) as MessageCount,
    AVG(LENGTH(Payload)) as AveragePayloadSize
FROM RetainedMessages
GROUP BY DATE(CreatedAt)
ORDER BY Date DESC;

-- 数据清理存储过程（通过定时任务调用）
-- 清理过期的会话
-- DELETE FROM Sessions WHERE ExpiresAt < datetime('now');

-- 清理过期的未确认消息
-- DELETE FROM PendingMessages WHERE ExpiresAt < datetime('now');

-- 清理过期的保留消息
-- DELETE FROM RetainedMessages WHERE ExpiresAt < datetime('now');

-- 清理旧的统计数据（保留30天）
-- DELETE FROM Statistics WHERE Timestamp < datetime('now', '-30 days');

-- 清理断开连接超过7天的客户端记录
-- DELETE FROM Clients WHERE IsConnected = 0 AND DisconnectedAt < datetime('now', '-7 days');

-- 数据库版本信息
PRAGMA user_version = 1;

-- 完成初始化
INSERT OR REPLACE INTO SystemConfig (Key, Value, Description) VALUES
('database.initialized', 'true', '数据库初始化完成'),
('database.version', '1.0', '数据库架构版本'),
('database.initialized_at', datetime('now'), '数据库初始化时间');
