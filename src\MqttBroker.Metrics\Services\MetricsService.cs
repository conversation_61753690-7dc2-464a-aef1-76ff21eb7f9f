using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MqttBroker.Metrics.Alerts;
using MqttBroker.Metrics.Collectors;
using MqttBroker.Metrics.Configuration;
using MqttBroker.Metrics.Export;
using MqttBroker.Metrics.Storage;

namespace MqttBroker.Metrics.Services;

/// <summary>
/// 性能监控服务
/// </summary>
public class MetricsService : BackgroundService
{
    private readonly IMetricsCollector _metricsCollector;
    private readonly IMetricsStorage _metricsStorage;
    private readonly IAlertManager _alertManager;
    private readonly IMetricsExportManager _exportManager;
    private readonly ILogger<MetricsService> _logger;
    private readonly MetricsOptions _options;

    private Timer? _exportTimer;
    private bool _disposed;

    /// <summary>
    /// 构造函数
    /// </summary>
    public MetricsService(
        IMetricsCollector metricsCollector,
        IMetricsStorage metricsStorage,
        IAlertManager alertManager,
        IMetricsExportManager exportManager,
        ILogger<MetricsService> logger,
        IOptions<MetricsOptions> options)
    {
        _metricsCollector = metricsCollector ?? throw new ArgumentNullException(nameof(metricsCollector));
        _metricsStorage = metricsStorage ?? throw new ArgumentNullException(nameof(metricsStorage));
        _alertManager = alertManager ?? throw new ArgumentNullException(nameof(alertManager));
        _exportManager = exportManager ?? throw new ArgumentNullException(nameof(exportManager));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
    }

    /// <summary>
    /// 启动服务
    /// </summary>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        if (!_options.EnableMetrics)
        {
            _logger.LogInformation("性能监控已禁用，服务将不会启动");
            return;
        }

        _logger.LogInformation("性能监控服务正在启动...");

        try
        {
            // 订阅指标收集事件
            _metricsCollector.MetricsCollected += OnMetricsCollected;

            // 启动指标收集器
            await _metricsCollector.StartAsync(stoppingToken);

            // 启动文件导出定时器（如果启用）
            if (_options.Export.EnableFileExport)
            {
                StartFileExportTimer();
            }

            _logger.LogInformation("性能监控服务已启动");

            // 等待取消信号
            await Task.Delay(Timeout.Infinite, stoppingToken);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("性能监控服务正在停止...");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "性能监控服务运行时发生错误");
            throw;
        }
        finally
        {
            await StopAsync(stoppingToken);
        }
    }

    /// <summary>
    /// 停止服务
    /// </summary>
    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("性能监控服务正在停止...");

        try
        {
            // 停止指标收集器
            if (_metricsCollector != null)
            {
                _metricsCollector.MetricsCollected -= OnMetricsCollected;
                await _metricsCollector.StopAsync(cancellationToken);
            }

            // 停止文件导出定时器
            _exportTimer?.Dispose();
            _exportTimer = null;

            _logger.LogInformation("性能监控服务已停止");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止性能监控服务时发生错误");
        }

        await base.StopAsync(cancellationToken);
    }

    /// <summary>
    /// 处理指标收集事件
    /// </summary>
    private async void OnMetricsCollected(object? sender, MetricsCollectedEventArgs e)
    {
        try
        {
            // 存储指标数据
            await _metricsStorage.StoreMetricsAsync(e.Metrics);

            // 评估告警规则
            await _alertManager.EvaluateMetricsAsync(e.Metrics);

            _logger.LogTrace("已处理指标收集事件，时间戳: {Timestamp}", e.Metrics.Timestamp);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理指标收集事件时发生错误");
        }
    }

    /// <summary>
    /// 启动文件导出定时器
    /// </summary>
    private void StartFileExportTimer()
    {
        var interval = TimeSpan.FromSeconds(_options.Export.ExportIntervalSeconds);
        
        _exportTimer = new Timer(
            ExportToFileCallback,
            null,
            interval,
            interval);

        _logger.LogInformation("文件导出定时器已启动，导出间隔: {Interval}秒", _options.Export.ExportIntervalSeconds);
    }

    /// <summary>
    /// 文件导出回调
    /// </summary>
    private async void ExportToFileCallback(object? state)
    {
        try
        {
            if (!_options.Export.EnableFileExport)
                return;

            // 获取最新指标
            var latestMetrics = await _metricsStorage.GetLatestMetricsAsync(1);
            var metrics = latestMetrics.FirstOrDefault();
            
            if (metrics == null)
            {
                _logger.LogDebug("没有可导出的指标数据");
                return;
            }

            // 导出到各种格式
            foreach (var format in _options.Export.FileExportFormats)
            {
                try
                {
                    var formatName = format.ToString().ToLowerInvariant();
                    var data = await _exportManager.ExportCurrentMetricsAsync(metrics, formatName);
                    
                    var fileName = $"metrics_{DateTime.UtcNow:yyyyMMdd_HHmmss}.{formatName}";
                    var filePath = Path.Combine(_options.Export.FileExportPath, fileName);
                    
                    await _exportManager.ExportToFileAsync(data, filePath);
                    
                    _logger.LogDebug("已导出指标到文件: {FilePath}", filePath);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "导出指标到 {Format} 格式文件时发生错误", format);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "文件导出回调执行时发生错误");
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public override void Dispose()
    {
        if (!_disposed)
        {
            _exportTimer?.Dispose();
            _disposed = true;
        }

        base.Dispose();
    }
}

/// <summary>
/// 性能监控管理器
/// </summary>
public interface IMetricsManager
{
    /// <summary>
    /// 获取当前性能指标
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>性能指标</returns>
    Task<Models.PerformanceMetrics> GetCurrentMetricsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取历史性能指标
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>历史性能指标</returns>
    Task<IEnumerable<Models.PerformanceMetrics>> GetHistoricalMetricsAsync(DateTime startTime, DateTime endTime, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取聚合性能指标
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="aggregationInterval">聚合间隔（秒）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>聚合性能指标</returns>
    Task<IEnumerable<Storage.AggregatedMetrics>> GetAggregatedMetricsAsync(DateTime startTime, DateTime endTime, int aggregationInterval, CancellationToken cancellationToken = default);

    /// <summary>
    /// 导出性能指标
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="format">导出格式</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>导出的数据</returns>
    Task<string> ExportMetricsAsync(DateTime startTime, DateTime endTime, string format, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取存储统计信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>存储统计信息</returns>
    Task<Storage.MetricsStorageStatistics> GetStorageStatisticsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 性能监控管理器实现
/// </summary>
public class MetricsManager : IMetricsManager
{
    private readonly IMetricsCollector _metricsCollector;
    private readonly IMetricsStorage _metricsStorage;
    private readonly IMetricsExportManager _exportManager;
    private readonly ILogger<MetricsManager> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    public MetricsManager(
        IMetricsCollector metricsCollector,
        IMetricsStorage metricsStorage,
        IMetricsExportManager exportManager,
        ILogger<MetricsManager> logger)
    {
        _metricsCollector = metricsCollector ?? throw new ArgumentNullException(nameof(metricsCollector));
        _metricsStorage = metricsStorage ?? throw new ArgumentNullException(nameof(metricsStorage));
        _exportManager = exportManager ?? throw new ArgumentNullException(nameof(exportManager));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 获取当前性能指标
    /// </summary>
    public async Task<Models.PerformanceMetrics> GetCurrentMetricsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _metricsCollector.CollectMetricsAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取当前性能指标时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 获取历史性能指标
    /// </summary>
    public async Task<IEnumerable<Models.PerformanceMetrics>> GetHistoricalMetricsAsync(DateTime startTime, DateTime endTime, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _metricsStorage.GetMetricsAsync(startTime, endTime, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取历史性能指标时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 获取聚合性能指标
    /// </summary>
    public async Task<IEnumerable<Storage.AggregatedMetrics>> GetAggregatedMetricsAsync(DateTime startTime, DateTime endTime, int aggregationInterval, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _metricsStorage.GetAggregatedMetricsAsync(startTime, endTime, aggregationInterval, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取聚合性能指标时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 导出性能指标
    /// </summary>
    public async Task<string> ExportMetricsAsync(DateTime startTime, DateTime endTime, string format, CancellationToken cancellationToken = default)
    {
        try
        {
            var metrics = await _metricsStorage.GetMetricsAsync(startTime, endTime, cancellationToken);
            return await _exportManager.ExportHistoricalMetricsAsync(metrics, format, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出性能指标时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 获取存储统计信息
    /// </summary>
    public async Task<Storage.MetricsStorageStatistics> GetStorageStatisticsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _metricsStorage.GetStorageStatisticsAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取存储统计信息时发生错误");
            throw;
        }
    }
}
