using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MqttBroker.Core;
using MqttBroker.Core.Client;
using MqttBroker.Core.Network;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using System.Net;
using Xunit;
using Xunit.Abstractions;

namespace MqttBroker.Tests.Integration.Client;

/// <summary>
/// MQTT 客户端连接管理集成测试
/// </summary>
public class MqttClientManagementIntegrationTests : IDisposable
{
    private readonly ITestOutputHelper _output;
    private readonly ServiceProvider _serviceProvider;
    private readonly IMqttClientManager _clientManager;
    private readonly IMqttConnectionPool _connectionPool;

    public MqttClientManagementIntegrationTests(ITestOutputHelper output)
    {
        _output = output;

        // 配置服务容器
        var services = new ServiceCollection();
        
        // 添加日志
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Debug);
        });

        // 添加 MQTT Broker 核心服务
        services.AddMqttBrokerCore();

        // 配置客户端管理选项
        services.Configure<MqttClientManagerOptions>(options =>
        {
            options.MaxConnections = 100;
            options.AllowClientIdReuse = true;
        });

        services.Configure<MqttClientAuthenticationOptions>(options =>
        {
            options.RequireAuthentication = true;
            options.StaticUsers["test_user"] = "test_password";
            options.StaticUsers["admin"] = "admin123";
        });

        services.Configure<MqttConnectionPoolOptions>(options =>
        {
            options.MaxPoolSize = 50;
            options.ConnectionTimeoutMs = 60000;
        });

        _serviceProvider = services.BuildServiceProvider();
        _clientManager = _serviceProvider.GetRequiredService<IMqttClientManager>();
        _connectionPool = _serviceProvider.GetRequiredService<IMqttConnectionPool>();
    }

    [Fact]
    public async Task ClientManager_HandleValidConnect_ShouldSucceed()
    {
        // Arrange
        var connection = CreateMockConnection("test_client_001");
        var connectPacket = new MqttConnectPacket
        {
            ClientId = "test_client_001",
            CleanSession = true,
            KeepAlive = 60,
            Username = "test_user",
            Password = "test_password",
            ProtocolVersion = MqttProtocolVersion.Version311
        };

        // Act
        var result = await _clientManager.HandleConnectAsync(connection, connectPacket);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(MqttConnectReturnCode.ConnectionAccepted, result.ReturnCode);
        Assert.Equal(1, _clientManager.ConnectedClientCount);

        var client = _clientManager.GetClient("test_client_001");
        Assert.NotNull(client);
        Assert.Equal("test_client_001", client.ClientId);
        Assert.True(client.IsAuthenticated);

        _output.WriteLine($"客户端连接成功: {client.ClientId}");
    }

    [Fact]
    public async Task ClientManager_HandleInvalidCredentials_ShouldFail()
    {
        // Arrange
        var connection = CreateMockConnection("test_client_002");
        var connectPacket = new MqttConnectPacket
        {
            ClientId = "test_client_002",
            CleanSession = true,
            KeepAlive = 60,
            Username = "invalid_user",
            Password = "invalid_password",
            ProtocolVersion = MqttProtocolVersion.Version311
        };

        // Act
        var result = await _clientManager.HandleConnectAsync(connection, connectPacket);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(MqttConnectReturnCode.BadUsernameOrPassword, result.ReturnCode);
        Assert.Equal(0, _clientManager.ConnectedClientCount);

        var client = _clientManager.GetClient("test_client_002");
        Assert.Null(client);

        _output.WriteLine($"认证失败，连接被拒绝: {result.ErrorMessage}");
    }

    [Fact]
    public async Task ClientManager_HandleMultipleClients_ShouldManageCorrectly()
    {
        // Arrange
        var clients = new[]
        {
            ("client_001", "test_user", "test_password"),
            ("client_002", "admin", "admin123"),
            ("client_003", "test_user", "test_password")
        };

        // Act
        var results = new List<MqttConnectResult>();
        foreach (var (clientId, username, password) in clients)
        {
            var connection = CreateMockConnection(clientId);
            var connectPacket = new MqttConnectPacket
            {
                ClientId = clientId,
                CleanSession = true,
                KeepAlive = 60,
                Username = username,
                Password = password,
                ProtocolVersion = MqttProtocolVersion.Version311
            };

            var result = await _clientManager.HandleConnectAsync(connection, connectPacket);
            results.Add(result);
        }

        // Assert
        Assert.All(results, result => Assert.True(result.IsSuccess));
        Assert.Equal(3, _clientManager.ConnectedClientCount);

        var allClients = _clientManager.GetAllClients().ToList();
        Assert.Equal(3, allClients.Count);

        foreach (var (clientId, _, _) in clients)
        {
            Assert.True(_clientManager.IsClientConnected(clientId));
            var client = _clientManager.GetClient(clientId);
            Assert.NotNull(client);
            Assert.Equal(clientId, client.ClientId);
        }

        _output.WriteLine($"成功管理 {_clientManager.ConnectedClientCount} 个客户端连接");
    }

    [Fact]
    public async Task ClientManager_HandleDisconnect_ShouldRemoveClient()
    {
        // Arrange
        var connection = CreateMockConnection("test_client_disconnect");
        var connectPacket = new MqttConnectPacket
        {
            ClientId = "test_client_disconnect",
            CleanSession = true,
            KeepAlive = 60,
            Username = "test_user",
            Password = "test_password",
            ProtocolVersion = MqttProtocolVersion.Version311
        };

        await _clientManager.HandleConnectAsync(connection, connectPacket);
        Assert.Equal(1, _clientManager.ConnectedClientCount);

        // Act
        await _clientManager.HandleDisconnectAsync("test_client_disconnect", DisconnectionReason.ClientDisconnected);

        // Assert
        Assert.Equal(0, _clientManager.ConnectedClientCount);
        Assert.False(_clientManager.IsClientConnected("test_client_disconnect"));
        Assert.Null(_clientManager.GetClient("test_client_disconnect"));

        _output.WriteLine("客户端断开连接处理成功");
    }

    [Fact]
    public void ClientManager_GetStatistics_ShouldReturnCorrectData()
    {
        // Act
        var stats = _clientManager.GetStatistics();

        // Assert
        Assert.NotNull(stats);
        Assert.Equal(0, stats.ConnectedClients);
        Assert.Equal(100, stats.MaxConnections); // 从配置中设置的值
        Assert.Equal(0, stats.TotalConnections);
        Assert.Equal(0, stats.ConnectionUtilization);

        _output.WriteLine($"统计信息: 连接数={stats.ConnectedClients}/{stats.MaxConnections}, 使用率={stats.ConnectionUtilization:F2}%");
    }

    [Fact]
    public void ConnectionPool_GetStatistics_ShouldReturnCorrectData()
    {
        // Act
        var stats = _connectionPool.GetStatistics();

        // Assert
        Assert.NotNull(stats);
        Assert.Equal(0, stats.CurrentPoolSize);
        Assert.Equal(50, stats.MaxPoolSize); // 从配置中设置的值
        Assert.Equal(0, stats.ActiveConnections);
        Assert.Equal(0, stats.PoolUtilization);

        _output.WriteLine($"连接池统计: 池大小={stats.CurrentPoolSize}/{stats.MaxPoolSize}, 使用率={stats.PoolUtilization:F2}%");
    }

    /// <summary>
    /// 创建模拟连接
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    /// <returns>模拟连接</returns>
    private IClientConnection CreateMockConnection(string clientId)
    {
        return new MockClientConnection
        {
            Id = $"conn_{clientId}",
            ClientId = null, // 将在连接过程中设置
            RemoteEndPoint = new IPEndPoint(IPAddress.Loopback, 12345),
            LocalEndPoint = new IPEndPoint(IPAddress.Loopback, 1883),
            State = ConnectionState.Connected,
            ConnectedAt = DateTime.UtcNow,
            LastActivity = DateTime.UtcNow,
            Properties = new Dictionary<string, object>()
        };
    }

    public void Dispose()
    {
        _serviceProvider?.Dispose();
    }
}

/// <summary>
/// 模拟客户端连接实现（用于集成测试）
/// </summary>
internal class MockClientConnection : IClientConnection
{
    public string Id { get; set; } = string.Empty;
    public string? ClientId { get; set; }
    public EndPoint RemoteEndPoint { get; set; } = new IPEndPoint(IPAddress.Loopback, 0);
    public EndPoint LocalEndPoint { get; set; } = new IPEndPoint(IPAddress.Loopback, 0);
    public ConnectionState State { get; set; }
    public DateTime ConnectedAt { get; set; }
    public DateTime LastActivity { get; set; }
    public MqttProtocolVersion? ProtocolVersion { get; set; }
    public bool IsAuthenticated { get; set; }
    public int KeepAliveInterval { get; set; }
    public IDictionary<string, object> Properties { get; set; } = new Dictionary<string, object>();

    public event EventHandler<PacketReceivedEventArgs>? PacketReceived;
    public event EventHandler<ConnectionClosedEventArgs>? ConnectionClosed;

    public Task SendPacketAsync(IMqttPacket packet, CancellationToken cancellationToken = default)
    {
        // 模拟发送数据包
        return Task.CompletedTask;
    }

    public Task SendDataAsync(ReadOnlyMemory<byte> data, CancellationToken cancellationToken = default)
    {
        // 模拟发送原始数据
        return Task.CompletedTask;
    }

    public Task CloseAsync(DisconnectionReason reason = DisconnectionReason.ServerDisconnected, CancellationToken cancellationToken = default)
    {
        State = ConnectionState.Disconnected;
        ConnectionClosed?.Invoke(this, new ConnectionClosedEventArgs(reason));
        return Task.CompletedTask;
    }

    public void UpdateLastActivity()
    {
        LastActivity = DateTime.UtcNow;
    }

    public ClientConnectionStatistics GetStatistics()
    {
        return new ClientConnectionStatistics
        {
            ConnectionId = Id,
            ClientId = ClientId,
            RemoteEndPoint = RemoteEndPoint.ToString(),
            ConnectedAt = ConnectedAt,
            LastActivity = LastActivity,
            ProtocolVersion = ProtocolVersion,
            IsAuthenticated = IsAuthenticated,
            KeepAliveInterval = KeepAliveInterval
        };
    }

    public void Dispose()
    {
        // 清理资源
    }
}
