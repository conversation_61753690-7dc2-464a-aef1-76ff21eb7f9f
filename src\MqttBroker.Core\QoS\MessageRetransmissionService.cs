using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Network;
using System.Collections.Concurrent;

namespace MqttBroker.Core.QoS;

/// <summary>
/// 消息重传服务实现，负责管理消息的重传逻辑
/// </summary>
public class MessageRetransmissionService : IMessageRetransmissionService, IDisposable
{
    private readonly ILogger<MessageRetransmissionService> _logger;
    private readonly MessageRetransmissionOptions _options;
    private readonly IMessageAcknowledgmentService _acknowledgmentService;

    private readonly Timer? _retransmissionTimer;
    private Func<string, IClientConnection?>? _connectionProvider;

    // 统计信息
    private long _totalRetransmissions;
    private long _qoS1Retransmissions;
    private long _qoS2Retransmissions;
    private long _successfulRetransmissions;
    private long _failedRetransmissions;
    private long _timeoutRetransmissions;
    private long _manualRetransmissions;

    private bool _isStarted;
    private bool _disposed;

    /// <summary>
    /// 初始化消息重传服务
    /// </summary>
    public MessageRetransmissionService(
        ILogger<MessageRetransmissionService> logger,
        IOptions<MessageRetransmissionOptions> options,
        IMessageAcknowledgmentService acknowledgmentService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _acknowledgmentService = acknowledgmentService ?? throw new ArgumentNullException(nameof(acknowledgmentService));

        if (_options.EnableRetransmission)
        {
            _retransmissionTimer = new Timer(CheckAndRetransmitMessagesCallback, null,
                Timeout.InfiniteTimeSpan, Timeout.InfiniteTimeSpan);
        }

        _logger.LogInformation("Message Retransmission Service initialized with options: Enabled={Enabled}, TimeoutMs={Timeout}, MaxAttempts={MaxAttempts}",
            _options.EnableRetransmission, _options.RetransmissionTimeoutMs, _options.MaxRetransmissionAttempts);
    }

    /// <summary>
    /// 启动重传服务
    /// </summary>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (_isStarted || !_options.EnableRetransmission)
        {
            _logger.LogInformation("Retransmission service is already started or disabled");
            return;
        }

        try
        {
            _logger.LogInformation("Starting Message Retransmission Service...");

            // 启动重传检查定时器
            _retransmissionTimer?.Change(
                TimeSpan.FromMilliseconds(_options.RetransmissionCheckIntervalMs),
                TimeSpan.FromMilliseconds(_options.RetransmissionCheckIntervalMs));

            _isStarted = true;
            _logger.LogInformation("Message Retransmission Service started successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start Message Retransmission Service");
            throw;
        }
    }

    /// <summary>
    /// 停止重传服务
    /// </summary>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (!_isStarted)
        {
            _logger.LogInformation("Retransmission service is not started");
            return;
        }

        try
        {
            _logger.LogInformation("Stopping Message Retransmission Service...");

            // 停止重传检查定时器
            _retransmissionTimer?.Change(Timeout.InfiniteTimeSpan, Timeout.InfiniteTimeSpan);

            _isStarted = false;
            _logger.LogInformation("Message Retransmission Service stopped successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to stop Message Retransmission Service");
            throw;
        }
    }

    /// <summary>
    /// 检查并执行消息重传
    /// </summary>
    public async Task<int> CheckAndRetransmitMessagesAsync(CancellationToken cancellationToken = default)
    {
        if (!_options.EnableRetransmission)
        {
            return 0;
        }

        try
        {
            var timeout = TimeSpan.FromMilliseconds(_options.RetransmissionTimeoutMs);
            var timedOutMessages = await _acknowledgmentService.GetTimedOutMessagesAsync(timeout);

            var retransmittedCount = 0;
            foreach (var message in timedOutMessages)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                var result = await RetransmitMessageAsync(message.ClientId, message.MessageId, cancellationToken);
                if (result.IsSuccess)
                {
                    retransmittedCount++;
                }
            }

            if (retransmittedCount > 0)
            {
                _logger.LogInformation("Retransmitted {Count} timed out messages", retransmittedCount);
            }

            return retransmittedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during message retransmission check");
            return 0;
        }
    }

    /// <summary>
    /// 重传特定客户端的超时消息
    /// </summary>
    public async Task<int> RetransmitClientMessagesAsync(string clientId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(clientId) || !_options.EnableRetransmission)
            return 0;

        try
        {
            var clientMessages = await _acknowledgmentService.GetClientPendingMessagesAsync(clientId);
            var timeout = TimeSpan.FromMilliseconds(_options.RetransmissionTimeoutMs);
            var timedOutMessages = clientMessages.Where(m => m.IsTimedOut(timeout)).ToList();

            var retransmittedCount = 0;
            foreach (var message in timedOutMessages)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                var result = await RetransmitMessageAsync(message.ClientId, message.MessageId, cancellationToken);
                if (result.IsSuccess)
                {
                    retransmittedCount++;
                }
            }

            if (retransmittedCount > 0)
            {
                _logger.LogInformation("Retransmitted {Count} timed out messages for client {ClientId}", retransmittedCount, clientId);
            }

            return retransmittedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retransmitting messages for client {ClientId}", clientId);
            return 0;
        }
    }

    /// <summary>
    /// 重传特定消息
    /// </summary>
    public async Task<RetransmissionResult> RetransmitMessageAsync(string clientId, ushort messageId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(clientId) || !_options.EnableRetransmission)
        {
            return RetransmissionResult.Failure(clientId, messageId, "Retransmission is disabled", RetransmissionReason.Manual);
        }

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            // 获取待确认消息
            var pendingMessage = await _acknowledgmentService.GetPendingMessageAsync(clientId, messageId);
            if (pendingMessage == null)
            {
                return RetransmissionResult.Failure(clientId, messageId, "Message not found in pending messages", RetransmissionReason.Timeout);
            }

            // 检查重传次数限制
            if (pendingMessage.RetransmissionCount >= _options.MaxRetransmissionAttempts)
            {
                _logger.LogWarning("Message {MessageId} for client {ClientId} has exceeded maximum retransmission attempts: {Attempts}",
                    messageId, clientId, _options.MaxRetransmissionAttempts);

                // 移除超过重传次数的消息
                await _acknowledgmentService.RemovePendingMessageAsync(clientId, messageId);
                
                return RetransmissionResult.Failure(clientId, messageId, 
                    $"Exceeded maximum retransmission attempts: {_options.MaxRetransmissionAttempts}", 
                    RetransmissionReason.Timeout);
            }

            // 获取客户端连接
            var connection = _connectionProvider?.Invoke(clientId);
            if (connection == null)
            {
                return RetransmissionResult.Failure(clientId, messageId, "Client connection not found", RetransmissionReason.Timeout);
            }

            // 重传消息
            await connection.SendPacketAsync(pendingMessage.PublishPacket, cancellationToken);

            // 更新重传信息
            pendingMessage.RetransmissionCount++;
            pendingMessage.LastRetransmissionAt = DateTime.UtcNow;

            stopwatch.Stop();

            // 更新统计信息
            Interlocked.Increment(ref _totalRetransmissions);
            Interlocked.Increment(ref _successfulRetransmissions);
            Interlocked.Increment(ref _timeoutRetransmissions);

            if (pendingMessage.QoSLevel == MqttQoSLevel.AtLeastOnce)
                Interlocked.Increment(ref _qoS1Retransmissions);
            else if (pendingMessage.QoSLevel == MqttQoSLevel.ExactlyOnce)
                Interlocked.Increment(ref _qoS2Retransmissions);

            _logger.LogTrace("Retransmitted message {MessageId} for client {ClientId}, Attempt: {Attempt}, QoS: {QoS}",
                messageId, clientId, pendingMessage.RetransmissionCount, pendingMessage.QoSLevel);

            return RetransmissionResult.Success(clientId, messageId, pendingMessage.RetransmissionCount, 
                pendingMessage.QoSLevel, RetransmissionReason.Timeout);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            Interlocked.Increment(ref _totalRetransmissions);
            Interlocked.Increment(ref _failedRetransmissions);

            _logger.LogError(ex, "Error retransmitting message {MessageId} for client {ClientId}", messageId, clientId);
            return RetransmissionResult.Failure(clientId, messageId, $"Retransmission failed: {ex.Message}", RetransmissionReason.Timeout);
        }
    }

    /// <summary>
    /// 获取重传统计信息
    /// </summary>
    public async Task<RetransmissionStatistics> GetStatisticsAsync()
    {
        try
        {
            var statistics = new RetransmissionStatistics
            {
                TotalRetransmissions = Interlocked.Read(ref _totalRetransmissions),
                QoS1Retransmissions = Interlocked.Read(ref _qoS1Retransmissions),
                QoS2Retransmissions = Interlocked.Read(ref _qoS2Retransmissions),
                SuccessfulRetransmissions = Interlocked.Read(ref _successfulRetransmissions),
                FailedRetransmissions = Interlocked.Read(ref _failedRetransmissions),
                TimeoutRetransmissions = Interlocked.Read(ref _timeoutRetransmissions),
                ManualRetransmissions = Interlocked.Read(ref _manualRetransmissions),
                LastRetransmissionCheckTime = DateTime.UtcNow
            };

            // 计算当前待重传消息数量
            var timeout = TimeSpan.FromMilliseconds(_options.RetransmissionTimeoutMs);
            var timedOutMessages = await _acknowledgmentService.GetTimedOutMessagesAsync(timeout);
            statistics.CurrentPendingRetransmissions = timedOutMessages.Count;

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting retransmission statistics");
            return new RetransmissionStatistics();
        }
    }

    /// <summary>
    /// 清理超过最大重传次数的消息
    /// </summary>
    public async Task<int> CleanupFailedMessagesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var timeout = TimeSpan.FromMilliseconds(_options.RetransmissionTimeoutMs);
            var timedOutMessages = await _acknowledgmentService.GetTimedOutMessagesAsync(timeout);

            var cleanedCount = 0;
            foreach (var message in timedOutMessages)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                if (message.RetransmissionCount >= _options.MaxRetransmissionAttempts)
                {
                    await _acknowledgmentService.RemovePendingMessageAsync(message.ClientId, message.MessageId);
                    cleanedCount++;

                    _logger.LogWarning("Cleaned up failed message {MessageId} for client {ClientId} after {Attempts} retransmission attempts",
                        message.MessageId, message.ClientId, message.RetransmissionCount);
                }
            }

            if (cleanedCount > 0)
            {
                _logger.LogInformation("Cleaned up {Count} failed messages that exceeded maximum retransmission attempts", cleanedCount);
            }

            return cleanedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up failed messages");
            return 0;
        }
    }

    /// <summary>
    /// 设置客户端连接提供者
    /// </summary>
    public void SetConnectionProvider(Func<string, IClientConnection?> connectionProvider)
    {
        _connectionProvider = connectionProvider ?? throw new ArgumentNullException(nameof(connectionProvider));
        _logger.LogTrace("Connection provider set for retransmission service");
    }

    /// <summary>
    /// 重传检查定时器回调
    /// </summary>
    private async void CheckAndRetransmitMessagesCallback(object? state)
    {
        try
        {
            await CheckAndRetransmitMessagesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in retransmission timer callback");
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            _retransmissionTimer?.Dispose();
            
            _disposed = true;
            _logger.LogInformation("Message Retransmission Service disposed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing Message Retransmission Service");
        }
    }
}
