using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MqttBroker.Core.Session;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Topic;
using MqttBroker.Core.QoS;

namespace SessionPersistenceDemo;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("=== MQTT Broker 会话持久化演示 ===\n");

        // 创建服务容器
        var services = new ServiceCollection();
        
        // 添加日志服务
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Information);
        });

        // 添加会话持久化服务
        services.AddSessionPersistence(options =>
        {
            options.SessionExpirationHours = 1; // 1小时过期
            options.EnableAutomaticCleanup = true;
            options.CleanupIntervalMinutes = 5; // 5分钟清理一次
        });

        var serviceProvider = services.BuildServiceProvider();
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
        var sessionPersistence = serviceProvider.GetRequiredService<ISessionPersistence>();

        try
        {
            await RunSessionPersistenceDemo(sessionPersistence, logger);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "演示过程中发生错误");
        }

        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }

    static async Task RunSessionPersistenceDemo(ISessionPersistence sessionPersistence, ILogger logger)
    {
        logger.LogInformation("开始会话持久化演示");

        // 1. 创建会话
        logger.LogInformation("\n--- 1. 创建会话 ---");
        var session = new MqttSession
        {
            ClientId = "demo-client-001",
            IsCleanSession = false,
            ProtocolVersion = MqttProtocolVersion.Version311,
            KeepAliveInterval = 60,
            Username = "demo-user",
            WillMessage = new MqttWillMessage
            {
                Topic = "clients/demo-client-001/status",
                Payload = System.Text.Encoding.UTF8.GetBytes("offline"),
                QoSLevel = MqttQoSLevel.AtLeastOnce,
                Retain = true
            }
        };

        var createResult = await sessionPersistence.CreateOrUpdateSessionAsync(session);
        logger.LogInformation("创建会话结果: {IsSuccess}, 操作类型: {OperationType}", 
            createResult.IsSuccess, createResult.OperationType);

        // 2. 获取会话
        logger.LogInformation("\n--- 2. 获取会话 ---");
        var retrievedSession = await sessionPersistence.GetSessionAsync("demo-client-001");
        if (retrievedSession != null)
        {
            logger.LogInformation("获取到会话: ClientId={ClientId}, Username={Username}, KeepAlive={KeepAlive}秒",
                retrievedSession.ClientId, retrievedSession.Username, retrievedSession.KeepAliveInterval);
        }

        // 3. 保存订阅信息
        logger.LogInformation("\n--- 3. 保存订阅信息 ---");
        var subscriptions = new List<ClientSubscription>
        {
            new ClientSubscription
            {
                ClientId = "demo-client-001",
                TopicFilter = "sensors/+/temperature",
                QoSLevel = MqttQoSLevel.AtLeastOnce,
                SubscribedAt = DateTime.UtcNow,
                Options = new MqttSubscriptionOptions { NoLocal = false }
            },
            new ClientSubscription
            {
                ClientId = "demo-client-001",
                TopicFilter = "devices/+/status",
                QoSLevel = MqttQoSLevel.ExactlyOnce,
                SubscribedAt = DateTime.UtcNow,
                Options = new MqttSubscriptionOptions { RetainAsPublished = true }
            }
        };

        var saveSubResult = await sessionPersistence.SaveSubscriptionsAsync("demo-client-001", subscriptions);
        logger.LogInformation("保存订阅结果: {IsSuccess}, 影响记录数: {AffectedRecords}", 
            saveSubResult.IsSuccess, saveSubResult.AffectedRecords);

        // 4. 获取订阅信息
        logger.LogInformation("\n--- 4. 获取订阅信息 ---");
        var retrievedSubscriptions = await sessionPersistence.GetSubscriptionsAsync("demo-client-001");
        logger.LogInformation("获取到 {Count} 个订阅:", retrievedSubscriptions.Count());
        foreach (var sub in retrievedSubscriptions)
        {
            logger.LogInformation("  - 主题: {TopicFilter}, QoS: {QoS}, 时间: {Time:yyyy-MM-dd HH:mm:ss}",
                sub.TopicFilter, sub.QoSLevel, sub.SubscribedAt);
        }

        // 5. 保存未确认消息
        logger.LogInformation("\n--- 5. 保存未确认消息 ---");
        var publishPacket = MqttPublishPacket.Create(
            "sensors/room1/temperature",
            System.Text.Encoding.UTF8.GetBytes("23.5"),
            MqttQoSLevel.AtLeastOnce,
            false,
            1001);

        var pendingMessage = new PendingMessage
        {
            ClientId = "demo-client-001",
            MessageId = 1001,
            PublishPacket = publishPacket,
            QoSLevel = MqttQoSLevel.AtLeastOnce,
            CreatedAt = DateTime.UtcNow,
            RetransmissionCount = 0
        };

        var saveMsgResult = await sessionPersistence.SavePendingMessageAsync("demo-client-001", pendingMessage);
        logger.LogInformation("保存未确认消息结果: {IsSuccess}", saveMsgResult.IsSuccess);

        // 6. 获取未确认消息
        logger.LogInformation("\n--- 6. 获取未确认消息 ---");
        var retrievedMessages = await sessionPersistence.GetPendingMessagesAsync("demo-client-001");
        logger.LogInformation("获取到 {Count} 条未确认消息:", retrievedMessages.Count());
        foreach (var msg in retrievedMessages)
        {
            logger.LogInformation("  - 消息ID: {MessageId}, 主题: {Topic}, QoS: {QoS}, 年龄: {Age}ms",
                msg.MessageId, msg.PublishPacket.Topic, msg.QoSLevel, msg.Age.TotalMilliseconds);
        }

        // 7. 获取统计信息
        logger.LogInformation("\n--- 7. 获取统计信息 ---");
        var statistics = await sessionPersistence.GetStatisticsAsync();
        logger.LogInformation("会话统计信息:");
        logger.LogInformation("  总会话数: {TotalSessions}", statistics.TotalSessions);
        logger.LogInformation("  活跃会话数: {ActiveSessions}", statistics.ActiveSessions);
        logger.LogInformation("  持久会话数: {PersistentSessions}", statistics.PersistentSessions);
        logger.LogInformation("  总订阅数: {TotalSubscriptions}", statistics.TotalSubscriptions);
        logger.LogInformation("  总未确认消息数: {TotalPendingMessages}", statistics.TotalPendingMessages);
        logger.LogInformation("  在线会话比例: {OnlinePercentage:F1}%", statistics.OnlineSessionPercentage);

        // 8. 创建过期会话并测试清理
        logger.LogInformation("\n--- 8. 测试会话清理 ---");
        var expiredSession = new MqttSession
        {
            ClientId = "expired-client",
            IsCleanSession = false,
            CreatedAt = DateTime.UtcNow.AddDays(-1),
            ExpiresAt = DateTime.UtcNow.AddMinutes(-10), // 10分钟前过期
            State = SessionState.Offline
        };

        await sessionPersistence.CreateOrUpdateSessionAsync(expiredSession);
        logger.LogInformation("创建了一个过期会话: {ClientId}", expiredSession.ClientId);

        var cleanupResult = await sessionPersistence.CleanupExpiredSessionsAsync(DateTime.UtcNow);
        logger.LogInformation("清理结果: 成功={IsSuccess}, 清理会话数={SessionCount}, 耗时={Duration}ms",
            cleanupResult.IsSuccess, cleanupResult.CleanedSessionsCount, cleanupResult.Duration.TotalMilliseconds);

        // 9. 更新会话活动时间
        logger.LogInformation("\n--- 9. 更新会话活动时间 ---");
        var updateResult = await sessionPersistence.UpdateSessionActivityAsync("demo-client-001", DateTime.UtcNow);
        logger.LogInformation("更新活动时间结果: {IsSuccess}", updateResult.IsSuccess);

        // 10. 删除未确认消息
        logger.LogInformation("\n--- 10. 删除未确认消息 ---");
        var deleteResult = await sessionPersistence.DeletePendingMessageAsync("demo-client-001", 1001);
        logger.LogInformation("删除未确认消息结果: {IsSuccess}, 影响记录数: {AffectedRecords}", 
            deleteResult.IsSuccess, deleteResult.AffectedRecords);

        logger.LogInformation("\n=== 会话持久化演示完成 ===");
    }
}
