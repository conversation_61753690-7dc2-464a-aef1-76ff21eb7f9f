using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Network;

namespace MqttBroker.Core.Client.Examples;

/// <summary>
/// MQTT 客户端连接管理示例
/// </summary>
public class MqttClientManagementExample
{
    private readonly ILogger<MqttClientManagementExample> _logger;
    private readonly IMqttClientManager _clientManager;
    private readonly IMqttConnectionPool _connectionPool;

    /// <summary>
    /// 初始化示例
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="clientManager">客户端管理器</param>
    /// <param name="connectionPool">连接池</param>
    public MqttClientManagementExample(
        ILogger<MqttClientManagementExample> logger,
        IMqttClientManager clientManager,
        IMqttConnectionPool connectionPool)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _clientManager = clientManager ?? throw new ArgumentNullException(nameof(clientManager));
        _connectionPool = connectionPool ?? throw new ArgumentNullException(nameof(connectionPool));
    }

    /// <summary>
    /// 运行示例
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>运行任务</returns>
    public async Task RunAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("=== MQTT 客户端连接管理示例 ===");

        // 订阅客户端管理器事件
        SubscribeToClientManagerEvents();

        // 演示客户端连接处理
        await DemonstrateClientConnectionAsync(cancellationToken);

        // 演示客户端管理功能
        await DemonstrateClientManagementAsync(cancellationToken);

        // 演示连接池功能
        await DemonstrateConnectionPoolAsync(cancellationToken);

        // 显示统计信息
        DisplayStatistics();

        _logger.LogInformation("=== 示例完成 ===");
    }

    /// <summary>
    /// 订阅客户端管理器事件
    /// </summary>
    private void SubscribeToClientManagerEvents()
    {
        _clientManager.ClientConnected += (sender, e) =>
        {
            _logger.LogInformation("客户端已连接: {ClientId}, 协议版本: {ProtocolVersion}, 用户名: {Username}",
                e.Client.ClientId, e.Client.ProtocolVersion, e.Client.Username);
        };

        _clientManager.ClientDisconnected += (sender, e) =>
        {
            _logger.LogInformation("客户端已断开: {ClientId}, 原因: {Reason}",
                e.Client.ClientId, e.Reason);
        };

        _clientManager.ClientAuthenticated += (sender, e) =>
        {
            _logger.LogInformation("客户端认证结果: {ClientId}, 成功: {IsSuccess}, 原因: {FailureReason}",
                e.Client.ClientId, e.IsSuccess, e.FailureReason);
        };
    }

    /// <summary>
    /// 演示客户端连接处理
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>演示任务</returns>
    private async Task DemonstrateClientConnectionAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("--- 演示客户端连接处理 ---");

        // 模拟创建 CONNECT 数据包
        var connectPacket = new MqttConnectPacket
        {
            ClientId = "example_client_001",
            CleanSession = true,
            KeepAlive = 60,
            Username = "admin",
            Password = "admin123",
            ProtocolVersion = MqttProtocolVersion.Version311
        };

        _logger.LogInformation("创建 CONNECT 数据包: ClientId={ClientId}, Username={Username}",
            connectPacket.ClientId, connectPacket.Username);

        // 注意：在实际应用中，这里会有真实的网络连接
        // 这里只是演示如何使用客户端管理器的 API
        _logger.LogInformation("在实际应用中，这里会处理来自网络层的 CONNECT 数据包");

        await Task.CompletedTask;
    }

    /// <summary>
    /// 演示客户端管理功能
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>演示任务</returns>
    private async Task DemonstrateClientManagementAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("--- 演示客户端管理功能 ---");

        // 获取所有连接的客户端
        var allClients = _clientManager.GetAllClients();
        _logger.LogInformation("当前连接的客户端数量: {Count}", allClients.Count());

        foreach (var client in allClients)
        {
            _logger.LogInformation("客户端: {ClientId}, 状态: {State}, 最后活动: {LastActivity}",
                client.ClientId, client.State, client.LastActivity);
        }

        // 检查特定客户端是否已连接
        var testClientId = "test_client";
        var isConnected = _clientManager.IsClientConnected(testClientId);
        _logger.LogInformation("客户端 {ClientId} 是否已连接: {IsConnected}", testClientId, isConnected);

        // 演示清理超时连接
        _logger.LogInformation("执行超时连接清理...");
        await _clientManager.CleanupTimeoutClientsAsync(cancellationToken);

        await Task.CompletedTask;
    }

    /// <summary>
    /// 演示连接池功能
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>演示任务</returns>
    private async Task DemonstrateConnectionPoolAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("--- 演示连接池功能 ---");

        _logger.LogInformation("连接池大小: {PoolSize}/{MaxPoolSize}, 活跃连接: {ActiveConnections}",
            _connectionPool.PoolSize, _connectionPool.MaxPoolSize, _connectionPool.ActiveConnections);

        // 演示连接池清理
        _logger.LogInformation("执行连接池清理...");
        await _connectionPool.CleanupAsync(cancellationToken);

        await Task.CompletedTask;
    }

    /// <summary>
    /// 显示统计信息
    /// </summary>
    private void DisplayStatistics()
    {
        _logger.LogInformation("--- 统计信息 ---");

        // 客户端管理器统计信息
        var clientStats = _clientManager.GetStatistics();
        _logger.LogInformation("客户端管理器统计:");
        _logger.LogInformation("  连接的客户端: {ConnectedClients}/{MaxConnections}",
            clientStats.ConnectedClients, clientStats.MaxConnections);
        _logger.LogInformation("  总连接数: {TotalConnections}", clientStats.TotalConnections);
        _logger.LogInformation("  总断开数: {TotalDisconnections}", clientStats.TotalDisconnections);
        _logger.LogInformation("  被拒绝连接: {RejectedConnections}", clientStats.RejectedConnections);
        _logger.LogInformation("  认证失败: {AuthenticationFailures}", clientStats.AuthenticationFailures);
        _logger.LogInformation("  超时连接: {TimeoutConnections}", clientStats.TimeoutConnections);
        _logger.LogInformation("  连接使用率: {ConnectionUtilization:F2}%", clientStats.ConnectionUtilization);

        // 按协议版本分组
        foreach (var kvp in clientStats.ConnectionsByProtocolVersion)
        {
            _logger.LogInformation("  协议版本 {ProtocolVersion}: {Count} 个连接", kvp.Key, kvp.Value);
        }

        // 按 Clean Session 分组
        foreach (var kvp in clientStats.ConnectionsByCleanSession)
        {
            _logger.LogInformation("  Clean Session {CleanSession}: {Count} 个连接", kvp.Key, kvp.Value);
        }

        // 连接池统计信息
        var poolStats = _connectionPool.GetStatistics();
        _logger.LogInformation("连接池统计:");
        _logger.LogInformation("  池大小: {CurrentPoolSize}/{MaxPoolSize}", 
            poolStats.CurrentPoolSize, poolStats.MaxPoolSize);
        _logger.LogInformation("  活跃连接: {ActiveConnections}", poolStats.ActiveConnections);
        _logger.LogInformation("  总连接数: {TotalConnections}", poolStats.TotalConnections);
        _logger.LogInformation("  重用连接: {ConnectionsReused}", poolStats.ConnectionsReused);
        _logger.LogInformation("  过期连接: {ExpiredConnections}", poolStats.ExpiredConnections);
        _logger.LogInformation("  池使用率: {PoolUtilization:F2}%", poolStats.PoolUtilization);
    }
}

/// <summary>
/// 客户端连接管理示例服务
/// </summary>
public class MqttClientManagementExampleService : BackgroundService
{
    private readonly ILogger<MqttClientManagementExampleService> _logger;
    private readonly MqttClientManagementExample _example;

    /// <summary>
    /// 初始化示例服务
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="example">示例实例</param>
    public MqttClientManagementExampleService(
        ILogger<MqttClientManagementExampleService> logger,
        MqttClientManagementExample example)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _example = example ?? throw new ArgumentNullException(nameof(example));
    }

    /// <summary>
    /// 执行后台任务
    /// </summary>
    /// <param name="stoppingToken">停止令牌</param>
    /// <returns>执行任务</returns>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("MQTT Client Management Example Service started");

        try
        {
            await _example.RunAsync(stoppingToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error running MQTT client management example");
        }

        _logger.LogInformation("MQTT Client Management Example Service completed");
    }
}
