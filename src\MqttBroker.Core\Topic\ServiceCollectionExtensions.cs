using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Network;

namespace MqttBroker.Core.Topic;

/// <summary>
/// 主题订阅系统服务注册扩展
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加主题订阅系统服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddTopicSubscriptionSystem(this IServiceCollection services)
    {
        // 注册核心服务
        services.TryAddSingleton<ITopicSubscriptionManager, TopicSubscriptionManager>();
        services.TryAddSingleton<IMessageDispatcher, MessageDispatcher>();
        
        // 注册数据包处理器
        services.TryAddTransient<IPacketHandler, MqttSubscribePacketHandler>();
        services.TryAddTransient<IPacketHandler, MqttUnsubscribePacketHandler>();

        // 注册主题匹配器（如果尚未注册）
        services.TryAddSingleton<IMqttTopicMatcher, MqttTopicMatcher>();

        // 注册配置选项
        services.AddOptions<TopicSubscriptionOptions>();
        services.AddOptions<MessageDispatchOptions>();

        return services;
    }

    /// <summary>
    /// 添加主题订阅系统服务并配置选项
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureSubscription">订阅配置</param>
    /// <param name="configureDispatch">分发配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddTopicSubscriptionSystem(
        this IServiceCollection services,
        Action<TopicSubscriptionOptions>? configureSubscription = null,
        Action<MessageDispatchOptions>? configureDispatch = null)
    {
        services.AddTopicSubscriptionSystem();

        if (configureSubscription != null)
        {
            services.Configure(configureSubscription);
        }

        if (configureDispatch != null)
        {
            services.Configure(configureDispatch);
        }

        return services;
    }
}
