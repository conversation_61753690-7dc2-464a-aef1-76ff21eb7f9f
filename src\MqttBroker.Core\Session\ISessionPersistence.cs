using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Topic;
using MqttBroker.Core.QoS;

namespace MqttBroker.Core.Session;

/// <summary>
/// 会话持久化服务接口，负责管理客户端会话状态的持久化存储
/// </summary>
public interface ISessionPersistence
{
    /// <summary>
    /// 创建或更新客户端会话
    /// </summary>
    /// <param name="session">会话信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<SessionOperationResult> CreateOrUpdateSessionAsync(MqttSession session, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取客户端会话
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>会话信息，如果不存在则返回null</returns>
    Task<MqttSession?> GetSessionAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除客户端会话
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<SessionOperationResult> DeleteSessionAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查会话是否存在
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    Task<bool> SessionExistsAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 保存客户端订阅信息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="subscriptions">订阅信息列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<SessionOperationResult> SaveSubscriptionsAsync(string clientId, IEnumerable<ClientSubscription> subscriptions, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取客户端订阅信息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅信息列表</returns>
    Task<IEnumerable<ClientSubscription>> GetSubscriptionsAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 保存未确认消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="pendingMessage">未确认消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<SessionOperationResult> SavePendingMessageAsync(string clientId, PendingMessage pendingMessage, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取客户端的未确认消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>未确认消息列表</returns>
    Task<IEnumerable<PendingMessage>> GetPendingMessagesAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除未确认消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="messageId">消息ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<SessionOperationResult> DeletePendingMessageAsync(string clientId, ushort messageId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理过期会话
    /// </summary>
    /// <param name="expiredBefore">过期时间点</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理结果</returns>
    Task<SessionCleanupResult> CleanupExpiredSessionsAsync(DateTime expiredBefore, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量获取活跃会话
    /// </summary>
    /// <param name="limit">限制数量</param>
    /// <param name="offset">偏移量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>会话列表</returns>
    Task<IEnumerable<MqttSession>> GetActiveSessionsAsync(int limit = 100, int offset = 0, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取会话统计信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>统计信息</returns>
    Task<SessionStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新会话的最后活动时间
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="lastActivity">最后活动时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<SessionOperationResult> UpdateSessionActivityAsync(string clientId, DateTime lastActivity, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量删除会话
    /// </summary>
    /// <param name="clientIds">客户端ID列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>批量操作结果</returns>
    Task<BatchSessionOperationResult> DeleteSessionsAsync(IEnumerable<string> clientIds, CancellationToken cancellationToken = default);
}

/// <summary>
/// MQTT 会话信息
/// </summary>
public class MqttSession
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 是否为 Clean Session
    /// </summary>
    public bool IsCleanSession { get; set; }

    /// <summary>
    /// 会话创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后活动时间
    /// </summary>
    public DateTime LastActivity { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 会话过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// 协议版本
    /// </summary>
    public MqttProtocolVersion ProtocolVersion { get; set; } = MqttProtocolVersion.Version311;

    /// <summary>
    /// 保活间隔（秒）
    /// </summary>
    public ushort KeepAliveInterval { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    public string? Username { get; set; }

    /// <summary>
    /// 遗嘱消息
    /// </summary>
    public MqttWillMessage? WillMessage { get; set; }

    /// <summary>
    /// 会话属性（MQTT 5.0）
    /// </summary>
    public Dictionary<string, object>? Properties { get; set; }

    /// <summary>
    /// 是否在线
    /// </summary>
    public bool IsOnline { get; set; }

    /// <summary>
    /// 连接建立时间
    /// </summary>
    public DateTime? ConnectedAt { get; set; }

    /// <summary>
    /// 断开连接时间
    /// </summary>
    public DateTime? DisconnectedAt { get; set; }

    /// <summary>
    /// 会话状态
    /// </summary>
    public SessionState State { get; set; } = SessionState.Active;

    /// <summary>
    /// 检查会话是否过期
    /// </summary>
    /// <returns>是否过期</returns>
    public bool IsExpired()
    {
        return ExpiresAt.HasValue && DateTime.UtcNow > ExpiresAt.Value;
    }

    /// <summary>
    /// 获取会话年龄
    /// </summary>
    /// <returns>会话年龄</returns>
    public TimeSpan Age => DateTime.UtcNow - CreatedAt;

    /// <summary>
    /// 获取最后活动间隔
    /// </summary>
    /// <returns>最后活动间隔</returns>
    public TimeSpan LastActivityAge => DateTime.UtcNow - LastActivity;
}

/// <summary>
/// 会话状态枚举
/// </summary>
public enum SessionState
{
    /// <summary>
    /// 活跃状态
    /// </summary>
    Active,

    /// <summary>
    /// 离线状态
    /// </summary>
    Offline,

    /// <summary>
    /// 过期状态
    /// </summary>
    Expired,

    /// <summary>
    /// 已删除状态
    /// </summary>
    Deleted
}
