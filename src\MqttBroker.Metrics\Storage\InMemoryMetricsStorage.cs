using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MqttBroker.Metrics.Configuration;
using MqttBroker.Metrics.Models;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace MqttBroker.Metrics.Storage;

/// <summary>
/// 内存指标存储实现
/// </summary>
public class InMemoryMetricsStorage : IMetricsStorage, IDisposable
{
    private readonly ConcurrentQueue<PerformanceMetrics> _metricsQueue = new();
    private readonly ILogger<InMemoryMetricsStorage> _logger;
    private readonly MetricsOptions _options;
    private readonly Timer _cleanupTimer;
    private readonly object _lockObject = new();

    // 性能统计
    private long _totalWrites;
    private long _totalReads;
    private double _totalWriteTime;
    private double _totalReadTime;

    /// <summary>
    /// 构造函数
    /// </summary>
    public InMemoryMetricsStorage(
        ILogger<InMemoryMetricsStorage> logger,
        IOptions<MetricsOptions> options)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));

        // 启动定期清理过期数据的定时器
        _cleanupTimer = new Timer(
            CleanupCallback,
            null,
            TimeSpan.FromMinutes(10), // 10分钟后开始第一次清理
            TimeSpan.FromMinutes(10)  // 每10分钟清理一次
        );

        _logger.LogInformation("内存指标存储已初始化，最大数据点数: {MaxDataPoints}", _options.MaxInMemoryDataPoints);
    }

    /// <summary>
    /// 存储性能指标
    /// </summary>
    public Task StoreMetricsAsync(PerformanceMetrics metrics, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            if (metrics == null)
            {
                throw new ArgumentNullException(nameof(metrics));
            }

            _metricsQueue.Enqueue(metrics);

            // 如果队列过长，移除最旧的数据
            while (_metricsQueue.Count > _options.MaxInMemoryDataPoints)
            {
                _metricsQueue.TryDequeue(out _);
            }

            Interlocked.Increment(ref _totalWrites);
            _totalWriteTime += stopwatch.Elapsed.TotalMilliseconds;

            _logger.LogDebug("已存储性能指标，时间戳: {Timestamp}，队列大小: {QueueSize}",
                metrics.Timestamp, _metricsQueue.Count);

            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "存储性能指标时发生错误");
            throw;
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    /// <summary>
    /// 批量存储性能指标
    /// </summary>
    public Task StoreMetricsBatchAsync(IEnumerable<PerformanceMetrics> metricsList, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            if (metricsList == null)
            {
                throw new ArgumentNullException(nameof(metricsList));
            }

            var count = 0;
            foreach (var metrics in metricsList)
            {
                _metricsQueue.Enqueue(metrics);
                count++;
            }

            // 如果队列过长，移除最旧的数据
            while (_metricsQueue.Count > _options.MaxInMemoryDataPoints)
            {
                _metricsQueue.TryDequeue(out _);
            }

            Interlocked.Add(ref _totalWrites, count);
            _totalWriteTime += stopwatch.Elapsed.TotalMilliseconds;

            _logger.LogDebug("已批量存储 {Count} 个性能指标，队列大小: {QueueSize}",
                count, _metricsQueue.Count);

            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量存储性能指标时发生错误");
            throw;
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    /// <summary>
    /// 获取指定时间范围内的性能指标
    /// </summary>
    public Task<IEnumerable<PerformanceMetrics>> GetMetricsAsync(DateTime startTime, DateTime endTime, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            var result = _metricsQueue
                .Where(m => m.Timestamp >= startTime && m.Timestamp <= endTime)
                .OrderBy(m => m.Timestamp)
                .ToList();

            Interlocked.Increment(ref _totalReads);
            _totalReadTime += stopwatch.Elapsed.TotalMilliseconds;

            _logger.LogDebug("获取时间范围 {StartTime} - {EndTime} 内的指标，返回 {Count} 条记录",
                startTime, endTime, result.Count);

            return Task.FromResult<IEnumerable<PerformanceMetrics>>(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取指定时间范围内的性能指标时发生错误");
            throw;
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    /// <summary>
    /// 获取最新的性能指标
    /// </summary>
    public Task<IEnumerable<PerformanceMetrics>> GetLatestMetricsAsync(int count = 1, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            var result = _metricsQueue
                .OrderByDescending(m => m.Timestamp)
                .Take(count)
                .OrderBy(m => m.Timestamp)
                .ToList();

            Interlocked.Increment(ref _totalReads);
            _totalReadTime += stopwatch.Elapsed.TotalMilliseconds;

            _logger.LogDebug("获取最新 {Count} 条指标记录，实际返回 {ActualCount} 条",
                count, result.Count);

            return Task.FromResult<IEnumerable<PerformanceMetrics>>(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取最新性能指标时发生错误");
            throw;
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    /// <summary>
    /// 获取聚合指标数据
    /// </summary>
    public Task<IEnumerable<AggregatedMetrics>> GetAggregatedMetricsAsync(DateTime startTime, DateTime endTime, int aggregationInterval, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            var metrics = _metricsQueue
                .Where(m => m.Timestamp >= startTime && m.Timestamp <= endTime)
                .OrderBy(m => m.Timestamp)
                .ToList();

            var aggregatedResults = new List<AggregatedMetrics>();
            var intervalSpan = TimeSpan.FromSeconds(aggregationInterval);

            for (var windowStart = startTime; windowStart < endTime; windowStart = windowStart.Add(intervalSpan))
            {
                var windowEnd = windowStart.Add(intervalSpan);
                var windowMetrics = metrics
                    .Where(m => m.Timestamp >= windowStart && m.Timestamp < windowEnd)
                    .ToList();

                if (windowMetrics.Count == 0)
                    continue;

                var aggregated = AggregateMetrics(windowMetrics, windowStart, windowEnd, aggregationInterval);
                aggregatedResults.Add(aggregated);
            }

            Interlocked.Increment(ref _totalReads);
            _totalReadTime += stopwatch.Elapsed.TotalMilliseconds;

            _logger.LogDebug("聚合时间范围 {StartTime} - {EndTime} 内的指标，间隔 {Interval}s，返回 {Count} 个聚合结果",
                startTime, endTime, aggregationInterval, aggregatedResults.Count);

            return Task.FromResult<IEnumerable<AggregatedMetrics>>(aggregatedResults);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取聚合指标数据时发生错误");
            throw;
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    /// <summary>
    /// 清理过期的指标数据
    /// </summary>
    public Task<int> CleanupExpiredMetricsAsync(TimeSpan retentionPeriod, CancellationToken cancellationToken = default)
    {
        try
        {
            var cutoffTime = DateTime.UtcNow - retentionPeriod;
            var removedCount = 0;

            lock (_lockObject)
            {
                var tempList = new List<PerformanceMetrics>();

                // 将未过期的数据保留
                while (_metricsQueue.TryDequeue(out var metrics))
                {
                    if (metrics.Timestamp >= cutoffTime)
                    {
                        tempList.Add(metrics);
                    }
                    else
                    {
                        removedCount++;
                    }
                }

                // 将未过期的数据重新入队
                foreach (var metrics in tempList)
                {
                    _metricsQueue.Enqueue(metrics);
                }
            }

            _logger.LogInformation("清理过期指标数据完成，删除 {RemovedCount} 条记录，保留期间: {RetentionPeriod}",
                removedCount, retentionPeriod);

            return Task.FromResult(removedCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期指标数据时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 获取存储统计信息
    /// </summary>
    public Task<MetricsStorageStatistics> GetStorageStatisticsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var metrics = _metricsQueue.ToArray();
            var statistics = new MetricsStorageStatistics
            {
                TotalRecords = metrics.Length,
                StorageSize = EstimateStorageSize(metrics),
                EarliestRecord = metrics.Length > 0 ? metrics.Min(m => m.Timestamp) : null,
                LatestRecord = metrics.Length > 0 ? metrics.Max(m => m.Timestamp) : null,
                AverageWriteLatency = _totalWrites > 0 ? _totalWriteTime / _totalWrites : 0,
                AverageReadLatency = _totalReads > 0 ? _totalReadTime / _totalReads : 0
            };

            return Task.FromResult(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取存储统计信息时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 聚合指标数据
    /// </summary>
    private static AggregatedMetrics AggregateMetrics(List<PerformanceMetrics> metrics, DateTime windowStart, DateTime windowEnd, int intervalSeconds)
    {
        if (metrics.Count == 0)
        {
            return new AggregatedMetrics
            {
                WindowStart = windowStart,
                WindowEnd = windowEnd,
                IntervalSeconds = intervalSeconds,
                DataPointCount = 0
            };
        }

        return new AggregatedMetrics
        {
            WindowStart = windowStart,
            WindowEnd = windowEnd,
            IntervalSeconds = intervalSeconds,
            DataPointCount = metrics.Count,
            Connection = new ConnectionMetricsAggregation
            {
                AverageActiveConnections = metrics.Average(m => m.Connection.ActiveConnections),
                MaxActiveConnections = metrics.Max(m => m.Connection.ActiveConnections),
                MinActiveConnections = metrics.Min(m => m.Connection.ActiveConnections),
                TotalConnectionsIncrement = metrics.Max(m => m.Connection.TotalConnections) - metrics.Min(m => m.Connection.TotalConnections),
                AverageConnectionRate = metrics.Average(m => m.Connection.ConnectionRate),
                AverageDisconnectionRate = metrics.Average(m => m.Connection.DisconnectionRate),
                AuthenticationFailures = metrics.Max(m => m.Connection.AuthenticationFailures) - metrics.Min(m => m.Connection.AuthenticationFailures),
                ConnectionTimeouts = metrics.Max(m => m.Connection.ConnectionTimeouts) - metrics.Min(m => m.Connection.ConnectionTimeouts)
            },
            Message = new MessageMetricsAggregation
            {
                MessagesSent = metrics.Max(m => m.Message.MessagesSent) - metrics.Min(m => m.Message.MessagesSent),
                MessagesReceived = metrics.Max(m => m.Message.MessagesReceived) - metrics.Min(m => m.Message.MessagesReceived),
                AverageMessageSendRate = metrics.Average(m => m.Message.MessageSendRate),
                AverageMessageReceiveRate = metrics.Average(m => m.Message.MessageReceiveRate),
                AverageMessageSize = metrics.Average(m => m.Message.AverageMessageSize),
                RoutingLatency = new LatencyAggregation
                {
                    Average = metrics.Average(m => m.Message.RoutingLatency.Average),
                    Min = metrics.Min(m => m.Message.RoutingLatency.Min),
                    Max = metrics.Max(m => m.Message.RoutingLatency.Max),
                    P50 = metrics.Average(m => m.Message.RoutingLatency.P50),
                    P95 = metrics.Average(m => m.Message.RoutingLatency.P95),
                    P99 = metrics.Average(m => m.Message.RoutingLatency.P99)
                },
                OfflineMessages = metrics.Max(m => m.Message.OfflineMessages),
                DeadLetterMessages = metrics.Max(m => m.Message.DeadLetterMessages),
                RetainedMessages = metrics.Max(m => m.Message.RetainedMessages)
            }
            // 可以继续添加其他指标的聚合...
        };
    }

    /// <summary>
    /// 估算存储大小
    /// </summary>
    private static long EstimateStorageSize(PerformanceMetrics[] metrics)
    {
        // 简单估算，每个指标对象大约 1KB
        return metrics.Length * 1024;
    }

    /// <summary>
    /// 清理回调
    /// </summary>
    private async void CleanupCallback(object? state)
    {
        try
        {
            var retentionPeriod = TimeSpan.FromHours(_options.DataRetentionHours);
            await CleanupExpiredMetricsAsync(retentionPeriod);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "定时清理过期指标数据时发生错误");
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        _cleanupTimer?.Dispose();
        GC.SuppressFinalize(this);
    }
}
