using System.Collections.Concurrent;
using System.Diagnostics;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MqttBroker.Core.Client;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;

namespace MqttBroker.Core.Topic;

/// <summary>
/// 消息分发器实现
/// </summary>
public class MessageDispatcher : IMessageDispatcher, IDisposable
{
    private readonly ITopicSubscriptionManager _subscriptionManager;
    private readonly ILogger<MessageDispatcher> _logger;
    private readonly MessageDispatchOptions _options;
    private readonly ConcurrentDictionary<string, long> _dispatchCounters;
    private readonly object _statsLock = new();
    private MessageDispatchStatistics _statistics;
    private bool _disposed;

    /// <summary>
    /// 消息分发事件
    /// </summary>
    public event EventHandler<MessageDispatchedEventArgs>? MessageDispatched;

    /// <summary>
    /// 消息分发失败事件
    /// </summary>
    public event EventHandler<MessageDispatchFailedEventArgs>? MessageDispatchFailed;

    /// <summary>
    /// 初始化消息分发器
    /// </summary>
    /// <param name="subscriptionManager">订阅管理器</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="options">配置选项</param>
    public MessageDispatcher(
        ITopicSubscriptionManager subscriptionManager,
        ILogger<MessageDispatcher> logger,
        IOptions<MessageDispatchOptions> options)
    {
        _subscriptionManager = subscriptionManager ?? throw new ArgumentNullException(nameof(subscriptionManager));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? new MessageDispatchOptions();
        
        _dispatchCounters = new ConcurrentDictionary<string, long>();
        _statistics = new MessageDispatchStatistics();
    }

    /// <summary>
    /// 分发消息到匹配的订阅者
    /// </summary>
    /// <param name="publishPacket">发布数据包</param>
    /// <param name="publisherClientId">发布者客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分发结果</returns>
    public async Task<MessageDispatchResult> DispatchAsync(MqttPublishPacket publishPacket, string? publisherClientId = null, CancellationToken cancellationToken = default)
    {
        if (publishPacket == null)
            throw new ArgumentNullException(nameof(publishPacket));

        var stopwatch = Stopwatch.StartNew();
        var topicName = publishPacket.Topic;

        try
        {
            _logger.LogDebug("Dispatching message to topic: {TopicName} from publisher: {PublisherId}", 
                topicName, publisherClientId ?? "system");

            // 获取匹配的订阅者
            var subscribers = await _subscriptionManager.GetSubscribersAsync(topicName, cancellationToken);
            
            if (subscribers.Count == 0)
            {
                _logger.LogDebug("No subscribers found for topic: {TopicName}", topicName);
                stopwatch.Stop();
                
                var emptyResult = MessageDispatchResult.Success(topicName, 0, 0, stopwatch.ElapsedMilliseconds);
                UpdateStatistics(emptyResult);
                return emptyResult;
            }

            // 过滤订阅者（排除发布者自己，如果设置了 NoLocal）
            var filteredSubscribers = FilterSubscribers(subscribers, publisherClientId);
            
            _logger.LogDebug("Found {TotalCount} subscribers, {FilteredCount} after filtering for topic: {TopicName}", 
                subscribers.Count, filteredSubscribers.Count, topicName);

            // 并行分发消息到订阅者
            var dispatchTasks = filteredSubscribers.Select(subscriber => 
                DispatchToSubscriberAsync(publishPacket, subscriber, cancellationToken)).ToArray();

            var dispatchResults = await Task.WhenAll(dispatchTasks);
            
            stopwatch.Stop();

            // 统计结果
            var successfulDispatches = dispatchResults.Count(r => r.IsSuccess);
            var result = MessageDispatchResult.Success(topicName, filteredSubscribers.Count, successfulDispatches, stopwatch.ElapsedMilliseconds);
            result.DispatchDetails = dispatchResults.ToList();

            // 更新统计信息
            UpdateStatistics(result);

            // 触发事件
            MessageDispatched?.Invoke(this, new MessageDispatchedEventArgs(result, publishPacket));

            _logger.LogDebug("Message dispatch completed for topic: {TopicName}. {SuccessCount}/{TotalCount} successful in {ElapsedMs}ms", 
                topicName, successfulDispatches, filteredSubscribers.Count, stopwatch.ElapsedMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error dispatching message to topic: {TopicName}", topicName);
            
            var errorResult = MessageDispatchResult.Failure(topicName, ex.Message);
            errorResult.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
            
            UpdateStatistics(errorResult);
            MessageDispatchFailed?.Invoke(this, new MessageDispatchFailedEventArgs(topicName, ex.Message, publishPacket, ex));
            
            return errorResult;
        }
    }

    /// <summary>
    /// 批量分发消息
    /// </summary>
    /// <param name="messages">消息列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分发结果列表</returns>
    public async Task<IList<MessageDispatchResult>> DispatchBatchAsync(IList<MessageDispatchRequest> messages, CancellationToken cancellationToken = default)
    {
        if (messages == null)
            throw new ArgumentNullException(nameof(messages));

        _logger.LogDebug("Dispatching batch of {Count} messages", messages.Count);

        var dispatchTasks = messages.Select(message => 
            DispatchAsync(message.PublishPacket, message.PublisherClientId, cancellationToken)).ToArray();

        var results = await Task.WhenAll(dispatchTasks);
        
        _logger.LogDebug("Batch dispatch completed. {SuccessCount}/{TotalCount} messages dispatched successfully", 
            results.Count(r => r.IsSuccess), results.Length);

        return results.ToList();
    }

    /// <summary>
    /// 获取分发统计信息
    /// </summary>
    /// <returns>分发统计信息</returns>
    public Task<MessageDispatchStatistics> GetStatisticsAsync()
    {
        lock (_statsLock)
        {
            // 创建统计信息的副本
            var stats = new MessageDispatchStatistics
            {
                TotalMessages = _statistics.TotalMessages,
                SuccessfulDispatches = _statistics.SuccessfulDispatches,
                FailedDispatches = _statistics.FailedDispatches,
                AverageDispatchLatency = _statistics.AverageDispatchLatency,
                MaxDispatchLatency = _statistics.MaxDispatchLatency,
                MessagesPerSecond = _statistics.MessagesPerSecond,
                ActiveSubscribers = _statistics.ActiveSubscribers
            };

            return Task.FromResult(stats);
        }
    }

    #region 私有方法

    /// <summary>
    /// 过滤订阅者
    /// </summary>
    /// <param name="subscribers">订阅者列表</param>
    /// <param name="publisherClientId">发布者客户端ID</param>
    /// <returns>过滤后的订阅者列表</returns>
    private List<TopicSubscriber> FilterSubscribers(IList<TopicSubscriber> subscribers, string? publisherClientId)
    {
        var filtered = new List<TopicSubscriber>();

        foreach (var subscriber in subscribers)
        {
            // 检查 NoLocal 选项
            if (subscriber.Options.NoLocal && subscriber.ClientId == publisherClientId)
            {
                _logger.LogTrace("Skipping subscriber {ClientId} due to NoLocal option", subscriber.ClientId);
                continue;
            }

            // 检查客户端是否仍然连接
            if (subscriber.Client?.State != MqttClientState.Authenticated)
            {
                _logger.LogTrace("Skipping disconnected subscriber {ClientId}", subscriber.ClientId);
                continue;
            }

            filtered.Add(subscriber);
        }

        return filtered;
    }

    /// <summary>
    /// 分发消息到单个订阅者
    /// </summary>
    /// <param name="publishPacket">发布数据包</param>
    /// <param name="subscriber">订阅者</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分发结果</returns>
    private async Task<SubscriberDispatchResult> DispatchToSubscriberAsync(MqttPublishPacket publishPacket, TopicSubscriber subscriber, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            if (subscriber.Client == null)
            {
                return new SubscriberDispatchResult
                {
                    ClientId = subscriber.ClientId,
                    TopicFilter = subscriber.TopicFilter,
                    IsSuccess = false,
                    ErrorMessage = "Client reference is null",
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                };
            }

            // 确定使用的QoS级别（取发布QoS和订阅QoS的最小值）
            var effectiveQoS = (MqttQoSLevel)Math.Min((int)publishPacket.QoSLevel, (int)subscriber.QoSLevel);

            // 创建要发送给订阅者的PUBLISH数据包
            var subscriberPacket = CreateSubscriberPacket(publishPacket, subscriber, effectiveQoS);

            // 发送数据包
            await subscriber.Client.SendPacketAsync(subscriberPacket, cancellationToken);

            stopwatch.Stop();

            _logger.LogTrace("Message dispatched to subscriber {ClientId} for topic filter {TopicFilter} with QoS {QoS} in {ElapsedMs}ms", 
                subscriber.ClientId, subscriber.TopicFilter, effectiveQoS, stopwatch.ElapsedMilliseconds);

            return new SubscriberDispatchResult
            {
                ClientId = subscriber.ClientId,
                TopicFilter = subscriber.TopicFilter,
                IsSuccess = true,
                QoSLevel = effectiveQoS,
                ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
            };
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            _logger.LogWarning(ex, "Failed to dispatch message to subscriber {ClientId} for topic filter {TopicFilter}", 
                subscriber.ClientId, subscriber.TopicFilter);

            return new SubscriberDispatchResult
            {
                ClientId = subscriber.ClientId,
                TopicFilter = subscriber.TopicFilter,
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
            };
        }
    }

    /// <summary>
    /// 为订阅者创建PUBLISH数据包
    /// </summary>
    /// <param name="originalPacket">原始发布数据包</param>
    /// <param name="subscriber">订阅者</param>
    /// <param name="effectiveQoS">有效QoS级别</param>
    /// <returns>订阅者数据包</returns>
    private MqttPublishPacket CreateSubscriberPacket(MqttPublishPacket originalPacket, TopicSubscriber subscriber, MqttQoSLevel effectiveQoS)
    {
        var subscriberPacket = MqttPublishPacket.Create(
            originalPacket.Topic,
            originalPacket.Payload,
            effectiveQoS,
            originalPacket.Retain && !subscriber.Options.RetainAsPublished ? false : originalPacket.Retain);

        // 如果需要数据包标识符（QoS > 0），生成一个新的
        if (effectiveQoS > MqttQoSLevel.AtMostOnce)
        {
            subscriberPacket.PacketIdentifier = GeneratePacketIdentifier();
        }

        // 复制MQTT 5.0属性（如果适用）
        if (originalPacket.Properties != null)
        {
            subscriberPacket.Properties = originalPacket.Properties;
        }

        return subscriberPacket;
    }

    /// <summary>
    /// 生成数据包标识符
    /// </summary>
    /// <returns>数据包标识符</returns>
    private ushort GeneratePacketIdentifier()
    {
        // 简单的递增计数器，实际实现中应该考虑线程安全和重用
        return (ushort)(DateTime.UtcNow.Ticks % ushort.MaxValue + 1);
    }

    /// <summary>
    /// 更新统计信息
    /// </summary>
    /// <param name="result">分发结果</param>
    private void UpdateStatistics(MessageDispatchResult result)
    {
        lock (_statsLock)
        {
            _statistics.TotalMessages++;
            
            if (result.IsSuccess)
            {
                _statistics.SuccessfulDispatches += result.SuccessfulDispatches;
                _statistics.FailedDispatches += result.FailedDispatches;
            }
            else
            {
                _statistics.FailedDispatches++;
            }

            // 更新延迟统计
            if (result.ElapsedMilliseconds > _statistics.MaxDispatchLatency)
            {
                _statistics.MaxDispatchLatency = result.ElapsedMilliseconds;
            }

            // 计算平均延迟（简单移动平均）
            _statistics.AverageDispatchLatency = (_statistics.AverageDispatchLatency * (_statistics.TotalMessages - 1) + result.ElapsedMilliseconds) / _statistics.TotalMessages;
        }
    }

    #endregion

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _dispatchCounters.Clear();
            _disposed = true;
        }
    }
}

/// <summary>
/// 消息分发配置选项
/// </summary>
public class MessageDispatchOptions
{
    /// <summary>
    /// 最大并发分发数
    /// </summary>
    public int MaxConcurrentDispatches { get; set; } = 1000;

    /// <summary>
    /// 分发超时时间（毫秒）
    /// </summary>
    public int DispatchTimeoutMs { get; set; } = 5000;

    /// <summary>
    /// 是否启用批量分发优化
    /// </summary>
    public bool EnableBatchOptimization { get; set; } = true;

    /// <summary>
    /// 批量分发大小
    /// </summary>
    public int BatchSize { get; set; } = 100;

    /// <summary>
    /// 是否启用分发统计
    /// </summary>
    public bool EnableStatistics { get; set; } = true;
}
