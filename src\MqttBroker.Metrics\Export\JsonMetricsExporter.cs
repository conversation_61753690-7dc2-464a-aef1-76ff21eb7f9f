using Microsoft.Extensions.Logging;
using MqttBroker.Metrics.Models;
using MqttBroker.Metrics.Storage;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace MqttBroker.Metrics.Export;

/// <summary>
/// JSON 格式指标导出器
/// </summary>
public class JsonMetricsExporter : IMetricsExporter
{
    private readonly ILogger<JsonMetricsExporter> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    /// <summary>
    /// 导出格式
    /// </summary>
    public string Format => "json";

    /// <summary>
    /// 构造函数
    /// </summary>
    public JsonMetricsExporter(ILogger<JsonMetricsExporter> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        _jsonOptions = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            Converters = { new JsonStringEnumConverter() }
        };
    }

    /// <summary>
    /// 导出当前指标
    /// </summary>
    public Task<string> ExportCurrentMetricsAsync(PerformanceMetrics metrics, CancellationToken cancellationToken = default)
    {
        try
        {
            if (metrics == null)
                throw new ArgumentNullException(nameof(metrics));

            var json = JsonSerializer.Serialize(metrics, _jsonOptions);
            _logger.LogDebug("已导出当前指标为 JSON 格式，大小: {Size} 字节", json.Length);
            
            return Task.FromResult(json);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出当前指标为 JSON 格式时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 导出历史指标
    /// </summary>
    public Task<string> ExportHistoricalMetricsAsync(IEnumerable<PerformanceMetrics> metricsList, CancellationToken cancellationToken = default)
    {
        try
        {
            if (metricsList == null)
                throw new ArgumentNullException(nameof(metricsList));

            var metricsArray = metricsList.ToArray();
            var exportData = new
            {
                ExportedAt = DateTime.UtcNow,
                TotalRecords = metricsArray.Length,
                TimeRange = metricsArray.Length > 0 ? new
                {
                    StartTime = metricsArray.Min(m => m.Timestamp),
                    EndTime = metricsArray.Max(m => m.Timestamp)
                } : null,
                Metrics = metricsArray
            };

            var json = JsonSerializer.Serialize(exportData, _jsonOptions);
            _logger.LogDebug("已导出 {Count} 条历史指标为 JSON 格式，大小: {Size} 字节", 
                metricsArray.Length, json.Length);
            
            return Task.FromResult(json);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出历史指标为 JSON 格式时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 导出聚合指标
    /// </summary>
    public Task<string> ExportAggregatedMetricsAsync(IEnumerable<AggregatedMetrics> aggregatedMetrics, CancellationToken cancellationToken = default)
    {
        try
        {
            if (aggregatedMetrics == null)
                throw new ArgumentNullException(nameof(aggregatedMetrics));

            var metricsArray = aggregatedMetrics.ToArray();
            var exportData = new
            {
                ExportedAt = DateTime.UtcNow,
                TotalWindows = metricsArray.Length,
                TimeRange = metricsArray.Length > 0 ? new
                {
                    StartTime = metricsArray.Min(m => m.WindowStart),
                    EndTime = metricsArray.Max(m => m.WindowEnd)
                } : null,
                AggregatedMetrics = metricsArray
            };

            var json = JsonSerializer.Serialize(exportData, _jsonOptions);
            _logger.LogDebug("已导出 {Count} 个聚合指标窗口为 JSON 格式，大小: {Size} 字节", 
                metricsArray.Length, json.Length);
            
            return Task.FromResult(json);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出聚合指标为 JSON 格式时发生错误");
            throw;
        }
    }
}

/// <summary>
/// CSV 格式指标导出器
/// </summary>
public class CsvMetricsExporter : IMetricsExporter
{
    private readonly ILogger<CsvMetricsExporter> _logger;

    /// <summary>
    /// 导出格式
    /// </summary>
    public string Format => "csv";

    /// <summary>
    /// 构造函数
    /// </summary>
    public CsvMetricsExporter(ILogger<CsvMetricsExporter> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 导出当前指标
    /// </summary>
    public Task<string> ExportCurrentMetricsAsync(PerformanceMetrics metrics, CancellationToken cancellationToken = default)
    {
        try
        {
            if (metrics == null)
                throw new ArgumentNullException(nameof(metrics));

            var csv = ConvertMetricsToCsv(new[] { metrics });
            _logger.LogDebug("已导出当前指标为 CSV 格式，大小: {Size} 字节", csv.Length);
            
            return Task.FromResult(csv);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出当前指标为 CSV 格式时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 导出历史指标
    /// </summary>
    public Task<string> ExportHistoricalMetricsAsync(IEnumerable<PerformanceMetrics> metricsList, CancellationToken cancellationToken = default)
    {
        try
        {
            if (metricsList == null)
                throw new ArgumentNullException(nameof(metricsList));

            var metricsArray = metricsList.ToArray();
            var csv = ConvertMetricsToCsv(metricsArray);
            _logger.LogDebug("已导出 {Count} 条历史指标为 CSV 格式，大小: {Size} 字节", 
                metricsArray.Length, csv.Length);
            
            return Task.FromResult(csv);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出历史指标为 CSV 格式时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 导出聚合指标
    /// </summary>
    public Task<string> ExportAggregatedMetricsAsync(IEnumerable<AggregatedMetrics> aggregatedMetrics, CancellationToken cancellationToken = default)
    {
        try
        {
            if (aggregatedMetrics == null)
                throw new ArgumentNullException(nameof(aggregatedMetrics));

            var metricsArray = aggregatedMetrics.ToArray();
            var csv = ConvertAggregatedMetricsToCsv(metricsArray);
            _logger.LogDebug("已导出 {Count} 个聚合指标窗口为 CSV 格式，大小: {Size} 字节", 
                metricsArray.Length, csv.Length);
            
            return Task.FromResult(csv);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出聚合指标为 CSV 格式时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 将指标转换为 CSV 格式
    /// </summary>
    private string ConvertMetricsToCsv(PerformanceMetrics[] metrics)
    {
        if (metrics.Length == 0)
            return string.Empty;

        var lines = new List<string>();

        // CSV 头部
        lines.Add("Timestamp,ActiveConnections,PeakConnections,TotalConnections,MessagesSent,MessagesReceived," +
                 "MessageSendRate,MessageReceiveRate,TotalSubscriptions,ActiveTopics,QoS0Messages,QoS1Messages," +
                 "QoS2Messages,CpuUsage,TotalMemory,BytesSent,BytesReceived");

        // 数据行
        foreach (var metric in metrics)
        {
            var line = $"{metric.Timestamp:yyyy-MM-dd HH:mm:ss}," +
                      $"{metric.Connection.ActiveConnections}," +
                      $"{metric.Connection.PeakConnections}," +
                      $"{metric.Connection.TotalConnections}," +
                      $"{metric.Message.MessagesSent}," +
                      $"{metric.Message.MessagesReceived}," +
                      $"{metric.Message.MessageSendRate:F2}," +
                      $"{metric.Message.MessageReceiveRate:F2}," +
                      $"{metric.Subscription.TotalSubscriptions}," +
                      $"{metric.Subscription.ActiveTopics}," +
                      $"{metric.QoS.QoS0Messages}," +
                      $"{metric.QoS.QoS1Messages}," +
                      $"{metric.QoS.QoS2Messages}," +
                      $"{metric.System.CpuUsage:F2}," +
                      $"{metric.System.Memory.TotalMemory}," +
                      $"{metric.Network.BytesSent}," +
                      $"{metric.Network.BytesReceived}";

            lines.Add(line);
        }

        return string.Join(Environment.NewLine, lines);
    }

    /// <summary>
    /// 将聚合指标转换为 CSV 格式
    /// </summary>
    private string ConvertAggregatedMetricsToCsv(AggregatedMetrics[] aggregatedMetrics)
    {
        if (aggregatedMetrics.Length == 0)
            return string.Empty;

        var lines = new List<string>();

        // CSV 头部
        lines.Add("WindowStart,WindowEnd,IntervalSeconds,DataPointCount,AvgActiveConnections,MaxActiveConnections," +
                 "MessagesSent,MessagesReceived,AvgMessageSendRate,AvgMessageReceiveRate,AvgTotalSubscriptions," +
                 "AvgActiveTopics,QoS0Messages,QoS1Messages,QoS2Messages,AvgCpuUsage,MaxCpuUsage,BytesSent,BytesReceived");

        // 数据行
        foreach (var metric in aggregatedMetrics)
        {
            var line = $"{metric.WindowStart:yyyy-MM-dd HH:mm:ss}," +
                      $"{metric.WindowEnd:yyyy-MM-dd HH:mm:ss}," +
                      $"{metric.IntervalSeconds}," +
                      $"{metric.DataPointCount}," +
                      $"{metric.Connection.AverageActiveConnections:F2}," +
                      $"{metric.Connection.MaxActiveConnections}," +
                      $"{metric.Message.MessagesSent}," +
                      $"{metric.Message.MessagesReceived}," +
                      $"{metric.Message.AverageMessageSendRate:F2}," +
                      $"{metric.Message.AverageMessageReceiveRate:F2}," +
                      $"{metric.Subscription.AverageTotalSubscriptions:F2}," +
                      $"{metric.Subscription.AverageActiveTopics:F2}," +
                      $"{metric.QoS.QoS0Messages}," +
                      $"{metric.QoS.QoS1Messages}," +
                      $"{metric.QoS.QoS2Messages}," +
                      $"{metric.System.AverageCpuUsage:F2}," +
                      $"{metric.System.MaxCpuUsage:F2}," +
                      $"{metric.Network.BytesSent}," +
                      $"{metric.Network.BytesReceived}";

            lines.Add(line);
        }

        return string.Join(Environment.NewLine, lines);
    }
}
