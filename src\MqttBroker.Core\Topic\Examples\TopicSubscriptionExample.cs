using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MqttBroker.Core.Client;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Network;

namespace MqttBroker.Core.Topic.Examples;

/// <summary>
/// 主题订阅系统使用示例
/// </summary>
public class TopicSubscriptionExample
{
    /// <summary>
    /// 运行示例
    /// </summary>
    public static async Task RunAsync()
    {
        // 创建服务容器
        var services = new ServiceCollection();
        
        // 添加日志服务
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        
        // 添加主题订阅系统
        services.AddTopicSubscriptionSystem(
            subscription =>
            {
                subscription.MaxSubscriptionsPerClient = 100;
                subscription.MaxQoSLevel = MqttQoSLevel.ExactlyOnce;
                subscription.AllowWildcardSubscriptions = true;
                subscription.AllowSystemTopicSubscriptions = true;
            },
            dispatch =>
            {
                dispatch.MaxConcurrentDispatches = 1000;
                dispatch.DispatchTimeoutMs = 5000;
                dispatch.EnableBatchOptimization = true;
                dispatch.BatchSize = 50;
            });

        // 构建服务提供者
        var serviceProvider = services.BuildServiceProvider();
        var logger = serviceProvider.GetRequiredService<ILogger<TopicSubscriptionExample>>();

        try
        {
            logger.LogInformation("=== MQTT Broker 主题订阅系统示例 ===");

            // 获取服务
            var subscriptionManager = serviceProvider.GetRequiredService<ITopicSubscriptionManager>();
            var messageDispatcher = serviceProvider.GetRequiredService<IMessageDispatcher>();

            // 注册事件处理器
            subscriptionManager.SubscriberAdded += (sender, args) =>
            {
                logger.LogInformation("订阅者已添加: {ClientId} -> {TopicFilter} (QoS: {QoS})",
                    args.Subscriber.ClientId, args.Subscriber.TopicFilter, args.Subscriber.QoSLevel);
            };

            subscriptionManager.SubscriberRemoved += (sender, args) =>
            {
                logger.LogInformation("订阅者已移除: {ClientId} -> {TopicFilter}",
                    args.ClientId, args.TopicFilter);
            };

            messageDispatcher.MessageDispatched += (sender, args) =>
            {
                logger.LogInformation("消息已分发: {TopicName} -> {SuccessCount}/{TotalCount} 订阅者 ({ElapsedMs}ms)",
                    args.Result.TopicName, args.Result.SuccessfulDispatches, 
                    args.Result.MatchedSubscribers, args.Result.ElapsedMilliseconds);
            };

            // 示例1: 基本订阅和消息分发
            await BasicSubscriptionExample(subscriptionManager, messageDispatcher, logger);

            // 示例2: 通配符订阅
            await WildcardSubscriptionExample(subscriptionManager, messageDispatcher, logger);

            // 示例3: 批量操作
            await BatchOperationExample(subscriptionManager, messageDispatcher, logger);

            // 示例4: 统计信息
            await StatisticsExample(subscriptionManager, messageDispatcher, logger);

            logger.LogInformation("=== 示例执行完成 ===");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "示例执行失败");
        }
        finally
        {
            await serviceProvider.DisposeAsync();
        }
    }

    /// <summary>
    /// 基本订阅和消息分发示例
    /// </summary>
    private static async Task BasicSubscriptionExample(ITopicSubscriptionManager subscriptionManager, 
        IMessageDispatcher messageDispatcher, ILogger logger)
    {
        logger.LogInformation("\n--- 基本订阅和消息分发示例 ---");

        // 创建模拟客户端
        var client1 = CreateMockClient("client1");
        var client2 = CreateMockClient("client2");

        // 创建订阅
        var subscription1 = new MqttSubscription
        {
            TopicFilter = "sensors/temperature",
            QoSLevel = MqttQoSLevel.AtLeastOnce
        };

        var subscription2 = new MqttSubscription
        {
            TopicFilter = "sensors/humidity",
            QoSLevel = MqttQoSLevel.ExactlyOnce
        };

        // 执行订阅
        var result1 = await subscriptionManager.SubscribeAsync(client1, subscription1);
        var result2 = await subscriptionManager.SubscribeAsync(client2, subscription2);

        logger.LogInformation("订阅结果: {Topic1} -> {Success1}, {Topic2} -> {Success2}",
            result1.TopicFilter, result1.IsSuccess, result2.TopicFilter, result2.IsSuccess);

        // 发布消息
        var publishPacket = MqttPublishPacket.Create("sensors/temperature", 
            System.Text.Encoding.UTF8.GetBytes("25.5°C"), MqttQoSLevel.AtLeastOnce);

        var dispatchResult = await messageDispatcher.DispatchAsync(publishPacket);
        logger.LogInformation("消息分发结果: {Success}, 匹配订阅者: {Count}",
            dispatchResult.IsSuccess, dispatchResult.MatchedSubscribers);
    }

    /// <summary>
    /// 通配符订阅示例
    /// </summary>
    private static async Task WildcardSubscriptionExample(ITopicSubscriptionManager subscriptionManager, 
        IMessageDispatcher messageDispatcher, ILogger logger)
    {
        logger.LogInformation("\n--- 通配符订阅示例 ---");

        var client = CreateMockClient("wildcard_client");

        // 单级通配符订阅
        var singleLevelSubscription = new MqttSubscription
        {
            TopicFilter = "sensors/+/temperature",
            QoSLevel = MqttQoSLevel.AtMostOnce
        };

        // 多级通配符订阅
        var multiLevelSubscription = new MqttSubscription
        {
            TopicFilter = "devices/#",
            QoSLevel = MqttQoSLevel.AtLeastOnce
        };

        await subscriptionManager.SubscribeAsync(client, singleLevelSubscription);
        await subscriptionManager.SubscribeAsync(client, multiLevelSubscription);

        // 测试匹配
        var testTopics = new[]
        {
            "sensors/room1/temperature",  // 匹配单级通配符
            "sensors/room2/humidity",     // 不匹配单级通配符
            "devices/sensor1/status",     // 匹配多级通配符
            "devices/gateway/config/network" // 匹配多级通配符
        };

        foreach (var topic in testTopics)
        {
            var publishPacket = MqttPublishPacket.Create(topic, 
                System.Text.Encoding.UTF8.GetBytes("test data"), MqttQoSLevel.AtMostOnce);
            
            var result = await messageDispatcher.DispatchAsync(publishPacket);
            logger.LogInformation("主题 {Topic}: {MatchCount} 个匹配订阅者", topic, result.MatchedSubscribers);
        }
    }

    /// <summary>
    /// 批量操作示例
    /// </summary>
    private static async Task BatchOperationExample(ITopicSubscriptionManager subscriptionManager, 
        IMessageDispatcher messageDispatcher, ILogger logger)
    {
        logger.LogInformation("\n--- 批量操作示例 ---");

        var client = CreateMockClient("batch_client");

        // 批量订阅
        var subscriptions = new[]
        {
            new MqttSubscription { TopicFilter = "batch/topic1", QoSLevel = MqttQoSLevel.AtMostOnce },
            new MqttSubscription { TopicFilter = "batch/topic2", QoSLevel = MqttQoSLevel.AtLeastOnce },
            new MqttSubscription { TopicFilter = "batch/topic3", QoSLevel = MqttQoSLevel.ExactlyOnce }
        };

        var subscriptionResults = await subscriptionManager.SubscribeAsync(client, subscriptions);
        var successCount = subscriptionResults.Count(r => r.IsSuccess);
        logger.LogInformation("批量订阅结果: {SuccessCount}/{TotalCount} 成功", successCount, subscriptions.Length);

        // 批量消息分发
        var messages = new[]
        {
            MessageDispatchRequest.Create(MqttPublishPacket.Create("batch/topic1", 
                System.Text.Encoding.UTF8.GetBytes("message1"), MqttQoSLevel.AtMostOnce)),
            MessageDispatchRequest.Create(MqttPublishPacket.Create("batch/topic2", 
                System.Text.Encoding.UTF8.GetBytes("message2"), MqttQoSLevel.AtLeastOnce)),
            MessageDispatchRequest.Create(MqttPublishPacket.Create("batch/topic3", 
                System.Text.Encoding.UTF8.GetBytes("message3"), MqttQoSLevel.ExactlyOnce))
        };

        var dispatchResults = await messageDispatcher.DispatchBatchAsync(messages);
        var dispatchSuccessCount = dispatchResults.Count(r => r.IsSuccess);
        logger.LogInformation("批量分发结果: {SuccessCount}/{TotalCount} 成功", dispatchSuccessCount, messages.Length);
    }

    /// <summary>
    /// 统计信息示例
    /// </summary>
    private static async Task StatisticsExample(ITopicSubscriptionManager subscriptionManager, 
        IMessageDispatcher messageDispatcher, ILogger logger)
    {
        logger.LogInformation("\n--- 统计信息示例 ---");

        // 获取订阅统计
        var subscriptionStats = await subscriptionManager.GetStatisticsAsync();
        logger.LogInformation("订阅统计: 总订阅数={TotalSubs}, 活跃客户端={ActiveClients}, 主题数={TopicCount}",
            subscriptionStats.TotalSubscriptions, subscriptionStats.ActiveClients, subscriptionStats.TopicCount);

        // 获取分发统计
        var dispatchStats = await messageDispatcher.GetStatisticsAsync();
        logger.LogInformation("分发统计: 总消息数={TotalMsgs}, 成功率={SuccessRate:F1}%, 平均延迟={AvgLatency:F1}ms",
            dispatchStats.TotalMessages, dispatchStats.SuccessRate, dispatchStats.AverageDispatchLatency);
    }

    /// <summary>
    /// 创建模拟客户端
    /// </summary>
    private static IMqttClient CreateMockClient(string clientId)
    {
        // 注意：这是一个简化的模拟实现，实际使用中应该使用真实的客户端实例
        return new MockMqttClient(clientId);
    }
}

/// <summary>
/// 模拟MQTT客户端（仅用于示例）
/// </summary>
internal class MockMqttClient : IMqttClient
{
    public string ClientId { get; }
    public IClientConnection Connection => throw new NotImplementedException();
    public MqttProtocolVersion ProtocolVersion => MqttProtocolVersion.Version311;
    public bool IsAuthenticated => true;
    public bool CleanSession => true;
    public ushort KeepAliveInterval => 60;
    public DateTime ConnectedAt => DateTime.UtcNow;
    public DateTime LastActivity { get; private set; } = DateTime.UtcNow;
    public string? Username => null;
    public MqttWillMessage? WillMessage => null;
    public MqttClientState State => MqttClientState.Authenticated;
    public IDictionary<string, object> Properties => new Dictionary<string, object>();

    public event EventHandler<MqttPacketReceivedEventArgs>? PacketReceived;
    public event EventHandler<MqttClientStateChangedEventArgs>? StateChanged;

    public MockMqttClient(string clientId)
    {
        ClientId = clientId;
    }

    public Task SendPacketAsync(IMqttPacket packet, CancellationToken cancellationToken = default)
    {
        // 模拟发送数据包
        return Task.CompletedTask;
    }

    public Task HandlePacketAsync(IMqttPacket packet, CancellationToken cancellationToken = default)
    {
        return Task.CompletedTask;
    }

    public Task DisconnectAsync(DisconnectionReason reason = DisconnectionReason.ServerDisconnected, CancellationToken cancellationToken = default)
    {
        return Task.CompletedTask;
    }

    public void UpdateLastActivity()
    {
        LastActivity = DateTime.UtcNow;
    }

    public bool IsTimeout()
    {
        return false;
    }

    public MqttClientStatistics GetStatistics()
    {
        return new MqttClientStatistics { ClientId = ClientId };
    }

    public void Dispose()
    {
        // 清理资源
    }
}
