# MQTT Broker Windows 11 原生部署安装脚本
# 适用于个人开发环境的一键安装方案

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("Development", "Testing", "Production")]
    [string]$Environment = "Development",
    
    [Parameter(Mandatory=$false)]
    [string]$InstallPath = "C:\MqttBroker",
    
    [Parameter(Mandatory=$false)]
    [switch]$InstallRedis,
    
    [Parameter(Mandatory=$false)]
    [switch]$InstallPostgreSQL,
    
    [Parameter(Mandatory=$false)]
    [switch]$UseWindowsService,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipDependencies,
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose
)

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "此脚本需要管理员权限运行。请以管理员身份运行 PowerShell。"
    exit 1
}

# 日志函数
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warn {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Debug {
    param([string]$Message)
    if ($Verbose) {
        Write-Host "[DEBUG] $Message" -ForegroundColor Blue
    }
}

# 检查 .NET 8 运行时
function Test-DotNetRuntime {
    Write-Info "检查 .NET 8 运行时..."
    
    try {
        $dotnetVersion = dotnet --version
        if ($dotnetVersion -like "8.*") {
            Write-Info ".NET 8 运行时已安装: $dotnetVersion"
            return $true
        }
    }
    catch {
        Write-Debug "dotnet 命令未找到"
    }
    
    Write-Warn ".NET 8 运行时未安装或版本不正确"
    return $false
}

# 安装 .NET 8 运行时
function Install-DotNetRuntime {
    Write-Info "下载并安装 .NET 8 运行时..."
    
    $downloadUrl = "https://download.microsoft.com/download/6/0/f/60fc8c9b-2e8a-4d6e-b5b7-b1e5b5e5e5e5/dotnet-runtime-8.0.0-win-x64.exe"
    $installerPath = "$env:TEMP\dotnet-runtime-8.0-win-x64.exe"
    
    try {
        # 下载安装程序
        Invoke-WebRequest -Uri $downloadUrl -OutFile $installerPath -UseBasicParsing
        
        # 静默安装
        Start-Process -FilePath $installerPath -ArgumentList "/quiet" -Wait
        
        # 验证安装
        if (Test-DotNetRuntime) {
            Write-Info ".NET 8 运行时安装成功"
            Remove-Item $installerPath -Force
        } else {
            throw "安装验证失败"
        }
    }
    catch {
        Write-Error "安装 .NET 8 运行时失败: $_"
        exit 1
    }
}

# 安装 Redis (可选)
function Install-Redis {
    if (-not $InstallRedis) {
        Write-Info "跳过 Redis 安装"
        return
    }
    
    Write-Info "安装 Redis..."
    
    # 检查是否已安装 Chocolatey
    if (-not (Get-Command choco -ErrorAction SilentlyContinue)) {
        Write-Info "安装 Chocolatey 包管理器..."
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    }
    
    # 安装 Redis
    choco install redis-64 -y
    
    # 启动 Redis 服务
    Start-Service redis
    Set-Service redis -StartupType Automatic
    
    Write-Info "Redis 安装并启动成功"
}

# 安装 PostgreSQL (可选)
function Install-PostgreSQL {
    if (-not $InstallPostgreSQL) {
        Write-Info "跳过 PostgreSQL 安装，将使用 SQLite"
        return
    }
    
    Write-Info "安装 PostgreSQL..."
    
    # 使用 Chocolatey 安装 PostgreSQL
    choco install postgresql -y --params '/Password:**********'
    
    # 等待服务启动
    Start-Sleep -Seconds 10
    
    # 创建数据库和用户
    $env:PGPASSWORD = "**********"
    & "C:\Program Files\PostgreSQL\15\bin\psql.exe" -U postgres -c "CREATE DATABASE mqtt_broker;"
    & "C:\Program Files\PostgreSQL\15\bin\psql.exe" -U postgres -c "CREATE USER mqtt_user WITH PASSWORD '**********';"
    & "C:\Program Files\PostgreSQL\15\bin\psql.exe" -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE mqtt_broker TO mqtt_user;"
    
    Write-Info "PostgreSQL 安装并配置成功"
}

# 创建安装目录
function New-InstallDirectory {
    Write-Info "创建安装目录: $InstallPath"
    
    if (Test-Path $InstallPath) {
        Write-Warn "安装目录已存在，将清理旧文件"
        Remove-Item "$InstallPath\*" -Recurse -Force -ErrorAction SilentlyContinue
    } else {
        New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null
    }
    
    # 创建子目录
    $subDirs = @("data", "logs", "config", "scripts", "backup")
    foreach ($dir in $subDirs) {
        New-Item -ItemType Directory -Path "$InstallPath\$dir" -Force | Out-Null
    }
    
    Write-Info "目录结构创建完成"
}

# 构建和发布应用程序
function Publish-Application {
    Write-Info "构建和发布 MQTT Broker 应用程序..."
    
    $projectRoot = Split-Path -Parent (Split-Path -Parent $PSScriptRoot)
    Set-Location $projectRoot
    
    # 清理之前的构建
    dotnet clean --configuration Release
    
    # 还原依赖
    dotnet restore
    
    # 发布应用程序
    dotnet publish src/MqttBroker.Host -c Release -o "$InstallPath\app" --self-contained false --runtime win-x64
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "应用程序发布失败"
        exit 1
    }
    
    Write-Info "应用程序发布成功"
}

# 创建配置文件
function New-ConfigurationFiles {
    Write-Info "创建配置文件..."
    
    # 根据环境选择数据库配置
    if ($InstallPostgreSQL) {
        $connectionString = "Host=localhost;Database=mqtt_broker;Username=mqtt_user;Password=**********;Pooling=true;MinPoolSize=5;MaxPoolSize=20"
        $storageProvider = "PostgreSQL"
    } else {
        $connectionString = "Data Source=$InstallPath\data\mqtt_broker.db"
        $storageProvider = "SQLite"
    }
    
    # Redis 配置
    $redisConfig = if ($InstallRedis) {
        @"
      "Redis": {
        "ConnectionString": "localhost:6379",
        "Database": 0,
        "KeyPrefix": "mqtt:",
        "EnableCaching": true
      },
"@
    } else {
        ""
    }
    
    # 创建 appsettings.json
    $appSettings = @"
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information",
      "MqttBroker": "Information"
    },
    "Console": {
      "IncludeScopes": true,
      "TimestampFormat": "yyyy-MM-dd HH:mm:ss.fff "
    },
    "File": {
      "Path": "$($InstallPath.Replace('\', '\\'))\\logs\\mqtt-broker-.log",
      "RollingInterval": "Day",
      "RetainedFileCountLimit": 7,
      "FileSizeLimitBytes": 52428800
    }
  },
  "MqttBroker": {
    "Network": {
      "Tcp": {
        "Enabled": true,
        "Port": 1883,
        "Address": "127.0.0.1",
        "Backlog": 100,
        "NoDelay": true
      },
      "Tls": {
        "Enabled": false,
        "Port": 8883,
        "Address": "127.0.0.1"
      },
      "WebSocket": {
        "Enabled": true,
        "Port": 8080,
        "Path": "/mqtt"
      },
      "Connection": {
        "MaxConnections": 1000,
        "ConnectionTimeout": 30,
        "KeepAliveTimeout": 60,
        "ReceiveBufferSize": 8192,
        "SendBufferSize": 8192,
        "EnableRateLimit": true,
        "MaxConnectionsPerSecond": 50
      },
      "Performance": {
        "UseMemoryPool": true,
        "MaxPooledBufferSize": 65536,
        "EnableZeroCopy": true,
        "BatchSize": 50
      }
    },
    "Storage": {
      "Provider": "$storageProvider",
      "ConnectionString": "$connectionString",
      "EnablePersistence": true$redisConfig
    },
    "Security": {
      "AllowAnonymous": true,
      "RequireAuthentication": false,
      "EnableTls": false
    },
    "Performance": {
      "MaxMessageSize": 1048576,
      "MaxTopicLength": 1024,
      "MaxClientIdLength": 128
    },
    "Metrics": {
      "EnableMetrics": true,
      "CollectionIntervalMs": 10000,
      "EnableDetailedMetrics": false,
      "Export": {
        "EnableFileExport": true,
        "FileExportPath": "$($InstallPath.Replace('\', '\\'))\\logs\\metrics",
        "FileExportInterval": "00:05:00"
      }
    },
    "HealthChecks": {
      "Enabled": true,
      "Port": 9090,
      "Path": "/health"
    }
  }
}
"@
    
    $appSettings | Out-File -FilePath "$InstallPath\app\appsettings.json" -Encoding UTF8
    
    Write-Info "配置文件创建完成"
}

# 主安装函数
function Install-MqttBroker {
    Write-Info "开始安装 MQTT Broker (环境: $Environment)"
    
    # 检查和安装依赖
    if (-not $SkipDependencies) {
        if (-not (Test-DotNetRuntime)) {
            Install-DotNetRuntime
        }
        
        Install-Redis
        Install-PostgreSQL
    }
    
    # 创建安装目录
    New-InstallDirectory
    
    # 构建和发布应用程序
    Publish-Application
    
    # 创建配置文件
    New-ConfigurationFiles
    
    Write-Info "MQTT Broker 安装完成！"
    Write-Info "安装路径: $InstallPath"
    Write-Info "配置文件: $InstallPath\app\appsettings.json"
    Write-Info ""
    Write-Info "下一步："
    Write-Info "1. 运行: .\deployment\windows\start.ps1 启动服务"
    Write-Info "2. 访问: http://localhost:9090/health 检查健康状态"
    Write-Info "3. MQTT 连接: localhost:1883"
}

# 执行安装
try {
    Install-MqttBroker
}
catch {
    Write-Error "安装过程中发生错误: $_"
    exit 1
}
