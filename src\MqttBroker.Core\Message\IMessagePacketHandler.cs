using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;

namespace MqttBroker.Core.Message;

/// <summary>
/// 消息数据包处理器接口
/// </summary>
public interface IMessagePacketHandler
{
    /// <summary>
    /// 支持的数据包类型
    /// </summary>
    MqttPacketType PacketType { get; }

    /// <summary>
    /// 处理数据包
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="packet">数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    Task HandleAsync(IClientConnection connection, IMqttPacket packet, CancellationToken cancellationToken = default);
}

/// <summary>
/// 客户端连接接口（简化版本）
/// </summary>
public interface IClientConnection
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    string? ClientId { get; }

    /// <summary>
    /// 发送数据包
    /// </summary>
    /// <param name="packet">数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    Task SendPacketAsync(IMqttPacket packet, CancellationToken cancellationToken = default);
}
