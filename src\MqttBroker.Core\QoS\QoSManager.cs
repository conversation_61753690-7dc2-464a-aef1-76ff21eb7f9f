using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Network;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace MqttBroker.Core.QoS;

/// <summary>
/// QoS 管理器实现，负责协调所有 QoS 级别的处理
/// </summary>
public class QoSManager : IQoSManager, IDisposable
{
    private readonly ILogger<QoSManager> _logger;
    private readonly QoSConfiguration _configuration;
    private readonly IMessageAcknowledgmentService _acknowledgmentService;
    private readonly IMessageRetransmissionService _retransmissionService;
    private readonly IMessageDeduplicationService _deduplicationService;

    private readonly ConcurrentDictionary<string, object> _clientLocks = new();
    private readonly Timer? _statisticsTimer;
    private readonly Timer? _performanceTimer;

    // 统计信息
    private long _totalQoS1MessagesProcessed;
    private long _totalQoS2MessagesProcessed;
    private long _totalProcessingTime;
    private long _totalProcessedMessages;

    private bool _isStarted;
    private bool _disposed;

    /// <summary>
    /// 初始化 QoS 管理器
    /// </summary>
    public QoSManager(
        ILogger<QoSManager> logger,
        IOptions<QoSConfiguration> configuration,
        IMessageAcknowledgmentService acknowledgmentService,
        IMessageRetransmissionService retransmissionService,
        IMessageDeduplicationService deduplicationService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configuration = configuration?.Value ?? throw new ArgumentNullException(nameof(configuration));
        _acknowledgmentService = acknowledgmentService ?? throw new ArgumentNullException(nameof(acknowledgmentService));
        _retransmissionService = retransmissionService ?? throw new ArgumentNullException(nameof(retransmissionService));
        _deduplicationService = deduplicationService ?? throw new ArgumentNullException(nameof(deduplicationService));

        // 验证配置
        _configuration.Validate();

        // 初始化定时器
        if (_configuration.Manager.EnableStatistics)
        {
            _statisticsTimer = new Timer(LogStatistics, null, 
                TimeSpan.FromMilliseconds(_configuration.Manager.StatisticsUpdateIntervalMs),
                TimeSpan.FromMilliseconds(_configuration.Manager.StatisticsUpdateIntervalMs));
        }

        if (_configuration.Manager.EnablePerformanceMonitoring)
        {
            _performanceTimer = new Timer(MonitorPerformance, null,
                TimeSpan.FromMilliseconds(_configuration.Performance.PerformanceMonitoringIntervalMs),
                TimeSpan.FromMilliseconds(_configuration.Performance.PerformanceMonitoringIntervalMs));
        }

        _logger.LogInformation("QoS Manager initialized with configuration: MaxQoS={MaxQoS}, EnableRetransmission={EnableRetransmission}, EnableDeduplication={EnableDeduplication}",
            _configuration.Manager.MaxSupportedQoSLevel,
            _configuration.Retransmission.EnableRetransmission,
            _configuration.Deduplication.EnableDeduplication);
    }

    /// <summary>
    /// 启动 QoS 管理器
    /// </summary>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (_isStarted)
        {
            _logger.LogWarning("QoS Manager is already started");
            return;
        }

        try
        {
            _logger.LogInformation("Starting QoS Manager...");

            // 启动子服务
            await _retransmissionService.StartAsync(cancellationToken);
            await _deduplicationService.StartAsync(cancellationToken);

            _isStarted = true;
            _logger.LogInformation("QoS Manager started successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start QoS Manager");
            throw;
        }
    }

    /// <summary>
    /// 停止 QoS 管理器
    /// </summary>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (!_isStarted)
        {
            _logger.LogWarning("QoS Manager is not started");
            return;
        }

        try
        {
            _logger.LogInformation("Stopping QoS Manager...");

            // 停止子服务
            await _retransmissionService.StopAsync(cancellationToken);
            await _deduplicationService.StopAsync(cancellationToken);

            _isStarted = false;
            _logger.LogInformation("QoS Manager stopped successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to stop QoS Manager");
            throw;
        }
    }

    /// <summary>
    /// 处理发布消息的 QoS 流程
    /// </summary>
    public async Task<QoSProcessingResult> ProcessPublishAsync(
        IClientConnection connection, 
        MqttPublishPacket publishPacket, 
        CancellationToken cancellationToken = default)
    {
        if (!_configuration.Manager.EnableQoSProcessing)
        {
            return QoSProcessingResult.Success(publishPacket.PacketIdentifier, connection.ClientId, publishPacket.QoSLevel);
        }

        var stopwatch = Stopwatch.StartNew();
        var clientId = connection.ClientId ?? "unknown";

        try
        {
            _logger.LogTrace("Processing PUBLISH packet from client {ClientId}, MessageId: {MessageId}, QoS: {QoS}, Topic: {Topic}",
                clientId, publishPacket.PacketIdentifier, publishPacket.QoSLevel, publishPacket.Topic);

            // 检查 QoS 级别是否支持
            if (publishPacket.QoSLevel > _configuration.Manager.MaxSupportedQoSLevel)
            {
                var errorMsg = $"QoS level {publishPacket.QoSLevel} is not supported. Maximum supported level is {_configuration.Manager.MaxSupportedQoSLevel}";
                _logger.LogWarning(errorMsg + " for client {ClientId}", clientId);
                return QoSProcessingResult.Failure(errorMsg, publishPacket.PacketIdentifier, clientId);
            }

            var result = publishPacket.QoSLevel switch
            {
                MqttQoSLevel.AtMostOnce => await ProcessQoS0PublishAsync(connection, publishPacket, cancellationToken),
                MqttQoSLevel.AtLeastOnce => await ProcessQoS1PublishAsync(connection, publishPacket, cancellationToken),
                MqttQoSLevel.ExactlyOnce => await ProcessQoS2PublishAsync(connection, publishPacket, cancellationToken),
                _ => QoSProcessingResult.Failure($"Unknown QoS level: {publishPacket.QoSLevel}", publishPacket.PacketIdentifier, clientId)
            };

            stopwatch.Stop();
            result.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;

            // 更新统计信息
            UpdateProcessingStatistics(publishPacket.QoSLevel, stopwatch.ElapsedMilliseconds);

            if (_configuration.Logging.LogPerformanceMetrics && stopwatch.ElapsedMilliseconds > _configuration.Logging.LogThresholdMs)
            {
                _logger.LogInformation("QoS processing completed for client {ClientId}, MessageId: {MessageId}, QoS: {QoS}, Duration: {Duration}ms",
                    clientId, publishPacket.PacketIdentifier, publishPacket.QoSLevel, stopwatch.ElapsedMilliseconds);
            }

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error processing PUBLISH packet from client {ClientId}, MessageId: {MessageId}, QoS: {QoS}",
                clientId, publishPacket.PacketIdentifier, publishPacket.QoSLevel);

            return QoSProcessingResult.Failure($"Internal error: {ex.Message}", publishPacket.PacketIdentifier, clientId);
        }
    }

    /// <summary>
    /// 处理 QoS 0 发布消息
    /// </summary>
    private async Task<QoSProcessingResult> ProcessQoS0PublishAsync(
        IClientConnection connection, 
        MqttPublishPacket publishPacket, 
        CancellationToken cancellationToken)
    {
        // QoS 0 不需要确认，直接返回成功
        _logger.LogTrace("Processing QoS 0 PUBLISH from client {ClientId}, Topic: {Topic}",
            connection.ClientId, publishPacket.Topic);

        return QoSProcessingResult.Success(publishPacket.PacketIdentifier, connection.ClientId, MqttQoSLevel.AtMostOnce);
    }

    /// <summary>
    /// 处理 QoS 1 发布消息
    /// </summary>
    private async Task<QoSProcessingResult> ProcessQoS1PublishAsync(
        IClientConnection connection, 
        MqttPublishPacket publishPacket, 
        CancellationToken cancellationToken)
    {
        var clientId = connection.ClientId ?? "unknown";
        var messageId = publishPacket.PacketIdentifier ?? 0;

        _logger.LogTrace("Processing QoS 1 PUBLISH from client {ClientId}, MessageId: {MessageId}, Topic: {Topic}",
            clientId, messageId, publishPacket.Topic);

        try
        {
            // 发送 PUBACK 响应
            var pubackPacket = MqttPubAckPacket.Create(messageId, MqttReasonCode.Success);
            await connection.SendPacketAsync(pubackPacket, cancellationToken);

            _logger.LogTrace("Sent PUBACK to client {ClientId}, MessageId: {MessageId}", clientId, messageId);

            return QoSProcessingResult.Success(messageId, clientId, MqttQoSLevel.AtLeastOnce);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send PUBACK to client {ClientId}, MessageId: {MessageId}", clientId, messageId);
            return QoSProcessingResult.Failure($"Failed to send PUBACK: {ex.Message}", messageId, clientId);
        }
    }

    /// <summary>
    /// 处理 QoS 2 发布消息
    /// </summary>
    private async Task<QoSProcessingResult> ProcessQoS2PublishAsync(
        IClientConnection connection, 
        MqttPublishPacket publishPacket, 
        CancellationToken cancellationToken)
    {
        var clientId = connection.ClientId ?? "unknown";
        var messageId = publishPacket.PacketIdentifier ?? 0;

        _logger.LogTrace("Processing QoS 2 PUBLISH from client {ClientId}, MessageId: {MessageId}, Topic: {Topic}",
            clientId, messageId, publishPacket.Topic);

        try
        {
            // 检查是否为重复消息
            if (_configuration.Deduplication.EnableDeduplication)
            {
                var deduplicationResult = await _deduplicationService.CheckDuplicateAsync(clientId, messageId, publishPacket);
                if (deduplicationResult.IsDuplicate)
                {
                    _logger.LogTrace("Duplicate QoS 2 message detected from client {ClientId}, MessageId: {MessageId}, OriginalTime: {OriginalTime}",
                        clientId, messageId, deduplicationResult.OriginalProcessedTime);

                    // 重复消息，仍需发送 PUBREC 但不处理消息内容
                    var duplicatePubrecPacket = MqttPubRecPacket.Create(messageId, MqttReasonCode.Success);
                    await connection.SendPacketAsync(duplicatePubrecPacket, cancellationToken);

                    return QoSProcessingResult.Success(messageId, clientId, MqttQoSLevel.ExactlyOnce);
                }

                // 记录消息已处理
                await _deduplicationService.RecordProcessedMessageAsync(clientId, messageId, publishPacket,
                    TimeSpan.FromMilliseconds(_configuration.Deduplication.DefaultRecordExpirationMs));
            }

            // 发送 PUBREC 响应
            var pubrecPacket = MqttPubRecPacket.Create(messageId, MqttReasonCode.Success);
            await connection.SendPacketAsync(pubrecPacket, cancellationToken);

            _logger.LogTrace("Sent PUBREC to client {ClientId}, MessageId: {MessageId}", clientId, messageId);

            return QoSProcessingResult.Success(messageId, clientId, MqttQoSLevel.ExactlyOnce);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process QoS 2 PUBLISH from client {ClientId}, MessageId: {MessageId}", clientId, messageId);
            return QoSProcessingResult.Failure($"Failed to process QoS 2 PUBLISH: {ex.Message}", messageId, clientId);
        }
    }

    /// <summary>
    /// 处理 PUBACK 数据包
    /// </summary>
    public async Task<QoSProcessingResult> ProcessPubAckAsync(
        IClientConnection connection,
        MqttPubAckPacket pubackPacket,
        CancellationToken cancellationToken = default)
    {
        var clientId = connection.ClientId ?? "unknown";
        var messageId = pubackPacket.PacketIdentifier ?? 0;

        _logger.LogTrace("Processing PUBACK from client {ClientId}, MessageId: {MessageId}, ReasonCode: {ReasonCode}",
            clientId, messageId, pubackPacket.ReasonCode);

        try
        {
            // 确认 QoS 1 消息
            var result = await _acknowledgmentService.AcknowledgeQoS1MessageAsync(clientId, messageId);

            if (result.IsSuccess)
            {
                _logger.LogTrace("QoS 1 message acknowledged for client {ClientId}, MessageId: {MessageId}", clientId, messageId);
                return QoSProcessingResult.Success(messageId, clientId, MqttQoSLevel.AtLeastOnce);
            }
            else
            {
                _logger.LogWarning("Failed to acknowledge QoS 1 message for client {ClientId}, MessageId: {MessageId}: {Error}",
                    clientId, messageId, result.ErrorMessage);
                return QoSProcessingResult.Failure(result.ErrorMessage ?? "Unknown error", messageId, clientId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing PUBACK from client {ClientId}, MessageId: {MessageId}", clientId, messageId);
            return QoSProcessingResult.Failure($"Internal error: {ex.Message}", messageId, clientId);
        }
    }

    /// <summary>
    /// 处理 PUBREC 数据包
    /// </summary>
    public async Task<QoSProcessingResult> ProcessPubRecAsync(
        IClientConnection connection,
        MqttPubRecPacket pubrecPacket,
        CancellationToken cancellationToken = default)
    {
        var clientId = connection.ClientId ?? "unknown";
        var messageId = pubrecPacket.PacketIdentifier ?? 0;

        _logger.LogTrace("Processing PUBREC from client {ClientId}, MessageId: {MessageId}, ReasonCode: {ReasonCode}",
            clientId, messageId, pubrecPacket.ReasonCode);

        try
        {
            // 确认 QoS 2 消息第一阶段
            var result = await _acknowledgmentService.AcknowledgeQoS2Phase1Async(clientId, messageId);

            if (result.IsSuccess)
            {
                // 发送 PUBREL 响应
                var pubrelPacket = MqttPubRelPacket.Create(messageId, MqttReasonCode.Success);
                await connection.SendPacketAsync(pubrelPacket, cancellationToken);

                _logger.LogTrace("Sent PUBREL to client {ClientId}, MessageId: {MessageId}", clientId, messageId);
                return QoSProcessingResult.Success(messageId, clientId, MqttQoSLevel.ExactlyOnce);
            }
            else
            {
                _logger.LogWarning("Failed to acknowledge QoS 2 phase 1 for client {ClientId}, MessageId: {MessageId}: {Error}",
                    clientId, messageId, result.ErrorMessage);
                return QoSProcessingResult.Failure(result.ErrorMessage ?? "Unknown error", messageId, clientId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing PUBREC from client {ClientId}, MessageId: {MessageId}", clientId, messageId);
            return QoSProcessingResult.Failure($"Internal error: {ex.Message}", messageId, clientId);
        }
    }

    /// <summary>
    /// 处理 PUBREL 数据包
    /// </summary>
    public async Task<QoSProcessingResult> ProcessPubRelAsync(
        IClientConnection connection,
        MqttPubRelPacket pubrelPacket,
        CancellationToken cancellationToken = default)
    {
        var clientId = connection.ClientId ?? "unknown";
        var messageId = pubrelPacket.PacketIdentifier ?? 0;

        _logger.LogTrace("Processing PUBREL from client {ClientId}, MessageId: {MessageId}, ReasonCode: {ReasonCode}",
            clientId, messageId, pubrelPacket.ReasonCode);

        try
        {
            // 发送 PUBCOMP 响应
            var pubcompPacket = MqttPubCompPacket.Create(messageId, MqttReasonCode.Success);
            await connection.SendPacketAsync(pubcompPacket, cancellationToken);

            _logger.LogTrace("Sent PUBCOMP to client {ClientId}, MessageId: {MessageId}", clientId, messageId);
            return QoSProcessingResult.Success(messageId, clientId, MqttQoSLevel.ExactlyOnce);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing PUBREL from client {ClientId}, MessageId: {MessageId}", clientId, messageId);
            return QoSProcessingResult.Failure($"Internal error: {ex.Message}", messageId, clientId);
        }
    }

    /// <summary>
    /// 处理 PUBCOMP 数据包
    /// </summary>
    public async Task<QoSProcessingResult> ProcessPubCompAsync(
        IClientConnection connection,
        MqttPubCompPacket pubcompPacket,
        CancellationToken cancellationToken = default)
    {
        var clientId = connection.ClientId ?? "unknown";
        var messageId = pubcompPacket.PacketIdentifier ?? 0;

        _logger.LogTrace("Processing PUBCOMP from client {ClientId}, MessageId: {MessageId}, ReasonCode: {ReasonCode}",
            clientId, messageId, pubcompPacket.ReasonCode);

        try
        {
            // 确认 QoS 2 消息第二阶段
            var result = await _acknowledgmentService.AcknowledgeQoS2Phase2Async(clientId, messageId);

            if (result.IsSuccess)
            {
                _logger.LogTrace("QoS 2 message fully acknowledged for client {ClientId}, MessageId: {MessageId}", clientId, messageId);
                return QoSProcessingResult.Success(messageId, clientId, MqttQoSLevel.ExactlyOnce);
            }
            else
            {
                _logger.LogWarning("Failed to acknowledge QoS 2 phase 2 for client {ClientId}, MessageId: {MessageId}: {Error}",
                    clientId, messageId, result.ErrorMessage);
                return QoSProcessingResult.Failure(result.ErrorMessage ?? "Unknown error", messageId, clientId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing PUBCOMP from client {ClientId}, MessageId: {MessageId}", clientId, messageId);
            return QoSProcessingResult.Failure($"Internal error: {ex.Message}", messageId, clientId);
        }
    }

    /// <summary>
    /// 获取客户端的待确认消息统计
    /// </summary>
    public async Task<PendingMessageStatistics> GetPendingMessageStatisticsAsync(string clientId)
    {
        try
        {
            var pendingMessages = await _acknowledgmentService.GetClientPendingMessagesAsync(clientId);

            var statistics = new PendingMessageStatistics
            {
                ClientId = clientId,
                PendingQoS1Messages = pendingMessages.Count(m => m.QoSLevel == MqttQoSLevel.AtLeastOnce),
                PendingQoS2Messages = pendingMessages.Count(m => m.QoSLevel == MqttQoSLevel.ExactlyOnce),
                OldestPendingMessageTime = pendingMessages.Any() ? pendingMessages.Min(m => m.CreatedAt) : null,
                RetransmissionCounts = pendingMessages.ToDictionary(m => m.MessageId, m => m.RetransmissionCount)
            };

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pending message statistics for client {ClientId}", clientId);
            return new PendingMessageStatistics { ClientId = clientId };
        }
    }

    /// <summary>
    /// 获取全局 QoS 统计信息
    /// </summary>
    public async Task<GlobalQoSStatistics> GetGlobalStatisticsAsync()
    {
        try
        {
            var acknowledgmentStats = await _acknowledgmentService.GetStatisticsAsync();
            var retransmissionStats = await _retransmissionService.GetStatisticsAsync();
            var deduplicationStats = await _deduplicationService.GetStatisticsAsync();

            var statistics = new GlobalQoSStatistics
            {
                TotalQoS1MessagesProcessed = Interlocked.Read(ref _totalQoS1MessagesProcessed),
                TotalQoS2MessagesProcessed = Interlocked.Read(ref _totalQoS2MessagesProcessed),
                CurrentPendingQoS1Messages = acknowledgmentStats.PendingQoS1Messages,
                CurrentPendingQoS2Messages = acknowledgmentStats.PendingQoS2Messages,
                TotalRetransmissions = retransmissionStats.TotalRetransmissions,
                TotalDuplications = deduplicationStats.TotalDuplicateMessages,
                AverageProcessingLatency = CalculateAverageProcessingLatency(),
                ClientsWithPendingMessages = acknowledgmentStats.ClientsWithPendingMessages
            };

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting global QoS statistics");
            return new GlobalQoSStatistics();
        }
    }

    /// <summary>
    /// 清理客户端的所有待确认消息
    /// </summary>
    public async Task<int> CleanupClientMessagesAsync(string clientId)
    {
        try
        {
            _logger.LogInformation("Cleaning up messages for client {ClientId}", clientId);

            var acknowledgmentCleanup = await _acknowledgmentService.ClearClientPendingMessagesAsync(clientId);
            var deduplicationCleanup = await _deduplicationService.ClearClientProcessedMessagesAsync(clientId);

            var totalCleaned = acknowledgmentCleanup + deduplicationCleanup;

            _logger.LogInformation("Cleaned up {TotalCleaned} messages for client {ClientId} (Acknowledgment: {AckCleanup}, Deduplication: {DedupCleanup})",
                totalCleaned, clientId, acknowledgmentCleanup, deduplicationCleanup);

            return totalCleaned;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up messages for client {ClientId}", clientId);
            return 0;
        }
    }

    /// <summary>
    /// 更新处理统计信息
    /// </summary>
    private void UpdateProcessingStatistics(MqttQoSLevel qosLevel, long elapsedMilliseconds)
    {
        Interlocked.Increment(ref _totalProcessedMessages);
        Interlocked.Add(ref _totalProcessingTime, elapsedMilliseconds);

        switch (qosLevel)
        {
            case MqttQoSLevel.AtLeastOnce:
                Interlocked.Increment(ref _totalQoS1MessagesProcessed);
                break;
            case MqttQoSLevel.ExactlyOnce:
                Interlocked.Increment(ref _totalQoS2MessagesProcessed);
                break;
        }
    }

    /// <summary>
    /// 计算平均处理延迟
    /// </summary>
    private double CalculateAverageProcessingLatency()
    {
        var totalMessages = Interlocked.Read(ref _totalProcessedMessages);
        var totalTime = Interlocked.Read(ref _totalProcessingTime);

        return totalMessages > 0 ? (double)totalTime / totalMessages : 0;
    }

    /// <summary>
    /// 记录统计信息
    /// </summary>
    private async void LogStatistics(object? state)
    {
        if (!_configuration.Logging.LogStatistics)
            return;

        try
        {
            var statistics = await GetGlobalStatisticsAsync();

            _logger.LogInformation("QoS Statistics - QoS1: {QoS1}, QoS2: {QoS2}, PendingQoS1: {PendingQoS1}, PendingQoS2: {PendingQoS2}, Retransmissions: {Retransmissions}, Duplications: {Duplications}, AvgLatency: {AvgLatency}ms",
                statistics.TotalQoS1MessagesProcessed,
                statistics.TotalQoS2MessagesProcessed,
                statistics.CurrentPendingQoS1Messages,
                statistics.CurrentPendingQoS2Messages,
                statistics.TotalRetransmissions,
                statistics.TotalDuplications,
                statistics.AverageProcessingLatency);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging QoS statistics");
        }
    }

    /// <summary>
    /// 监控性能
    /// </summary>
    private async void MonitorPerformance(object? state)
    {
        if (!_configuration.Manager.EnablePerformanceMonitoring)
            return;

        try
        {
            var statistics = await GetGlobalStatisticsAsync();

            // 检查性能阈值
            if (statistics.AverageProcessingLatency > _configuration.Logging.LogThresholdMs)
            {
                _logger.LogWarning("QoS processing latency is high: {Latency}ms (threshold: {Threshold}ms)",
                    statistics.AverageProcessingLatency, _configuration.Logging.LogThresholdMs);
            }

            if (statistics.CurrentPendingQoS1Messages + statistics.CurrentPendingQoS2Messages > 1000)
            {
                _logger.LogWarning("High number of pending messages: QoS1={PendingQoS1}, QoS2={PendingQoS2}",
                    statistics.CurrentPendingQoS1Messages, statistics.CurrentPendingQoS2Messages);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error monitoring QoS performance");
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            _statisticsTimer?.Dispose();
            _performanceTimer?.Dispose();

            _disposed = true;
            _logger.LogInformation("QoS Manager disposed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing QoS Manager");
        }
    }
}
