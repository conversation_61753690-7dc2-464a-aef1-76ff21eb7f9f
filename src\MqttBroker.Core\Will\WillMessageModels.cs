using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;

namespace MqttBroker.Core.Will;

/// <summary>
/// 遗嘱消息注册信息
/// </summary>
public class WillMessageRegistration
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 遗嘱消息主题
    /// </summary>
    public string Topic { get; set; } = string.Empty;

    /// <summary>
    /// 遗嘱消息载荷
    /// </summary>
    public byte[] Payload { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// QoS 级别
    /// </summary>
    public MqttQoSLevel QoSLevel { get; set; }

    /// <summary>
    /// 保留标志
    /// </summary>
    public bool Retain { get; set; }

    /// <summary>
    /// MQTT 协议版本
    /// </summary>
    public MqttProtocolVersion ProtocolVersion { get; set; }

    /// <summary>
    /// 遗嘱消息属性（MQTT 5.0）
    /// </summary>
    public MqttProperties? Properties { get; set; }

    /// <summary>
    /// 注册时间
    /// </summary>
    public DateTime RegisteredAt { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdatedAt { get; set; }

    /// <summary>
    /// 过期时间（可选）
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// 是否已触发
    /// </summary>
    public bool IsTriggered { get; set; }

    /// <summary>
    /// 触发时间
    /// </summary>
    public DateTime? TriggeredAt { get; set; }

    /// <summary>
    /// 触发条件
    /// </summary>
    public WillMessageTriggerCondition? TriggerCondition { get; set; }

    /// <summary>
    /// 客户端用户名
    /// </summary>
    public string? Username { get; set; }

    /// <summary>
    /// 客户端连接时间
    /// </summary>
    public DateTime ConnectedAt { get; set; }

    /// <summary>
    /// 自定义属性
    /// </summary>
    public Dictionary<string, object> CustomProperties { get; set; } = new();

    /// <summary>
    /// 从 MQTT 遗嘱消息创建注册信息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="willMessage">MQTT 遗嘱消息</param>
    /// <param name="protocolVersion">协议版本</param>
    /// <param name="username">用户名</param>
    /// <param name="connectedAt">连接时间</param>
    /// <returns>遗嘱消息注册信息</returns>
    public static WillMessageRegistration FromMqttWillMessage(string clientId, MqttWillMessage willMessage, 
        MqttProtocolVersion protocolVersion, string? username = null, DateTime? connectedAt = null)
    {
        var now = DateTime.UtcNow;
        return new WillMessageRegistration
        {
            ClientId = clientId,
            Topic = willMessage.Topic,
            Payload = willMessage.Payload,
            QoSLevel = willMessage.QoSLevel,
            Retain = willMessage.Retain,
            ProtocolVersion = protocolVersion,
            Properties = willMessage.Properties,
            RegisteredAt = now,
            LastUpdatedAt = now,
            Username = username,
            ConnectedAt = connectedAt ?? now
        };
    }

    /// <summary>
    /// 转换为 MQTT 发布数据包
    /// </summary>
    /// <returns>MQTT 发布数据包</returns>
    public MqttPublishPacket ToPublishPacket()
    {
        // 为QoS > 0的消息生成数据包标识符
        ushort? packetIdentifier = null;
        if (QoSLevel > MqttQoSLevel.AtMostOnce)
        {
            packetIdentifier = (ushort)(DateTime.UtcNow.Ticks % ushort.MaxValue + 1);
        }

        var publishPacket = MqttPublishPacket.Create(Topic, Payload, QoSLevel, Retain, packetIdentifier);

        // 设置 MQTT 5.0 属性
        if (ProtocolVersion == MqttProtocolVersion.Version50 && Properties != null)
        {
            publishPacket.Properties = Properties;
        }

        return publishPacket;
    }

    /// <summary>
    /// 标记为已触发
    /// </summary>
    /// <param name="triggerCondition">触发条件</param>
    public void MarkAsTriggered(WillMessageTriggerCondition triggerCondition)
    {
        IsTriggered = true;
        TriggeredAt = DateTime.UtcNow;
        TriggerCondition = triggerCondition;
        LastUpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// 检查是否过期
    /// </summary>
    /// <returns>如果过期则返回true，否则返回false</returns>
    public bool IsExpired()
    {
        return ExpiresAt.HasValue && DateTime.UtcNow > ExpiresAt.Value;
    }

    /// <summary>
    /// 更新最后更新时间
    /// </summary>
    public void UpdateLastModified()
    {
        LastUpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// 克隆遗嘱消息注册信息
    /// </summary>
    /// <returns>克隆的遗嘱消息注册信息</returns>
    public WillMessageRegistration Clone()
    {
        return new WillMessageRegistration
        {
            ClientId = ClientId,
            Topic = Topic,
            Payload = (byte[])Payload.Clone(),
            QoSLevel = QoSLevel,
            Retain = Retain,
            ProtocolVersion = ProtocolVersion,
            Properties = Properties, // 注意：这里是浅拷贝
            RegisteredAt = RegisteredAt,
            LastUpdatedAt = LastUpdatedAt,
            ExpiresAt = ExpiresAt,
            IsTriggered = IsTriggered,
            TriggeredAt = TriggeredAt,
            TriggerCondition = TriggerCondition,
            Username = Username,
            ConnectedAt = ConnectedAt,
            CustomProperties = new Dictionary<string, object>(CustomProperties)
        };
    }
}

/// <summary>
/// 遗嘱消息清理结果
/// </summary>
public class WillMessageCleanupResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 清理的遗嘱消息数量
    /// </summary>
    public int CleanedCount { get; set; }

    /// <summary>
    /// 清理时间
    /// </summary>
    public DateTime CleanupTime { get; set; }

    /// <summary>
    /// 清理的客户端ID列表
    /// </summary>
    public IList<string> CleanedClientIds { get; set; } = new List<string>();

    /// <summary>
    /// 处理延迟（毫秒）
    /// </summary>
    public long ProcessingTimeMs { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    public static WillMessageCleanupResult Success(int cleanedCount, IList<string> cleanedClientIds, long processingTimeMs)
    {
        return new WillMessageCleanupResult
        {
            IsSuccess = true,
            CleanedCount = cleanedCount,
            CleanedClientIds = cleanedClientIds,
            CleanupTime = DateTime.UtcNow,
            ProcessingTimeMs = processingTimeMs
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    public static WillMessageCleanupResult Failure(string errorMessage)
    {
        return new WillMessageCleanupResult
        {
            IsSuccess = false,
            CleanupTime = DateTime.UtcNow,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// 遗嘱消息统计信息
/// </summary>
public class WillMessageStatistics
{
    /// <summary>
    /// 总遗嘱消息数量
    /// </summary>
    public int TotalWillMessages { get; set; }

    /// <summary>
    /// 活跃遗嘱消息数量
    /// </summary>
    public int ActiveWillMessages { get; set; }

    /// <summary>
    /// 已触发遗嘱消息数量
    /// </summary>
    public int TriggeredWillMessages { get; set; }

    /// <summary>
    /// 过期遗嘱消息数量
    /// </summary>
    public int ExpiredWillMessages { get; set; }

    /// <summary>
    /// 注册操作总数
    /// </summary>
    public long TotalRegistrations { get; set; }

    /// <summary>
    /// 触发操作总数
    /// </summary>
    public long TotalTriggers { get; set; }

    /// <summary>
    /// 清除操作总数
    /// </summary>
    public long TotalClears { get; set; }

    /// <summary>
    /// 失败操作总数
    /// </summary>
    public long TotalFailures { get; set; }

    /// <summary>
    /// 平均处理延迟（毫秒）
    /// </summary>
    public double AverageProcessingTimeMs { get; set; }

    /// <summary>
    /// 最后操作时间
    /// </summary>
    public DateTime LastOperationTime { get; set; }

    /// <summary>
    /// 管理器启动时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 按触发条件分组的统计
    /// </summary>
    public Dictionary<WillMessageTriggerCondition, int> TriggersByCondition { get; set; } = new();

    /// <summary>
    /// 按QoS级别分组的统计
    /// </summary>
    public Dictionary<MqttQoSLevel, int> WillMessagesByQoS { get; set; } = new();

    /// <summary>
    /// 按主题分组的统计
    /// </summary>
    public Dictionary<string, int> WillMessagesByTopic { get; set; } = new();

    /// <summary>
    /// 按协议版本分组的统计
    /// </summary>
    public Dictionary<MqttProtocolVersion, int> WillMessagesByProtocolVersion { get; set; } = new();
}
