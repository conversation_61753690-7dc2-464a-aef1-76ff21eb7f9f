using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace MqttBroker.Core.Session;

/// <summary>
/// 会话持久化服务注册扩展
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加会话持久化服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddSessionPersistence(this IServiceCollection services, IConfiguration configuration)
    {
        // 注册配置选项
        services.Configure<SessionManagerOptions>(configuration.GetSection(SessionManagerOptions.SectionName));

        // 注册核心服务
        services.AddScoped<ISessionPersistence, InMemorySessionPersistence>();
        services.AddScoped<ISessionManager, SessionManager>();

        return services;
    }

    /// <summary>
    /// 添加会话持久化服务（使用配置委托）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureOptions">配置委托</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddSessionPersistence(this IServiceCollection services, Action<SessionManagerOptions> configureOptions)
    {
        // 注册配置选项
        services.Configure(configureOptions);

        // 注册核心服务
        services.AddScoped<ISessionPersistence, InMemorySessionPersistence>();
        services.AddScoped<ISessionManager, SessionManager>();

        return services;
    }

    /// <summary>
    /// 添加会话持久化服务（使用默认配置）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddSessionPersistence(this IServiceCollection services)
    {
        return services.AddSessionPersistence(_ => { });
    }

    /// <summary>
    /// 添加高性能会话持久化服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddHighPerformanceSessionPersistence(this IServiceCollection services)
    {
        return services.AddSessionPersistence(options =>
        {
            var highPerfOptions = SessionManagerOptions.HighPerformance;
            options.SessionExpirationHours = highPerfOptions.SessionExpirationHours;
            options.CleanupIntervalMinutes = highPerfOptions.CleanupIntervalMinutes;
            options.MaxConcurrentSessions = highPerfOptions.MaxConcurrentSessions;
            options.MaxSubscriptionsPerClient = highPerfOptions.MaxSubscriptionsPerClient;
            options.MaxPendingMessagesPerClient = highPerfOptions.MaxPendingMessagesPerClient;
            options.SessionActivityTimeoutMinutes = highPerfOptions.SessionActivityTimeoutMinutes;
            options.StatisticsUpdateIntervalMinutes = highPerfOptions.StatisticsUpdateIntervalMinutes;
            options.BatchOperationSize = highPerfOptions.BatchOperationSize;
            options.EnableMemoryCache = highPerfOptions.EnableMemoryCache;
            options.MemoryCacheSizeMB = highPerfOptions.MemoryCacheSizeMB;
            options.CacheExpirationMinutes = highPerfOptions.CacheExpirationMinutes;
        });
    }

    /// <summary>
    /// 添加低资源会话持久化服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddLowResourceSessionPersistence(this IServiceCollection services)
    {
        return services.AddSessionPersistence(options =>
        {
            var lowResourceOptions = SessionManagerOptions.LowResource;
            options.SessionExpirationHours = lowResourceOptions.SessionExpirationHours;
            options.CleanupIntervalMinutes = lowResourceOptions.CleanupIntervalMinutes;
            options.MaxConcurrentSessions = lowResourceOptions.MaxConcurrentSessions;
            options.MaxSubscriptionsPerClient = lowResourceOptions.MaxSubscriptionsPerClient;
            options.MaxPendingMessagesPerClient = lowResourceOptions.MaxPendingMessagesPerClient;
            options.SessionActivityTimeoutMinutes = lowResourceOptions.SessionActivityTimeoutMinutes;
            options.StatisticsUpdateIntervalMinutes = lowResourceOptions.StatisticsUpdateIntervalMinutes;
            options.BatchOperationSize = lowResourceOptions.BatchOperationSize;
            options.EnableMemoryCache = lowResourceOptions.EnableMemoryCache;
            options.MemoryCacheSizeMB = lowResourceOptions.MemoryCacheSizeMB;
            options.CacheExpirationMinutes = lowResourceOptions.CacheExpirationMinutes;
        });
    }

    /// <summary>
    /// 验证会话持久化配置
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection ValidateSessionPersistenceConfiguration(this IServiceCollection services)
    {
        services.AddOptions<SessionManagerOptions>()
            .Validate(options =>
            {
                var result = options.Validate();
                if (!result.IsValid)
                {
                    throw new InvalidOperationException($"Invalid SessionManagerOptions: {result.ErrorMessage}");
                }
                return true;
            });

        return services;
    }

    /// <summary>
    /// 添加会话持久化后台服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddSessionPersistenceBackgroundServices(this IServiceCollection services)
    {
        services.AddHostedService<SessionCleanupBackgroundService>();
        return services;
    }

    /// <summary>
    /// 添加完整的会话持久化系统
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddCompleteSessionPersistence(this IServiceCollection services, IConfiguration configuration)
    {
        return services
            .AddSessionPersistence(configuration)
            .ValidateSessionPersistenceConfiguration()
            .AddSessionPersistenceBackgroundServices();
    }

    /// <summary>
    /// 添加完整的会话持久化系统（使用配置委托）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureOptions">配置委托</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddCompleteSessionPersistence(this IServiceCollection services, Action<SessionManagerOptions> configureOptions)
    {
        return services
            .AddSessionPersistence(configureOptions)
            .ValidateSessionPersistenceConfiguration()
            .AddSessionPersistenceBackgroundServices();
    }
}
