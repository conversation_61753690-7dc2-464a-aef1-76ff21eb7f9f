@echo off
REM MQTT Broker Windows CMD 管理脚本
REM 提供启动、停止、重启、状态检查等功能

setlocal enabledelayedexpansion

REM 配置变量
set "INSTALL_PATH=C:\MqttBroker"
set "PROCESS_NAME=MqttBroker.Host.exe"
set "SERVICE_NAME=MqttBrokerService"

REM 颜色定义
set "COLOR_INFO=0A"
set "COLOR_WARN=0E"
set "COLOR_ERROR=0C"
set "COLOR_SUCCESS=02"

REM 解析命令行参数
set "ACTION=start"
if not "%~1"=="" set "ACTION=%~1"

REM 显示帮助
if /i "%ACTION%"=="help" goto show_help
if /i "%ACTION%"=="--help" goto show_help
if /i "%ACTION%"=="-h" goto show_help

REM 执行操作
if /i "%ACTION%"=="start" goto start_broker
if /i "%ACTION%"=="stop" goto stop_broker
if /i "%ACTION%"=="restart" goto restart_broker
if /i "%ACTION%"=="status" goto show_status
if /i "%ACTION%"=="install-service" goto install_service
if /i "%ACTION%"=="uninstall-service" goto uninstall_service
if /i "%ACTION%"=="logs" goto show_logs
if /i "%ACTION%"=="health" goto health_check

echo [错误] 未知操作: %ACTION%
goto show_help

:show_help
color %COLOR_INFO%
echo.
echo MQTT Broker Windows 管理工具
echo.
echo 用法: start.cmd [操作]
echo.
echo 操作:
echo   start              启动 MQTT Broker (默认)
echo   stop               停止 MQTT Broker
echo   restart            重启 MQTT Broker
echo   status             显示状态信息
echo   install-service    安装为 Windows 服务
echo   uninstall-service  卸载 Windows 服务
echo   logs               查看最新日志
echo   health             健康检查
echo   help               显示此帮助信息
echo.
echo 示例:
echo   start.cmd start
echo   start.cmd status
echo   start.cmd logs
echo.
goto end

:start_broker
color %COLOR_INFO%
echo [信息] 启动 MQTT Broker...

REM 检查是否已在运行
tasklist /fi "imagename eq %PROCESS_NAME%" | find /i "%PROCESS_NAME%" >nul
if %errorLevel% equ 0 (
    echo [警告] MQTT Broker 已在运行
    goto show_running_info
)

REM 检查应用程序是否存在
if not exist "%INSTALL_PATH%\app\%PROCESS_NAME%" (
    color %COLOR_ERROR%
    echo [错误] 应用程序未找到: %INSTALL_PATH%\app\%PROCESS_NAME%
    echo 请先运行 install.cmd 安装应用程序
    pause
    goto end
)

REM 启动应用程序
cd /d "%INSTALL_PATH%\app"
start "MQTT Broker" %PROCESS_NAME%

REM 等待启动
timeout /t 3 /nobreak >nul

REM 检查启动状态
tasklist /fi "imagename eq %PROCESS_NAME%" | find /i "%PROCESS_NAME%" >nul
if %errorLevel% equ 0 (
    color %COLOR_SUCCESS%
    echo [成功] MQTT Broker 启动成功
    goto show_running_info
) else (
    color %COLOR_ERROR%
    echo [错误] MQTT Broker 启动失败
    pause
    goto end
)

:show_running_info
echo.
echo 访问地址:
echo   MQTT TCP: localhost:1883
echo   WebSocket: ws://localhost:8080/mqtt
echo   健康检查: http://localhost:9090/health
echo   管理接口: http://localhost:9090
echo.
echo 管理命令:
echo   查看状态: start.cmd status
echo   查看日志: start.cmd logs
echo   健康检查: start.cmd health
echo   停止服务: start.cmd stop
echo.
pause
goto end

:stop_broker
color %COLOR_INFO%
echo [信息] 停止 MQTT Broker...

REM 检查是否在运行
tasklist /fi "imagename eq %PROCESS_NAME%" | find /i "%PROCESS_NAME%" >nul
if %errorLevel% neq 0 (
    echo [信息] MQTT Broker 未在运行
    pause
    goto end
)

REM 获取进程 ID
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq %PROCESS_NAME%" /fo table /nh') do set "PID=%%i"

REM 停止进程
taskkill /f /im %PROCESS_NAME% >nul 2>&1
if %errorLevel% equ 0 (
    color %COLOR_SUCCESS%
    echo [成功] MQTT Broker 已停止 (PID: %PID%)
) else (
    color %COLOR_ERROR%
    echo [错误] 停止 MQTT Broker 失败
)

pause
goto end

:restart_broker
echo [信息] 重启 MQTT Broker...
call :stop_broker_silent
timeout /t 2 /nobreak >nul
call :start_broker_silent
goto end

:stop_broker_silent
taskkill /f /im %PROCESS_NAME% >nul 2>&1
goto :eof

:start_broker_silent
cd /d "%INSTALL_PATH%\app"
start "MQTT Broker" %PROCESS_NAME%
timeout /t 3 /nobreak >nul
goto :eof

:show_status
color %COLOR_INFO%
echo ========================================
echo MQTT Broker 状态信息
echo ========================================
echo 检查时间: %date% %time%
echo.

REM 进程状态
echo [进程状态]
tasklist /fi "imagename eq %PROCESS_NAME%" | find /i "%PROCESS_NAME%" >nul
if %errorLevel% equ 0 (
    echo   状态: 运行中
    for /f "tokens=2,5" %%i in ('tasklist /fi "imagename eq %PROCESS_NAME%" /fo table /nh') do (
        echo   进程 ID: %%i
        echo   内存使用: %%j
    )
    
    REM 运行时长
    for /f "tokens=*" %%i in ('wmic process where "name='%PROCESS_NAME%'" get CreationDate /value ^| find "="') do (
        set "%%i"
    )
    echo   启动时间: !CreationDate:~0,4!-!CreationDate:~4,2!-!CreationDate:~6,2! !CreationDate:~8,2!:!CreationDate:~10,2!:!CreationDate:~12,2!
) else (
    echo   状态: 未运行
)

echo.

REM 服务状态
echo [Windows 服务状态]
sc query %SERVICE_NAME% >nul 2>&1
if %errorLevel% equ 0 (
    for /f "tokens=3" %%i in ('sc query %SERVICE_NAME% ^| find "STATE"') do set "SERVICE_STATE=%%i"
    echo   服务状态: !SERVICE_STATE!
    for /f "tokens=3" %%i in ('sc qc %SERVICE_NAME% ^| find "START_TYPE"') do set "START_TYPE=%%i"
    echo   启动类型: !START_TYPE!
) else (
    echo   服务状态: 未安装
)

echo.

REM 端口状态
echo [端口状态]
set "PORTS=1883 8080 9090"
for %%p in (%PORTS%) do (
    netstat -an | find ":%%p " | find "LISTENING" >nul
    if !errorLevel! equ 0 (
        echo   端口 %%p: 监听中
    ) else (
        echo   端口 %%p: 未监听
    )
)

echo.

REM 健康检查
echo [健康检查]
tasklist /fi "imagename eq %PROCESS_NAME%" | find /i "%PROCESS_NAME%" >nul
if %errorLevel% equ 0 (
    powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:9090/health' -UseBasicParsing -TimeoutSec 5; Write-Host '  健康状态: HTTP' $response.StatusCode } catch { Write-Host '  健康状态: 无响应' }" 2>nul
) else (
    echo   健康状态: 服务未运行
)

echo.
echo ========================================
pause
goto end

:install_service
REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    color %COLOR_ERROR%
    echo [错误] 安装服务需要管理员权限
    echo 请以管理员身份运行命令提示符
    pause
    goto end
)

color %COLOR_INFO%
echo [信息] 安装 MQTT Broker Windows 服务...

REM 检查应用程序是否存在
if not exist "%INSTALL_PATH%\app\%PROCESS_NAME%" (
    color %COLOR_ERROR%
    echo [错误] 应用程序未找到
    echo 请先运行 install.cmd 安装应用程序
    pause
    goto end
)

REM 检查服务是否已安装
sc query %SERVICE_NAME% >nul 2>&1
if %errorLevel% equ 0 (
    echo [警告] Windows 服务已安装
    pause
    goto end
)

REM 创建服务
sc create %SERVICE_NAME% binPath= "%INSTALL_PATH%\app\%PROCESS_NAME%" DisplayName= "MQTT Broker Service" start= auto >nul 2>&1
if %errorLevel% equ 0 (
    sc description %SERVICE_NAME% "High-performance MQTT Broker for IoT applications" >nul 2>&1
    color %COLOR_SUCCESS%
    echo [成功] Windows 服务安装成功
    echo 服务名称: %SERVICE_NAME%
    echo 显示名称: MQTT Broker Service
    echo 启动类型: 自动
) else (
    color %COLOR_ERROR%
    echo [错误] 服务安装失败
)

pause
goto end

:uninstall_service
REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    color %COLOR_ERROR%
    echo [错误] 卸载服务需要管理员权限
    echo 请以管理员身份运行命令提示符
    pause
    goto end
)

color %COLOR_INFO%
echo [信息] 卸载 MQTT Broker Windows 服务...

REM 检查服务是否存在
sc query %SERVICE_NAME% >nul 2>&1
if %errorLevel% neq 0 (
    echo [信息] Windows 服务未安装
    pause
    goto end
)

REM 停止服务
sc query %SERVICE_NAME% | find "RUNNING" >nul
if %errorLevel% equ 0 (
    echo [信息] 停止服务...
    sc stop %SERVICE_NAME% >nul 2>&1
    timeout /t 3 /nobreak >nul
)

REM 删除服务
sc delete %SERVICE_NAME% >nul 2>&1
if %errorLevel% equ 0 (
    color %COLOR_SUCCESS%
    echo [成功] Windows 服务卸载成功
) else (
    color %COLOR_ERROR%
    echo [错误] 服务卸载失败
)

pause
goto end

:show_logs
color %COLOR_INFO%
echo [信息] 显示最新日志...

REM 查找最新的日志文件
set "LOG_FILE="
for /f "delims=" %%i in ('dir "%INSTALL_PATH%\logs\*.log" /b /o-d 2^>nul') do (
    if not defined LOG_FILE set "LOG_FILE=%%i"
)

if not defined LOG_FILE (
    echo [警告] 未找到日志文件
    pause
    goto end
)

echo 日志文件: %LOG_FILE%
echo ========================================

REM 显示最后 20 行
powershell -Command "Get-Content '%INSTALL_PATH%\logs\%LOG_FILE%' -Tail 20" 2>nul
if %errorLevel% neq 0 (
    REM 如果 PowerShell 不可用，使用 more 命令
    more +0 "%INSTALL_PATH%\logs\%LOG_FILE%"
)

echo ========================================
pause
goto end

:health_check
color %COLOR_INFO%
echo [信息] 执行健康检查...

REM 检查进程是否运行
tasklist /fi "imagename eq %PROCESS_NAME%" | find /i "%PROCESS_NAME%" >nul
if %errorLevel% neq 0 (
    color %COLOR_ERROR%
    echo [错误] MQTT Broker 未运行
    pause
    goto end
)

REM 执行健康检查
echo 正在检查健康状态...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:9090/health' -TimeoutSec 10; Write-Host '健康检查结果:'; Write-Host '  状态:' $response.status; if ($response.checks) { Write-Host '  详细检查:'; foreach ($check in $response.checks) { Write-Host '   ' $check.name ':' $check.status } } } catch { Write-Host '健康检查失败:' $_.Exception.Message }" 2>nul

if %errorLevel% neq 0 (
    echo [错误] 健康检查失败，可能的原因：
    echo   1. 服务正在启动中，请稍后重试
    echo   2. 端口 9090 被占用
    echo   3. 应用程序配置错误
)

pause
goto end

:end
endlocal
