using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Client;
using MqttBroker.Core.Network;
using MqttBroker.Core.Topic;
using MqttBroker.Core.Message;

namespace MqttBroker.Core;

/// <summary>
/// MQTT Broker 核心服务注册扩展
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加 MQTT Broker 核心服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddMqttBrokerCore(this IServiceCollection services)
    {
        // 注册 MQTT 协议解析器相关服务
        services.AddSingleton<IMqttPacketParser, MqttPacketParser>();
        services.AddSingleton<IMqttPacketSerializer, MqttPacketSerializer>();
        services.AddSingleton<IMqttPacketValidator, MqttPacketValidator>();
        services.AddSingleton<IMqttTopicMatcher, MqttTopicMatcher>();

        // 添加客户端连接管理服务
        services.AddMqttClientManagement();

        // 添加主题订阅系统服务
        services.AddTopicSubscriptionSystem();

        // 添加消息路由引擎服务
        services.AddMessageRoutingEngine();

        return services;
    }

    /// <summary>
    /// 添加 MQTT 客户端连接管理服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddMqttClientManagement(this IServiceCollection services)
    {
        // 注册客户端管理器
        services.TryAddSingleton<IMqttClientManager, MqttClientManager>();

        // 注册客户端认证器
        services.TryAddSingleton<IMqttClientAuthenticator, DefaultMqttClientAuthenticator>();

        // 注册连接池
        services.TryAddSingleton<IMqttConnectionPool, MqttConnectionPool>();

        // 注册数据包处理器
        services.TryAddTransient<IPacketHandler, MqttConnectPacketHandler>();
        services.TryAddTransient<IPacketHandler, MqttDisconnectPacketHandler>();

        // 注册配置选项
        services.AddOptions<MqttClientManagerOptions>();
        services.AddOptions<MqttClientAuthenticationOptions>();
        services.AddOptions<MqttConnectionPoolOptions>();
        services.AddOptions<MqttClientCleanupOptions>();

        // 注册后台清理服务
        services.AddHostedService<MqttClientCleanupService>();

        return services;
    }
}
