# MQTT Broker Windows 监控脚本
# 提供实时监控、性能分析和故障排查功能

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("dashboard", "performance", "connections", "messages", "logs", "alerts")]
    [string]$Mode = "dashboard",
    
    [Parameter(Mandatory=$false)]
    [int]$RefreshInterval = 5,
    
    [Parameter(Mandatory=$false)]
    [string]$InstallPath = "C:\MqttBroker",
    
    [Parameter(Mandatory=$false)]
    [switch]$Continuous,
    
    [Parameter(Mandatory=$false)]
    [switch]$Export
)

# 全局变量
$ProcessName = "MqttBroker.Host"
$HealthUrl = "http://localhost:9090/health"
$MetricsUrl = "http://localhost:9090/metrics"
$LogPath = "$InstallPath\logs"

# 颜色定义
$Colors = @{
    Good = "Green"
    Warning = "Yellow"
    Error = "Red"
    Info = "Cyan"
    Header = "Magenta"
}

# 获取系统信息
function Get-SystemInfo {
    $os = Get-CimInstance -ClassName Win32_OperatingSystem
    $cpu = Get-CimInstance -ClassName Win32_Processor
    $memory = Get-CimInstance -ClassName Win32_PhysicalMemory | Measure-Object -Property Capacity -Sum
    
    return @{
        OS = "$($os.Caption) $($os.Version)"
        CPU = $cpu[0].Name
        Cores = $cpu[0].NumberOfCores
        TotalMemory = [math]::Round($memory.Sum / 1GB, 2)
        Architecture = $os.OSArchitecture
    }
}

# 获取进程信息
function Get-ProcessInfo {
    $process = Get-Process -Name $ProcessName -ErrorAction SilentlyContinue
    
    if ($process) {
        return @{
            IsRunning = $true
            ProcessId = $process.Id
            StartTime = $process.StartTime
            CPUTime = $process.TotalProcessorTime
            WorkingSet = [math]::Round($process.WorkingSet64 / 1MB, 2)
            VirtualMemory = [math]::Round($process.VirtualMemorySize64 / 1MB, 2)
            Threads = $process.Threads.Count
            Handles = $process.HandleCount
        }
    } else {
        return @{
            IsRunning = $false
        }
    }
}

# 获取网络连接信息
function Get-NetworkInfo {
    $connections = @()
    $ports = @(1883, 8080, 9090)
    
    foreach ($port in $ports) {
        $tcpConnections = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
        if ($tcpConnections) {
            $listening = $tcpConnections | Where-Object { $_.State -eq "Listen" }
            $established = $tcpConnections | Where-Object { $_.State -eq "Established" }
            
            $connections += @{
                Port = $port
                IsListening = $listening.Count -gt 0
                EstablishedConnections = $established.Count
                TotalConnections = $tcpConnections.Count
            }
        } else {
            $connections += @{
                Port = $port
                IsListening = $false
                EstablishedConnections = 0
                TotalConnections = 0
            }
        }
    }
    
    return $connections
}

# 获取健康状态
function Get-HealthStatus {
    try {
        $response = Invoke-RestMethod -Uri $HealthUrl -TimeoutSec 5
        return @{
            IsHealthy = $response.status -eq "Healthy"
            Status = $response.status
            Checks = $response.checks
            ResponseTime = (Measure-Command { Invoke-WebRequest -Uri $HealthUrl -UseBasicParsing }).TotalMilliseconds
        }
    }
    catch {
        return @{
            IsHealthy = $false
            Status = "Unreachable"
            Checks = @()
            ResponseTime = -1
            Error = $_.Exception.Message
        }
    }
}

# 获取性能指标
function Get-PerformanceMetrics {
    $processInfo = Get-ProcessInfo
    if (-not $processInfo.IsRunning) {
        return $null
    }
    
    # 获取 CPU 使用率
    $cpuCounter = Get-Counter "\Process($ProcessName)\% Processor Time" -ErrorAction SilentlyContinue
    $cpuUsage = if ($cpuCounter) { [math]::Round($cpuCounter.CounterSamples[0].CookedValue, 2) } else { 0 }
    
    # 获取系统内存使用率
    $totalMemory = (Get-CimInstance -ClassName Win32_ComputerSystem).TotalPhysicalMemory
    $availableMemory = (Get-CimInstance -ClassName Win32_OperatingSystem).AvailablePhysicalMemory
    $memoryUsage = [math]::Round((($totalMemory - $availableMemory) / $totalMemory) * 100, 2)
    
    return @{
        CPUUsage = $cpuUsage
        MemoryUsage = $memoryUsage
        ProcessMemory = $processInfo.WorkingSet
        ProcessVirtualMemory = $processInfo.VirtualMemory
        ThreadCount = $processInfo.Threads
        HandleCount = $processInfo.Handles
        Uptime = if ($processInfo.StartTime) { (Get-Date) - $processInfo.StartTime } else { $null }
    }
}

# 显示仪表板
function Show-Dashboard {
    Clear-Host
    
    # 标题
    Write-Host "=" * 80 -ForegroundColor $Colors.Header
    Write-Host "MQTT Broker 监控仪表板" -ForegroundColor $Colors.Header
    Write-Host "刷新时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor $Colors.Info
    Write-Host "=" * 80 -ForegroundColor $Colors.Header
    Write-Host ""
    
    # 系统信息
    $systemInfo = Get-SystemInfo
    Write-Host "系统信息:" -ForegroundColor $Colors.Header
    Write-Host "  操作系统: $($systemInfo.OS)"
    Write-Host "  处理器: $($systemInfo.CPU)"
    Write-Host "  核心数: $($systemInfo.Cores)"
    Write-Host "  总内存: $($systemInfo.TotalMemory) GB"
    Write-Host ""
    
    # 进程状态
    $processInfo = Get-ProcessInfo
    Write-Host "进程状态:" -ForegroundColor $Colors.Header
    if ($processInfo.IsRunning) {
        Write-Host "  状态: " -NoNewline
        Write-Host "运行中" -ForegroundColor $Colors.Good
        Write-Host "  进程 ID: $($processInfo.ProcessId)"
        Write-Host "  启动时间: $($processInfo.StartTime)"
        Write-Host "  运行时长: $((Get-Date) - $processInfo.StartTime)"
        Write-Host "  内存使用: $($processInfo.WorkingSet) MB"
        Write-Host "  虚拟内存: $($processInfo.VirtualMemory) MB"
        Write-Host "  线程数: $($processInfo.Threads)"
        Write-Host "  句柄数: $($processInfo.Handles)"
    } else {
        Write-Host "  状态: " -NoNewline
        Write-Host "未运行" -ForegroundColor $Colors.Error
    }
    Write-Host ""
    
    # 网络状态
    $networkInfo = Get-NetworkInfo
    Write-Host "网络状态:" -ForegroundColor $Colors.Header
    foreach ($conn in $networkInfo) {
        $portName = switch ($conn.Port) {
            1883 { "MQTT" }
            8080 { "WebSocket" }
            9090 { "管理" }
            default { "端口 $($conn.Port)" }
        }
        
        Write-Host "  $portName ($($conn.Port)): " -NoNewline
        if ($conn.IsListening) {
            Write-Host "监听中" -ForegroundColor $Colors.Good -NoNewline
            Write-Host " ($($conn.EstablishedConnections) 个连接)"
        } else {
            Write-Host "未监听" -ForegroundColor $Colors.Error
        }
    }
    Write-Host ""
    
    # 健康状态
    $healthStatus = Get-HealthStatus
    Write-Host "健康状态:" -ForegroundColor $Colors.Header
    Write-Host "  总体状态: " -NoNewline
    if ($healthStatus.IsHealthy) {
        Write-Host $healthStatus.Status -ForegroundColor $Colors.Good
    } else {
        Write-Host $healthStatus.Status -ForegroundColor $Colors.Error
    }
    
    if ($healthStatus.ResponseTime -gt 0) {
        Write-Host "  响应时间: $([math]::Round($healthStatus.ResponseTime, 2)) ms"
    }
    
    if ($healthStatus.Checks) {
        Write-Host "  详细检查:"
        foreach ($check in $healthStatus.Checks) {
            Write-Host "    $($check.name): " -NoNewline
            if ($check.status -eq "Healthy") {
                Write-Host $check.status -ForegroundColor $Colors.Good
            } else {
                Write-Host $check.status -ForegroundColor $Colors.Error
            }
        }
    }
    
    if ($healthStatus.Error) {
        Write-Host "  错误: $($healthStatus.Error)" -ForegroundColor $Colors.Error
    }
    Write-Host ""
    
    # 性能指标
    if ($processInfo.IsRunning) {
        $perfMetrics = Get-PerformanceMetrics
        if ($perfMetrics) {
            Write-Host "性能指标:" -ForegroundColor $Colors.Header
            Write-Host "  CPU 使用率: $($perfMetrics.CPUUsage)%"
            Write-Host "  系统内存使用率: $($perfMetrics.MemoryUsage)%"
            Write-Host "  进程内存: $($perfMetrics.ProcessMemory) MB"
            Write-Host "  运行时长: $($perfMetrics.Uptime)"
            Write-Host ""
        }
    }
    
    # 操作提示
    Write-Host "操作:" -ForegroundColor $Colors.Header
    Write-Host "  按 Ctrl+C 退出监控"
    Write-Host "  按 R 刷新显示"
    Write-Host "  按 L 查看日志"
    Write-Host "  按 H 查看健康详情"
}

# 显示性能监控
function Show-Performance {
    $perfMetrics = Get-PerformanceMetrics
    if (-not $perfMetrics) {
        Write-Host "MQTT Broker 未运行，无法获取性能数据" -ForegroundColor $Colors.Error
        return
    }
    
    Clear-Host
    Write-Host "性能监控 - $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor $Colors.Header
    Write-Host "=" * 60 -ForegroundColor $Colors.Header
    
    # CPU 使用率图表
    Write-Host "CPU 使用率: $($perfMetrics.CPUUsage)%" -ForegroundColor $Colors.Info
    $cpuBar = "█" * [math]::Floor($perfMetrics.CPUUsage / 2)
    $cpuEmpty = "░" * (50 - [math]::Floor($perfMetrics.CPUUsage / 2))
    Write-Host "[$cpuBar$cpuEmpty] $($perfMetrics.CPUUsage)%"
    
    # 内存使用率图表
    Write-Host "内存使用率: $($perfMetrics.MemoryUsage)%" -ForegroundColor $Colors.Info
    $memBar = "█" * [math]::Floor($perfMetrics.MemoryUsage / 2)
    $memEmpty = "░" * (50 - [math]::Floor($perfMetrics.MemoryUsage / 2))
    Write-Host "[$memBar$memEmpty] $($perfMetrics.MemoryUsage)%"
    
    # 进程详细信息
    Write-Host ""
    Write-Host "进程详细信息:" -ForegroundColor $Colors.Info
    Write-Host "  物理内存: $($perfMetrics.ProcessMemory) MB"
    Write-Host "  虚拟内存: $($perfMetrics.ProcessVirtualMemory) MB"
    Write-Host "  线程数: $($perfMetrics.ThreadCount)"
    Write-Host "  句柄数: $($perfMetrics.HandleCount)"
    Write-Host "  运行时长: $($perfMetrics.Uptime)"
}

# 显示连接信息
function Show-Connections {
    $networkInfo = Get-NetworkInfo
    
    Clear-Host
    Write-Host "网络连接监控 - $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor $Colors.Header
    Write-Host "=" * 60 -ForegroundColor $Colors.Header
    
    foreach ($conn in $networkInfo) {
        $portName = switch ($conn.Port) {
            1883 { "MQTT TCP" }
            8080 { "WebSocket" }
            9090 { "管理接口" }
            default { "端口 $($conn.Port)" }
        }
        
        Write-Host ""
        Write-Host "$portName (端口 $($conn.Port)):" -ForegroundColor $Colors.Info
        Write-Host "  监听状态: " -NoNewline
        if ($conn.IsListening) {
            Write-Host "是" -ForegroundColor $Colors.Good
        } else {
            Write-Host "否" -ForegroundColor $Colors.Error
        }
        Write-Host "  活跃连接: $($conn.EstablishedConnections)"
        Write-Host "  总连接数: $($conn.TotalConnections)"
    }
}

# 显示最新日志
function Show-RecentLogs {
    $logFiles = Get-ChildItem -Path $LogPath -Filter "*.log" -ErrorAction SilentlyContinue | Sort-Object LastWriteTime -Descending
    
    if ($logFiles.Count -eq 0) {
        Write-Host "未找到日志文件" -ForegroundColor $Colors.Warning
        return
    }
    
    $latestLog = $logFiles[0]
    
    Clear-Host
    Write-Host "最新日志 - $($latestLog.Name)" -ForegroundColor $Colors.Header
    Write-Host "文件大小: $([math]::Round($latestLog.Length / 1KB, 2)) KB" -ForegroundColor $Colors.Info
    Write-Host "=" * 80 -ForegroundColor $Colors.Header
    
    # 显示最后 20 行
    $logContent = Get-Content -Path $latestLog.FullName -Tail 20
    foreach ($line in $logContent) {
        if ($line -match "\[ERR\]|\[ERROR\]") {
            Write-Host $line -ForegroundColor $Colors.Error
        } elseif ($line -match "\[WRN\]|\[WARN\]") {
            Write-Host $line -ForegroundColor $Colors.Warning
        } elseif ($line -match "\[INF\]|\[INFO\]") {
            Write-Host $line -ForegroundColor $Colors.Good
        } else {
            Write-Host $line
        }
    }
}

# 导出监控报告
function Export-MonitoringReport {
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $reportPath = "$InstallPath\logs\monitoring_report_$timestamp.txt"
    
    $report = @"
MQTT Broker 监控报告
生成时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
========================================

系统信息:
$(Get-SystemInfo | ConvertTo-Json -Depth 2)

进程信息:
$(Get-ProcessInfo | ConvertTo-Json -Depth 2)

网络信息:
$(Get-NetworkInfo | ConvertTo-Json -Depth 2)

健康状态:
$(Get-HealthStatus | ConvertTo-Json -Depth 2)

性能指标:
$(Get-PerformanceMetrics | ConvertTo-Json -Depth 2)
"@
    
    $report | Out-File -FilePath $reportPath -Encoding UTF8
    Write-Host "监控报告已导出到: $reportPath" -ForegroundColor $Colors.Good
}

# 主监控循环
function Start-Monitoring {
    do {
        switch ($Mode.ToLower()) {
            "dashboard" { Show-Dashboard }
            "performance" { Show-Performance }
            "connections" { Show-Connections }
            "logs" { Show-RecentLogs }
            default { Show-Dashboard }
        }
        
        if ($Export) {
            Export-MonitoringReport
        }
        
        if ($Continuous) {
            Start-Sleep -Seconds $RefreshInterval
        } else {
            break
        }
    } while ($Continuous)
}

# 主函数
function Main {
    Write-Host "MQTT Broker Windows 监控工具" -ForegroundColor $Colors.Header
    Write-Host "监控模式: $Mode" -ForegroundColor $Colors.Info
    
    if ($Continuous) {
        Write-Host "连续监控模式，刷新间隔: $RefreshInterval 秒" -ForegroundColor $Colors.Info
        Write-Host "按 Ctrl+C 退出" -ForegroundColor $Colors.Warning
    }
    
    Write-Host ""
    
    try {
        Start-Monitoring
    }
    catch {
        Write-Host "监控过程中发生错误: $_" -ForegroundColor $Colors.Error
    }
}

# 执行主函数
Main
