namespace MqttBroker.Metrics.Configuration;

/// <summary>
/// 性能监控配置选项
/// </summary>
public class MetricsOptions
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public const string SectionName = "Metrics";

    /// <summary>
    /// 是否启用性能监控
    /// </summary>
    public bool EnableMetrics { get; set; } = true;

    /// <summary>
    /// 指标收集间隔（毫秒）
    /// </summary>
    public int CollectionIntervalMs { get; set; } = 1000;

    /// <summary>
    /// 指标数据保留时间（小时）
    /// </summary>
    public int DataRetentionHours { get; set; } = 24;

    /// <summary>
    /// 最大内存中指标数据点数量
    /// </summary>
    public int MaxInMemoryDataPoints { get; set; } = 86400; // 24小时 * 3600秒

    /// <summary>
    /// 是否启用详细指标收集
    /// </summary>
    public bool EnableDetailedMetrics { get; set; } = true;

    /// <summary>
    /// 是否启用系统资源监控
    /// </summary>
    public bool EnableSystemMetrics { get; set; } = true;

    /// <summary>
    /// 是否启用网络指标监控
    /// </summary>
    public bool EnableNetworkMetrics { get; set; } = true;

    /// <summary>
    /// 是否启用 GC 指标监控
    /// </summary>
    public bool EnableGCMetrics { get; set; } = true;

    /// <summary>
    /// 指标聚合配置
    /// </summary>
    public MetricsAggregationOptions Aggregation { get; set; } = new();

    /// <summary>
    /// 存储配置
    /// </summary>
    public MetricsStorageOptions Storage { get; set; } = new();

    /// <summary>
    /// 导出配置
    /// </summary>
    public MetricsExportOptions Export { get; set; } = new();
}

/// <summary>
/// 指标聚合配置选项
/// </summary>
public class MetricsAggregationOptions
{
    /// <summary>
    /// 是否启用数据聚合
    /// </summary>
    public bool EnableAggregation { get; set; } = true;

    /// <summary>
    /// 聚合间隔（秒）
    /// </summary>
    public int AggregationIntervalSeconds { get; set; } = 60;

    /// <summary>
    /// 聚合窗口大小
    /// </summary>
    public int WindowSize { get; set; } = 60;

    /// <summary>
    /// 是否保留原始数据
    /// </summary>
    public bool KeepRawData { get; set; } = false;

    /// <summary>
    /// 聚合策略
    /// </summary>
    public List<AggregationStrategy> Strategies { get; set; } = new()
    {
        AggregationStrategy.Average,
        AggregationStrategy.Max,
        AggregationStrategy.Min
    };
}

/// <summary>
/// 聚合策略
/// </summary>
public enum AggregationStrategy
{
    /// <summary>
    /// 平均值
    /// </summary>
    Average = 0,

    /// <summary>
    /// 最大值
    /// </summary>
    Max = 1,

    /// <summary>
    /// 最小值
    /// </summary>
    Min = 2,

    /// <summary>
    /// 总和
    /// </summary>
    Sum = 3,

    /// <summary>
    /// 计数
    /// </summary>
    Count = 4,

    /// <summary>
    /// 百分位数
    /// </summary>
    Percentile = 5
}

/// <summary>
/// 指标存储配置选项
/// </summary>
public class MetricsStorageOptions
{
    /// <summary>
    /// 存储类型
    /// </summary>
    public MetricsStorageType Type { get; set; } = MetricsStorageType.Memory;

    /// <summary>
    /// 连接字符串
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// 数据库名称
    /// </summary>
    public string DatabaseName { get; set; } = "MqttBrokerMetrics";

    /// <summary>
    /// 表名前缀
    /// </summary>
    public string TablePrefix { get; set; } = "metrics_";

    /// <summary>
    /// 批量写入大小
    /// </summary>
    public int BatchSize { get; set; } = 1000;

    /// <summary>
    /// 写入超时（毫秒）
    /// </summary>
    public int WriteTimeoutMs { get; set; } = 5000;

    /// <summary>
    /// 是否启用压缩
    /// </summary>
    public bool EnableCompression { get; set; } = true;
}

/// <summary>
/// 指标存储类型
/// </summary>
public enum MetricsStorageType
{
    /// <summary>
    /// 内存存储
    /// </summary>
    Memory = 0,

    /// <summary>
    /// 文件存储
    /// </summary>
    File = 1,

    /// <summary>
    /// SQLite 数据库
    /// </summary>
    SQLite = 2,

    /// <summary>
    /// SQL Server 数据库
    /// </summary>
    SqlServer = 3,

    /// <summary>
    /// PostgreSQL 数据库
    /// </summary>
    PostgreSQL = 4,

    /// <summary>
    /// InfluxDB 时序数据库
    /// </summary>
    InfluxDB = 5
}

/// <summary>
/// 指标导出配置选项
/// </summary>
public class MetricsExportOptions
{
    /// <summary>
    /// 是否启用 REST API 导出
    /// </summary>
    public bool EnableRestApi { get; set; } = true;

    /// <summary>
    /// API 端口
    /// </summary>
    public int ApiPort { get; set; } = 8080;

    /// <summary>
    /// API 路径前缀
    /// </summary>
    public string ApiPathPrefix { get; set; } = "/api/metrics";

    /// <summary>
    /// 是否启用 Prometheus 导出
    /// </summary>
    public bool EnablePrometheus { get; set; } = false;

    /// <summary>
    /// Prometheus 端点路径
    /// </summary>
    public string PrometheusEndpoint { get; set; } = "/metrics";

    /// <summary>
    /// 是否启用文件导出
    /// </summary>
    public bool EnableFileExport { get; set; } = false;

    /// <summary>
    /// 文件导出路径
    /// </summary>
    public string FileExportPath { get; set; } = "./metrics_export";

    /// <summary>
    /// 文件导出格式
    /// </summary>
    public List<ExportFormat> FileExportFormats { get; set; } = new() { ExportFormat.Json };

    /// <summary>
    /// 导出间隔（秒）
    /// </summary>
    public int ExportIntervalSeconds { get; set; } = 300;
}

/// <summary>
/// 导出格式
/// </summary>
public enum ExportFormat
{
    /// <summary>
    /// JSON 格式
    /// </summary>
    Json = 0,

    /// <summary>
    /// CSV 格式
    /// </summary>
    Csv = 1,

    /// <summary>
    /// XML 格式
    /// </summary>
    Xml = 2
}

/// <summary>
/// 告警配置选项
/// </summary>
public class AlertOptions
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public const string SectionName = "Alerts";

    /// <summary>
    /// 是否启用告警
    /// </summary>
    public bool EnableAlerts { get; set; } = true;

    /// <summary>
    /// 告警评估间隔（秒）
    /// </summary>
    public int EvaluationIntervalSeconds { get; set; } = 30;

    /// <summary>
    /// 告警历史保留天数
    /// </summary>
    public int HistoryRetentionDays { get; set; } = 30;

    /// <summary>
    /// 最大活跃告警数
    /// </summary>
    public int MaxActiveAlerts { get; set; } = 1000;

    /// <summary>
    /// 默认告警规则
    /// </summary>
    public List<DefaultAlertRule> DefaultRules { get; set; } = new();

    /// <summary>
    /// 通知配置
    /// </summary>
    public AlertNotificationOptions Notification { get; set; } = new();
}

/// <summary>
/// 默认告警规则
/// </summary>
public class DefaultAlertRule
{
    /// <summary>
    /// 规则名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 指标路径
    /// </summary>
    public string MetricPath { get; set; } = string.Empty;

    /// <summary>
    /// 操作符
    /// </summary>
    public string Operator { get; set; } = string.Empty;

    /// <summary>
    /// 阈值
    /// </summary>
    public double Threshold { get; set; }

    /// <summary>
    /// 告警级别
    /// </summary>
    public string Level { get; set; } = "Warning";

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;
}

/// <summary>
/// 告警通知配置选项
/// </summary>
public class AlertNotificationOptions
{
    /// <summary>
    /// 是否启用通知
    /// </summary>
    public bool EnableNotifications { get; set; } = false;

    /// <summary>
    /// 通知渠道
    /// </summary>
    public List<NotificationChannel> Channels { get; set; } = new();

    /// <summary>
    /// 通知模板
    /// </summary>
    public NotificationTemplates Templates { get; set; } = new();
}

/// <summary>
/// 通知渠道
/// </summary>
public class NotificationChannel
{
    /// <summary>
    /// 渠道名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 渠道类型
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 配置参数
    /// </summary>
    public Dictionary<string, string> Configuration { get; set; } = new();
}

/// <summary>
/// 通知模板
/// </summary>
public class NotificationTemplates
{
    /// <summary>
    /// 告警触发模板
    /// </summary>
    public string AlertTriggered { get; set; } = "Alert: {AlertName} - {Message}";

    /// <summary>
    /// 告警解决模板
    /// </summary>
    public string AlertResolved { get; set; } = "Resolved: {AlertName} - {Message}";

    /// <summary>
    /// 告警升级模板
    /// </summary>
    public string AlertEscalated { get; set; } = "Escalated: {AlertName} - {Message}";
}
