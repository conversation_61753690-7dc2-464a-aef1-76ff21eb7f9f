using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Session;
using System.Text.Json;

namespace MqttBroker.Storage.Models;

/// <summary>
/// 会话实体
/// </summary>
[Table("Sessions")]
public class SessionEntity
{
    /// <summary>
    /// 客户端ID（主键）
    /// </summary>
    [Key]
    [MaxLength(256)]
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 是否为 Clean Session
    /// </summary>
    public bool IsCleanSession { get; set; }

    /// <summary>
    /// 会话创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后活动时间
    /// </summary>
    public DateTime LastActivity { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 会话过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// 协议版本
    /// </summary>
    public int ProtocolVersion { get; set; } = (int)MqttProtocolVersion.Version311;

    /// <summary>
    /// 保活间隔（秒）
    /// </summary>
    public ushort KeepAliveInterval { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    [MaxLength(256)]
    public string? Username { get; set; }

    /// <summary>
    /// 遗嘱消息（JSON 序列化）
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? WillMessageJson { get; set; }

    /// <summary>
    /// 会话属性（JSON 序列化）
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? PropertiesJson { get; set; }

    /// <summary>
    /// 是否在线
    /// </summary>
    public bool IsOnline { get; set; }

    /// <summary>
    /// 连接建立时间
    /// </summary>
    public DateTime? ConnectedAt { get; set; }

    /// <summary>
    /// 断开连接时间
    /// </summary>
    public DateTime? DisconnectedAt { get; set; }

    /// <summary>
    /// 会话状态
    /// </summary>
    public int State { get; set; } = (int)SessionState.Active;

    /// <summary>
    /// 记录版本（用于并发控制）
    /// </summary>
    [Timestamp]
    public byte[]? RowVersion { get; set; }

    /// <summary>
    /// 订阅集合（导航属性）
    /// </summary>
    public virtual ICollection<SubscriptionEntity> Subscriptions { get; set; } = new List<SubscriptionEntity>();

    /// <summary>
    /// 未确认消息集合（导航属性）
    /// </summary>
    public virtual ICollection<PendingMessageEntity> PendingMessages { get; set; } = new List<PendingMessageEntity>();

    /// <summary>
    /// 转换为领域模型
    /// </summary>
    /// <returns>MQTT 会话</returns>
    public MqttSession ToMqttSession()
    {
        var session = new MqttSession
        {
            ClientId = ClientId,
            IsCleanSession = IsCleanSession,
            CreatedAt = CreatedAt,
            LastActivity = LastActivity,
            ExpiresAt = ExpiresAt,
            ProtocolVersion = (MqttProtocolVersion)ProtocolVersion,
            KeepAliveInterval = KeepAliveInterval,
            Username = Username,
            IsOnline = IsOnline,
            ConnectedAt = ConnectedAt,
            DisconnectedAt = DisconnectedAt,
            State = (SessionState)State
        };

        // 反序列化遗嘱消息
        if (!string.IsNullOrEmpty(WillMessageJson))
        {
            try
            {
                session.WillMessage = JsonSerializer.Deserialize<MqttWillMessage>(WillMessageJson);
            }
            catch
            {
                // 忽略反序列化错误
            }
        }

        // 反序列化属性
        if (!string.IsNullOrEmpty(PropertiesJson))
        {
            try
            {
                session.Properties = JsonSerializer.Deserialize<Dictionary<string, object>>(PropertiesJson);
            }
            catch
            {
                // 忽略反序列化错误
            }
        }

        return session;
    }

    /// <summary>
    /// 从领域模型创建实体
    /// </summary>
    /// <param name="session">MQTT 会话</param>
    /// <returns>会话实体</returns>
    public static SessionEntity FromMqttSession(MqttSession session)
    {
        var entity = new SessionEntity
        {
            ClientId = session.ClientId,
            IsCleanSession = session.IsCleanSession,
            CreatedAt = session.CreatedAt,
            LastActivity = session.LastActivity,
            ExpiresAt = session.ExpiresAt,
            ProtocolVersion = (int)session.ProtocolVersion,
            KeepAliveInterval = session.KeepAliveInterval,
            Username = session.Username,
            IsOnline = session.IsOnline,
            ConnectedAt = session.ConnectedAt,
            DisconnectedAt = session.DisconnectedAt,
            State = (int)session.State
        };

        // 序列化遗嘱消息
        if (session.WillMessage != null)
        {
            try
            {
                entity.WillMessageJson = JsonSerializer.Serialize(session.WillMessage);
            }
            catch
            {
                // 忽略序列化错误
            }
        }

        // 序列化属性
        if (session.Properties != null && session.Properties.Count > 0)
        {
            try
            {
                entity.PropertiesJson = JsonSerializer.Serialize(session.Properties);
            }
            catch
            {
                // 忽略序列化错误
            }
        }

        return entity;
    }

    /// <summary>
    /// 更新实体属性
    /// </summary>
    /// <param name="session">MQTT 会话</param>
    public void UpdateFromMqttSession(MqttSession session)
    {
        IsCleanSession = session.IsCleanSession;
        LastActivity = session.LastActivity;
        ExpiresAt = session.ExpiresAt;
        ProtocolVersion = (int)session.ProtocolVersion;
        KeepAliveInterval = session.KeepAliveInterval;
        Username = session.Username;
        IsOnline = session.IsOnline;
        ConnectedAt = session.ConnectedAt;
        DisconnectedAt = session.DisconnectedAt;
        State = (int)session.State;

        // 序列化遗嘱消息
        if (session.WillMessage != null)
        {
            try
            {
                WillMessageJson = JsonSerializer.Serialize(session.WillMessage);
            }
            catch
            {
                WillMessageJson = null;
            }
        }
        else
        {
            WillMessageJson = null;
        }

        // 序列化属性
        if (session.Properties != null && session.Properties.Count > 0)
        {
            try
            {
                PropertiesJson = JsonSerializer.Serialize(session.Properties);
            }
            catch
            {
                PropertiesJson = null;
            }
        }
        else
        {
            PropertiesJson = null;
        }
    }
}
