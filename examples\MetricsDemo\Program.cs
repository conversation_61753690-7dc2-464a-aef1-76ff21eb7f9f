using MqttBroker.Metrics.Examples;

namespace MetricsDemo;

/// <summary>
/// 性能监控系统演示程序
/// </summary>
class Program
{
    /// <summary>
    /// 程序入口点
    /// </summary>
    static async Task Main(string[] args)
    {
        try
        {
            await MetricsExample.RunAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"演示程序运行时发生错误: {ex.Message}");
            Console.WriteLine($"详细信息: {ex}");
        }

        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }
}
