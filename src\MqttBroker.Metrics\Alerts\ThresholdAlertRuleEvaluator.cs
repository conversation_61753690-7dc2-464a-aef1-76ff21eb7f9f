using Microsoft.Extensions.Logging;
using MqttBroker.Metrics.Models;
using System.Collections.Concurrent;
using System.Reflection;

namespace MqttBroker.Metrics.Alerts;

/// <summary>
/// 阈值告警规则评估器
/// </summary>
public class ThresholdAlertRuleEvaluator : IAlertRuleEvaluator
{
    private readonly ILogger<ThresholdAlertRuleEvaluator> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    public ThresholdAlertRuleEvaluator(ILogger<ThresholdAlertRuleEvaluator> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 检查规则是否支持
    /// </summary>
    public bool SupportsRule(AlertRule rule)
    {
        return rule.Type == AlertRuleType.Threshold;
    }

    /// <summary>
    /// 评估告警规则
    /// </summary>
    public AlertEvaluationResult EvaluateRule(AlertRule rule, PerformanceMetrics metrics)
    {
        if (rule == null)
            throw new ArgumentNullException(nameof(rule));
        
        if (metrics == null)
            throw new ArgumentNullException(nameof(metrics));

        try
        {
            var currentValue = GetMetricValue(metrics, rule.MetricPath);
            var isTriggered = EvaluateThreshold(currentValue, rule.Operator, rule.Threshold);

            var message = isTriggered
                ? $"{rule.Name}: 当前值 {currentValue:F2} {GetOperatorText(rule.Operator)} 阈值 {rule.Threshold:F2}"
                : $"{rule.Name}: 当前值 {currentValue:F2} 正常";

            return new AlertEvaluationResult
            {
                IsTriggered = isTriggered,
                CurrentValue = currentValue,
                Threshold = rule.Threshold,
                Message = message,
                AdditionalData = new Dictionary<string, object>
                {
                    ["MetricPath"] = rule.MetricPath,
                    ["Operator"] = rule.Operator.ToString(),
                    ["EvaluatedAt"] = DateTime.UtcNow
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "评估阈值告警规则时发生错误: {RuleId} - {MetricPath}", rule.Id, rule.MetricPath);
            
            return new AlertEvaluationResult
            {
                IsTriggered = false,
                CurrentValue = 0,
                Threshold = rule.Threshold,
                Message = $"评估规则时发生错误: {ex.Message}",
                AdditionalData = new Dictionary<string, object>
                {
                    ["Error"] = ex.Message,
                    ["EvaluatedAt"] = DateTime.UtcNow
                }
            };
        }
    }

    /// <summary>
    /// 获取指标值
    /// </summary>
    private double GetMetricValue(PerformanceMetrics metrics, string metricPath)
    {
        if (string.IsNullOrEmpty(metricPath))
            throw new ArgumentException("指标路径不能为空", nameof(metricPath));

        var pathParts = metricPath.Split('.');
        if (pathParts.Length < 2)
            throw new ArgumentException($"无效的指标路径: {metricPath}", nameof(metricPath));

        object? currentObject = metrics;

        foreach (var part in pathParts)
        {
            if (currentObject == null)
                throw new InvalidOperationException($"指标路径 {metricPath} 中的 {part} 为 null");

            var property = currentObject.GetType().GetProperty(part, BindingFlags.Public | BindingFlags.Instance);
            if (property == null)
                throw new InvalidOperationException($"未找到属性: {part} 在类型 {currentObject.GetType().Name} 中");

            currentObject = property.GetValue(currentObject);
        }

        if (currentObject == null)
            return 0;

        // 尝试转换为 double
        return currentObject switch
        {
            double d => d,
            float f => f,
            int i => i,
            long l => l,
            decimal dec => (double)dec,
            _ => throw new InvalidOperationException($"无法将类型 {currentObject.GetType().Name} 转换为 double")
        };
    }

    /// <summary>
    /// 评估阈值条件
    /// </summary>
    private static bool EvaluateThreshold(double currentValue, ComparisonOperator op, double threshold)
    {
        return op switch
        {
            ComparisonOperator.GreaterThan => currentValue > threshold,
            ComparisonOperator.GreaterThanOrEqual => currentValue >= threshold,
            ComparisonOperator.LessThan => currentValue < threshold,
            ComparisonOperator.LessThanOrEqual => currentValue <= threshold,
            ComparisonOperator.Equal => Math.Abs(currentValue - threshold) < 0.0001,
            ComparisonOperator.NotEqual => Math.Abs(currentValue - threshold) >= 0.0001,
            _ => false
        };
    }

    /// <summary>
    /// 获取操作符文本
    /// </summary>
    private static string GetOperatorText(ComparisonOperator op)
    {
        return op switch
        {
            ComparisonOperator.GreaterThan => ">",
            ComparisonOperator.GreaterThanOrEqual => ">=",
            ComparisonOperator.LessThan => "<",
            ComparisonOperator.LessThanOrEqual => "<=",
            ComparisonOperator.Equal => "==",
            ComparisonOperator.NotEqual => "!=",
            _ => "?"
        };
    }
}

/// <summary>
/// 内存告警存储实现
/// </summary>
public class InMemoryAlertStorage : IAlertStorage
{
    private readonly ConcurrentDictionary<string, AlertRule> _rules = new();
    private readonly ConcurrentDictionary<string, Alert> _alerts = new();
    private readonly ILogger<InMemoryAlertStorage> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    public InMemoryAlertStorage(ILogger<InMemoryAlertStorage> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 存储告警规则
    /// </summary>
    public Task StoreRuleAsync(AlertRule rule, CancellationToken cancellationToken = default)
    {
        if (rule == null)
            throw new ArgumentNullException(nameof(rule));

        _rules[rule.Id] = rule;
        _logger.LogDebug("已存储告警规则: {RuleId}", rule.Id);
        
        return Task.CompletedTask;
    }

    /// <summary>
    /// 删除告警规则
    /// </summary>
    public Task DeleteRuleAsync(string ruleId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(ruleId))
            throw new ArgumentException("规则 ID 不能为空", nameof(ruleId));

        _rules.TryRemove(ruleId, out _);
        _logger.LogDebug("已删除告警规则: {RuleId}", ruleId);
        
        return Task.CompletedTask;
    }

    /// <summary>
    /// 获取所有告警规则
    /// </summary>
    public Task<IEnumerable<AlertRule>> GetRulesAsync(CancellationToken cancellationToken = default)
    {
        return Task.FromResult<IEnumerable<AlertRule>>(_rules.Values.ToList());
    }

    /// <summary>
    /// 获取指定告警规则
    /// </summary>
    public Task<AlertRule?> GetRuleAsync(string ruleId, CancellationToken cancellationToken = default)
    {
        _rules.TryGetValue(ruleId, out var rule);
        return Task.FromResult(rule);
    }

    /// <summary>
    /// 存储告警
    /// </summary>
    public Task StoreAlertAsync(Alert alert, CancellationToken cancellationToken = default)
    {
        if (alert == null)
            throw new ArgumentNullException(nameof(alert));

        _alerts[alert.Id] = alert;
        _logger.LogDebug("已存储告警: {AlertId}", alert.Id);
        
        return Task.CompletedTask;
    }

    /// <summary>
    /// 更新告警
    /// </summary>
    public Task UpdateAlertAsync(Alert alert, CancellationToken cancellationToken = default)
    {
        if (alert == null)
            throw new ArgumentNullException(nameof(alert));

        _alerts[alert.Id] = alert;
        _logger.LogDebug("已更新告警: {AlertId}", alert.Id);
        
        return Task.CompletedTask;
    }

    /// <summary>
    /// 获取活跃告警
    /// </summary>
    public Task<IEnumerable<Alert>> GetActiveAlertsAsync(CancellationToken cancellationToken = default)
    {
        var activeAlerts = _alerts.Values
            .Where(a => a.Status == AlertStatus.Active)
            .OrderByDescending(a => a.LastTriggeredAt)
            .ToList();

        return Task.FromResult<IEnumerable<Alert>>(activeAlerts);
    }

    /// <summary>
    /// 获取告警历史
    /// </summary>
    public Task<IEnumerable<Alert>> GetAlertHistoryAsync(DateTime startTime, DateTime endTime, CancellationToken cancellationToken = default)
    {
        var alerts = _alerts.Values
            .Where(a => a.FirstTriggeredAt >= startTime && a.FirstTriggeredAt <= endTime)
            .OrderByDescending(a => a.FirstTriggeredAt)
            .ToList();

        return Task.FromResult<IEnumerable<Alert>>(alerts);
    }

    /// <summary>
    /// 清理过期告警
    /// </summary>
    public Task<int> CleanupExpiredAlertsAsync(TimeSpan retentionPeriod, CancellationToken cancellationToken = default)
    {
        var cutoffTime = DateTime.UtcNow - retentionPeriod;
        var expiredAlerts = _alerts.Values
            .Where(a => a.Status == AlertStatus.Resolved && a.ResolvedAt.HasValue && a.ResolvedAt.Value < cutoffTime)
            .ToList();

        var removedCount = 0;
        foreach (var alert in expiredAlerts)
        {
            if (_alerts.TryRemove(alert.Id, out _))
            {
                removedCount++;
            }
        }

        if (removedCount > 0)
        {
            _logger.LogInformation("清理了 {Count} 个过期告警", removedCount);
        }

        return Task.FromResult(removedCount);
    }
}
