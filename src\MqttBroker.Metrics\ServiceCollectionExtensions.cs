using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MqttBroker.Metrics.Alerts;
using MqttBroker.Metrics.Collectors;
using MqttBroker.Metrics.Configuration;
using MqttBroker.Metrics.Export;
using MqttBroker.Metrics.Services;
using MqttBroker.Metrics.Storage;

namespace MqttBroker.Metrics;

/// <summary>
/// MQTT Broker 性能监控服务注册扩展
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加 MQTT Broker 性能监控服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddMqttBrokerMetrics(this IServiceCollection services, IConfiguration configuration)
    {
        // 注册配置选项
        services.Configure<MetricsOptions>(options => configuration.GetSection(MetricsOptions.SectionName).Bind(options));
        services.Configure<AlertOptions>(options => configuration.GetSection(AlertOptions.SectionName).Bind(options));

        // 注册指标收集器
        services.AddSingleton<IConnectionMetricsCollector, ConnectionMetricsCollector>();
        services.AddSingleton<IMessageMetricsCollector, MessageMetricsCollector>();
        services.AddSingleton<ISubscriptionMetricsCollector, SubscriptionMetricsCollector>();
        services.AddSingleton<IQoSMetricsCollector, QoSMetricsCollector>();
        services.AddSingleton<ISystemMetricsCollector, SystemMetricsCollector>();
        services.AddSingleton<INetworkMetricsCollector, NetworkMetricsCollector>();
        services.AddSingleton<IMetricsCollector, MetricsCollector>();

        // 注册指标存储
        services.AddSingleton<IMetricsStorage, InMemoryMetricsStorage>();

        // 注册告警系统
        services.AddSingleton<IAlertStorage, InMemoryAlertStorage>();
        services.AddSingleton<IAlertRuleEvaluator, ThresholdAlertRuleEvaluator>();
        services.AddSingleton<IAlertManager, AlertManager>();

        // 注册导出器
        services.AddSingleton<IMetricsExporter, JsonMetricsExporter>();
        services.AddSingleton<IMetricsExporter, CsvMetricsExporter>();
        services.AddSingleton<IPrometheusExporter, PrometheusExporter>();
        services.AddSingleton<IMetricsExportManager>(provider =>
        {
            var metricsOptions = provider.GetRequiredService<IOptions<MetricsOptions>>();
            var exportOptions = Options.Create(metricsOptions.Value.Export);
            var manager = new MetricsExportManager(
                provider.GetRequiredService<ILogger<MetricsExportManager>>(),
                exportOptions);

            // 注册所有导出器
            var exporters = provider.GetServices<IMetricsExporter>();
            foreach (var exporter in exporters)
            {
                manager.RegisterExporter(exporter);
            }

            return manager;
        });

        // 注册管理器和服务
        services.AddSingleton<IMetricsManager, MetricsManager>();
        services.AddHostedService<MetricsService>();

        return services;
    }

    /// <summary>
    /// 添加高性能配置的性能监控服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddHighPerformanceMetrics(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddMqttBrokerMetrics(configuration);

        // 高性能配置
        services.Configure<MetricsOptions>(options =>
        {
            options.CollectionIntervalMs = 500; // 更频繁的收集
            options.MaxInMemoryDataPoints = 172800; // 48小时数据
            options.EnableDetailedMetrics = true;
            options.EnableSystemMetrics = true;
            options.EnableNetworkMetrics = true;
            options.EnableGCMetrics = true;
        });

        return services;
    }

    /// <summary>
    /// 添加低资源配置的性能监控服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddLowResourceMetrics(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddMqttBrokerMetrics(configuration);

        // 低资源配置
        services.Configure<MetricsOptions>(options =>
        {
            options.CollectionIntervalMs = 5000; // 较少的收集频率
            options.MaxInMemoryDataPoints = 8640; // 12小时数据
            options.EnableDetailedMetrics = false;
            options.EnableSystemMetrics = false;
            options.EnableNetworkMetrics = false;
            options.EnableGCMetrics = false;
        });

        return services;
    }

    /// <summary>
    /// 添加性能监控服务（带自定义配置）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureMetrics">指标配置</param>
    /// <param name="configureAlerts">告警配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddMqttBrokerMetrics(
        this IServiceCollection services,
        Action<MetricsOptions>? configureMetrics = null,
        Action<AlertOptions>? configureAlerts = null)
    {
        // 注册指标收集器
        services.AddSingleton<IConnectionMetricsCollector, ConnectionMetricsCollector>();
        services.AddSingleton<IMessageMetricsCollector, MessageMetricsCollector>();
        services.AddSingleton<ISubscriptionMetricsCollector, SubscriptionMetricsCollector>();
        services.AddSingleton<IQoSMetricsCollector, QoSMetricsCollector>();
        services.AddSingleton<ISystemMetricsCollector, SystemMetricsCollector>();
        services.AddSingleton<INetworkMetricsCollector, NetworkMetricsCollector>();
        services.AddSingleton<IMetricsCollector, MetricsCollector>();

        // 注册指标存储
        services.AddSingleton<IMetricsStorage, InMemoryMetricsStorage>();

        // 注册告警系统
        services.AddSingleton<IAlertStorage, InMemoryAlertStorage>();
        services.AddSingleton<IAlertRuleEvaluator, ThresholdAlertRuleEvaluator>();
        services.AddSingleton<IAlertManager, AlertManager>();

        // 注册导出器
        services.AddSingleton<IMetricsExporter, JsonMetricsExporter>();
        services.AddSingleton<IMetricsExporter, CsvMetricsExporter>();
        services.AddSingleton<IPrometheusExporter, PrometheusExporter>();
        services.AddSingleton<IMetricsExportManager>(provider =>
        {
            var metricsOptions = provider.GetRequiredService<IOptions<MetricsOptions>>();
            var exportOptions = Options.Create(metricsOptions.Value.Export);
            var manager = new MetricsExportManager(
                provider.GetRequiredService<ILogger<MetricsExportManager>>(),
                exportOptions);

            // 注册所有导出器
            var exporters = provider.GetServices<IMetricsExporter>();
            foreach (var exporter in exporters)
            {
                manager.RegisterExporter(exporter);
            }

            return manager;
        });

        // 注册管理器和服务
        services.AddSingleton<IMetricsManager, MetricsManager>();
        services.AddHostedService<MetricsService>();

        // 应用自定义配置
        if (configureMetrics != null)
        {
            services.Configure(configureMetrics);
        }

        if (configureAlerts != null)
        {
            services.Configure(configureAlerts);
        }

        return services;
    }
}
