using MqttBroker.Core.Protocol;
using MqttBroker.Core.Network;

namespace MqttBroker.Core.QoS;

/// <summary>
/// 消息重传服务接口，负责管理消息的重传逻辑
/// </summary>
public interface IMessageRetransmissionService
{
    /// <summary>
    /// 启动重传服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止重传服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查并执行消息重传
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重传的消息数量</returns>
    Task<int> CheckAndRetransmitMessagesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 重传特定客户端的超时消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重传的消息数量</returns>
    Task<int> RetransmitClientMessagesAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 重传特定消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="messageId">消息ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重传结果</returns>
    Task<RetransmissionResult> RetransmitMessageAsync(string clientId, ushort messageId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取重传统计信息
    /// </summary>
    /// <returns>重传统计信息</returns>
    Task<RetransmissionStatistics> GetStatisticsAsync();

    /// <summary>
    /// 清理超过最大重传次数的消息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理的消息数量</returns>
    Task<int> CleanupFailedMessagesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 设置客户端连接提供者（用于获取活跃连接）
    /// </summary>
    /// <param name="connectionProvider">连接提供者</param>
    void SetConnectionProvider(Func<string, IClientConnection?> connectionProvider);
}

/// <summary>
/// 重传结果
/// </summary>
public class RetransmissionResult
{
    /// <summary>
    /// 是否重传成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 消息ID
    /// </summary>
    public ushort MessageId { get; set; }

    /// <summary>
    /// 重传次数
    /// </summary>
    public int RetransmissionCount { get; set; }

    /// <summary>
    /// QoS级别
    /// </summary>
    public MqttQoSLevel QoSLevel { get; set; }

    /// <summary>
    /// 重传耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }

    /// <summary>
    /// 重传原因
    /// </summary>
    public RetransmissionReason Reason { get; set; }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    public static RetransmissionResult Success(string clientId, ushort messageId, int retransmissionCount, MqttQoSLevel qosLevel, RetransmissionReason reason)
    {
        return new RetransmissionResult
        {
            IsSuccess = true,
            ClientId = clientId,
            MessageId = messageId,
            RetransmissionCount = retransmissionCount,
            QoSLevel = qosLevel,
            Reason = reason
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    public static RetransmissionResult Failure(string clientId, ushort messageId, string errorMessage, RetransmissionReason reason)
    {
        return new RetransmissionResult
        {
            IsSuccess = false,
            ClientId = clientId,
            MessageId = messageId,
            ErrorMessage = errorMessage,
            Reason = reason
        };
    }
}

/// <summary>
/// 重传原因枚举
/// </summary>
public enum RetransmissionReason
{
    /// <summary>
    /// 超时重传
    /// </summary>
    Timeout,

    /// <summary>
    /// 手动重传
    /// </summary>
    Manual,

    /// <summary>
    /// 客户端重连后重传
    /// </summary>
    ClientReconnected,

    /// <summary>
    /// 系统重启后重传
    /// </summary>
    SystemRestart
}

/// <summary>
/// 重传统计信息
/// </summary>
public class RetransmissionStatistics
{
    /// <summary>
    /// 总重传次数
    /// </summary>
    public long TotalRetransmissions { get; set; }

    /// <summary>
    /// QoS 1 重传次数
    /// </summary>
    public long QoS1Retransmissions { get; set; }

    /// <summary>
    /// QoS 2 重传次数
    /// </summary>
    public long QoS2Retransmissions { get; set; }

    /// <summary>
    /// 成功重传次数
    /// </summary>
    public long SuccessfulRetransmissions { get; set; }

    /// <summary>
    /// 失败重传次数
    /// </summary>
    public long FailedRetransmissions { get; set; }

    /// <summary>
    /// 超时导致的重传次数
    /// </summary>
    public long TimeoutRetransmissions { get; set; }

    /// <summary>
    /// 手动重传次数
    /// </summary>
    public long ManualRetransmissions { get; set; }

    /// <summary>
    /// 平均重传延迟（毫秒）
    /// </summary>
    public double AverageRetransmissionLatency { get; set; }

    /// <summary>
    /// 当前待重传消息数量
    /// </summary>
    public int CurrentPendingRetransmissions { get; set; }

    /// <summary>
    /// 超过最大重传次数的消息数量
    /// </summary>
    public long MessagesExceedingMaxRetries { get; set; }

    /// <summary>
    /// 最后一次重传检查时间
    /// </summary>
    public DateTime? LastRetransmissionCheckTime { get; set; }

    /// <summary>
    /// 重传成功率
    /// </summary>
    public double SuccessRate => TotalRetransmissions > 0 ? (double)SuccessfulRetransmissions / TotalRetransmissions * 100 : 0;

    /// <summary>
    /// 按重传原因分组的统计
    /// </summary>
    public Dictionary<RetransmissionReason, long> RetransmissionsByReason { get; set; } = new();

    /// <summary>
    /// 按客户端分组的重传统计
    /// </summary>
    public Dictionary<string, ClientRetransmissionStatistics> ClientStatistics { get; set; } = new();
}

/// <summary>
/// 客户端重传统计信息
/// </summary>
public class ClientRetransmissionStatistics
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 重传次数
    /// </summary>
    public long RetransmissionCount { get; set; }

    /// <summary>
    /// 成功重传次数
    /// </summary>
    public long SuccessfulRetransmissions { get; set; }

    /// <summary>
    /// 失败重传次数
    /// </summary>
    public long FailedRetransmissions { get; set; }

    /// <summary>
    /// 当前待重传消息数量
    /// </summary>
    public int CurrentPendingRetransmissions { get; set; }

    /// <summary>
    /// 最后一次重传时间
    /// </summary>
    public DateTime? LastRetransmissionTime { get; set; }

    /// <summary>
    /// 平均重传延迟（毫秒）
    /// </summary>
    public double AverageRetransmissionLatency { get; set; }

    /// <summary>
    /// 重传成功率
    /// </summary>
    public double SuccessRate => RetransmissionCount > 0 ? (double)SuccessfulRetransmissions / RetransmissionCount * 100 : 0;
}
