@echo off
REM MQTT Broker 最简单的一键安装脚本
REM 放在项目根目录，双击即可运行

setlocal enabledelayedexpansion

REM 设置控制台编码
chcp 936 >nul

echo ==========================================
echo        MQTT Broker 一键安装
echo ==========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 【错误】需要管理员权限
    echo.
    echo 请按以下步骤操作：
    echo 1. 右键点击此文件
    echo 2. 选择"以管理员身份运行"
    echo.
    pause
    exit /b 1
)

echo 【检查】管理员权限 - 通过
echo.

REM 获取当前目录（项目根目录）
set "PROJECT_ROOT=%~dp0"
set "INSTALL_PATH=C:\MqttBroker"

echo 【信息】项目目录: %PROJECT_ROOT%
echo 【信息】安装目录: %INSTALL_PATH%
echo.

REM 检查项目结构
if not exist "%PROJECT_ROOT%src\MqttBroker.Host" (
    echo 【错误】项目结构不正确
    echo 请确保此脚本位于项目根目录
    echo 期望找到: src\MqttBroker.Host
    pause
    exit /b 1
)

echo 【检查】项目结构 - 通过
echo.

REM 检查 .NET 运行时
echo 【检查】.NET 8 运行时...
dotnet --version >nul 2>&1
if %errorLevel% neq 0 (
    echo 【错误】未找到 .NET 运行时
    echo.
    echo 请先安装 .NET 8 运行时：
    echo 1. 访问：https://dotnet.microsoft.com/download/dotnet/8.0
    echo 2. 下载并安装 ".NET 8.0 Runtime" (x64)
    echo 3. 重新运行此脚本
    echo.
    pause
    exit /b 1
)

echo 【检查】.NET 运行时 - 通过
echo.

REM 开始安装
echo 【开始】安装 MQTT Broker...
echo.

REM 创建目录
echo   步骤 1/5: 创建目录...
if exist "%INSTALL_PATH%" (
    rmdir /s /q "%INSTALL_PATH%" 2>nul
)
mkdir "%INSTALL_PATH%"
mkdir "%INSTALL_PATH%\app"
mkdir "%INSTALL_PATH%\data"
mkdir "%INSTALL_PATH%\logs"
mkdir "%INSTALL_PATH%\scripts"

REM 构建项目
echo   步骤 2/5: 构建项目...
cd /d "%PROJECT_ROOT%"
dotnet clean --configuration Release >nul 2>&1
dotnet restore >nul 2>&1
if %errorLevel% neq 0 (
    echo 【错误】依赖还原失败
    pause
    exit /b 1
)

REM 发布应用
echo   步骤 3/5: 发布应用...
dotnet publish src\MqttBroker.Host -c Release -o "%INSTALL_PATH%\app" --self-contained false >nul 2>&1
if %errorLevel% neq 0 (
    echo 【错误】应用发布失败
    pause
    exit /b 1
)

REM 创建配置
echo   步骤 4/5: 创建配置...
(
echo {
echo   "Logging": {
echo     "LogLevel": { "Default": "Information", "Microsoft": "Warning" },
echo     "Console": { "IncludeScopes": true },
echo     "File": {
echo       "Path": "C:\\MqttBroker\\logs\\mqtt-broker-.log",
echo       "RollingInterval": "Day",
echo       "RetainedFileCountLimit": 7
echo     }
echo   },
echo   "MqttBroker": {
echo     "Network": {
echo       "Tcp": { "Enabled": true, "Port": 1883, "Address": "127.0.0.1" },
echo       "WebSocket": { "Enabled": true, "Port": 8080 }
echo     },
echo     "Storage": {
echo       "Provider": "SQLite",
echo       "ConnectionString": "Data Source=C:\\MqttBroker\\data\\mqtt_broker.db"
echo     },
echo     "Security": { "AllowAnonymous": true },
echo     "HealthChecks": { "Enabled": true, "Port": 9090, "Path": "/health" }
echo   }
echo }
) > "%INSTALL_PATH%\app\appsettings.json"

REM 创建管理脚本
echo   步骤 5/5: 创建管理脚本...

REM 启动脚本
(
echo @echo off
echo echo 启动 MQTT Broker...
echo cd /d C:\MqttBroker\app
echo start "MQTT Broker" MqttBroker.Host.exe
echo timeout /t 3 /nobreak ^>nul
echo echo MQTT Broker 已启动！
echo echo 访问地址：
echo echo   MQTT: localhost:1883
echo echo   WebSocket: ws://localhost:8080/mqtt
echo echo   健康检查: http://localhost:9090/health
echo pause
) > "%INSTALL_PATH%\scripts\启动.cmd"

REM 停止脚本
(
echo @echo off
echo echo 停止 MQTT Broker...
echo taskkill /f /im MqttBroker.Host.exe ^>nul 2^>^&1
echo echo MQTT Broker 已停止
echo pause
) > "%INSTALL_PATH%\scripts\停止.cmd"

REM 状态脚本
(
echo @echo off
echo echo 检查状态...
echo tasklist /fi "imagename eq MqttBroker.Host.exe" ^| find /i "MqttBroker.Host.exe" ^>nul
echo if %%errorLevel%% equ 0 (
echo     echo [状态] 运行中
echo ^) else (
echo     echo [状态] 未运行
echo ^)
echo pause
) > "%INSTALL_PATH%\scripts\状态.cmd"

REM 完成安装
echo.
echo ==========================================
echo           安装完成！
echo ==========================================
echo.
echo 安装位置: %INSTALL_PATH%
echo.
echo 快速操作：
echo   启动: %INSTALL_PATH%\scripts\启动.cmd
echo   停止: %INSTALL_PATH%\scripts\停止.cmd
echo   状态: %INSTALL_PATH%\scripts\状态.cmd
echo.
echo 访问地址：
echo   MQTT: localhost:1883
echo   WebSocket: ws://localhost:8080/mqtt
echo   健康检查: http://localhost:9090/health
echo.

REM 询问是否启动
set /p "START_NOW=是否现在启动 MQTT Broker? (Y/n): "
if /i not "%START_NOW%"=="n" (
    echo.
    echo 正在启动...
    cd /d "%INSTALL_PATH%\app"
    start "MQTT Broker" MqttBroker.Host.exe
    timeout /t 3 /nobreak >nul
    echo MQTT Broker 已启动！
)

echo.
echo 安装和配置完成！
pause
endlocal
