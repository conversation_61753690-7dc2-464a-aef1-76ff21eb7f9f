namespace MqttBroker.Core.Will;

/// <summary>
/// 遗嘱消息注册事件参数
/// </summary>
public class WillMessageRegisteredEventArgs : EventArgs
{
    /// <summary>
    /// 遗嘱消息注册信息
    /// </summary>
    public WillMessageRegistration Registration { get; }

    /// <summary>
    /// 注册结果
    /// </summary>
    public WillMessageRegistrationResult Result { get; }

    /// <summary>
    /// 事件时间
    /// </summary>
    public DateTime EventTime { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="registration">遗嘱消息注册信息</param>
    /// <param name="result">注册结果</param>
    public WillMessageRegisteredEventArgs(WillMessageRegistration registration, WillMessageRegistrationResult result)
    {
        Registration = registration ?? throw new ArgumentNullException(nameof(registration));
        Result = result ?? throw new ArgumentNullException(nameof(result));
        EventTime = DateTime.UtcNow;
    }
}

/// <summary>
/// 遗嘱消息触发事件参数
/// </summary>
public class WillMessageTriggeredEventArgs : EventArgs
{
    /// <summary>
    /// 遗嘱消息注册信息
    /// </summary>
    public WillMessageRegistration? Registration { get; }

    /// <summary>
    /// 触发结果
    /// </summary>
    public WillMessageTriggerResult Result { get; }

    /// <summary>
    /// 事件时间
    /// </summary>
    public DateTime EventTime { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="registration">遗嘱消息注册信息（可能为null，如果客户端没有遗嘱消息）</param>
    /// <param name="result">触发结果</param>
    public WillMessageTriggeredEventArgs(WillMessageRegistration? registration, WillMessageTriggerResult result)
    {
        Registration = registration;
        Result = result ?? throw new ArgumentNullException(nameof(result));
        EventTime = DateTime.UtcNow;
    }
}

/// <summary>
/// 遗嘱消息清除事件参数
/// </summary>
public class WillMessageClearedEventArgs : EventArgs
{
    /// <summary>
    /// 遗嘱消息注册信息（清除前的信息）
    /// </summary>
    public WillMessageRegistration? Registration { get; }

    /// <summary>
    /// 清除结果
    /// </summary>
    public WillMessageClearResult Result { get; }

    /// <summary>
    /// 事件时间
    /// </summary>
    public DateTime EventTime { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="registration">遗嘱消息注册信息（可能为null，如果客户端没有遗嘱消息）</param>
    /// <param name="result">清除结果</param>
    public WillMessageClearedEventArgs(WillMessageRegistration? registration, WillMessageClearResult result)
    {
        Registration = registration;
        Result = result ?? throw new ArgumentNullException(nameof(result));
        EventTime = DateTime.UtcNow;
    }
}

/// <summary>
/// 遗嘱消息存储事件参数
/// </summary>
public class WillMessageStoredEventArgs : EventArgs
{
    /// <summary>
    /// 遗嘱消息注册信息
    /// </summary>
    public WillMessageRegistration Registration { get; }

    /// <summary>
    /// 存储结果
    /// </summary>
    public WillMessageStorageResult Result { get; }

    /// <summary>
    /// 事件时间
    /// </summary>
    public DateTime EventTime { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="registration">遗嘱消息注册信息</param>
    /// <param name="result">存储结果</param>
    public WillMessageStoredEventArgs(WillMessageRegistration registration, WillMessageStorageResult result)
    {
        Registration = registration ?? throw new ArgumentNullException(nameof(registration));
        Result = result ?? throw new ArgumentNullException(nameof(result));
        EventTime = DateTime.UtcNow;
    }
}

/// <summary>
/// 遗嘱消息删除事件参数
/// </summary>
public class WillMessageDeletedEventArgs : EventArgs
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; }

    /// <summary>
    /// 删除结果
    /// </summary>
    public WillMessageStorageResult Result { get; }

    /// <summary>
    /// 事件时间
    /// </summary>
    public DateTime EventTime { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="result">删除结果</param>
    public WillMessageDeletedEventArgs(string clientId, WillMessageStorageResult result)
    {
        ClientId = clientId ?? throw new ArgumentNullException(nameof(clientId));
        Result = result ?? throw new ArgumentNullException(nameof(result));
        EventTime = DateTime.UtcNow;
    }
}

/// <summary>
/// 遗嘱消息清理事件参数
/// </summary>
public class WillMessageCleanupEventArgs : EventArgs
{
    /// <summary>
    /// 清理结果
    /// </summary>
    public WillMessageCleanupResult Result { get; }

    /// <summary>
    /// 事件时间
    /// </summary>
    public DateTime EventTime { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="result">清理结果</param>
    public WillMessageCleanupEventArgs(WillMessageCleanupResult result)
    {
        Result = result ?? throw new ArgumentNullException(nameof(result));
        EventTime = DateTime.UtcNow;
    }
}

/// <summary>
/// 遗嘱消息发布事件参数
/// </summary>
public class WillMessagePublishedEventArgs : EventArgs
{
    /// <summary>
    /// 遗嘱消息注册信息
    /// </summary>
    public WillMessageRegistration Registration { get; }

    /// <summary>
    /// 发布是否成功
    /// </summary>
    public bool IsSuccess { get; }

    /// <summary>
    /// 发布延迟（毫秒）
    /// </summary>
    public long PublishLatencyMs { get; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; }

    /// <summary>
    /// 事件时间
    /// </summary>
    public DateTime EventTime { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="registration">遗嘱消息注册信息</param>
    /// <param name="isSuccess">发布是否成功</param>
    /// <param name="publishLatencyMs">发布延迟</param>
    /// <param name="errorMessage">错误消息</param>
    public WillMessagePublishedEventArgs(WillMessageRegistration registration, bool isSuccess, 
        long publishLatencyMs, string? errorMessage = null)
    {
        Registration = registration ?? throw new ArgumentNullException(nameof(registration));
        IsSuccess = isSuccess;
        PublishLatencyMs = publishLatencyMs;
        ErrorMessage = errorMessage;
        EventTime = DateTime.UtcNow;
    }
}

/// <summary>
/// 遗嘱消息错误事件参数
/// </summary>
public class WillMessageErrorEventArgs : EventArgs
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; }

    /// <summary>
    /// 错误类型
    /// </summary>
    public WillMessageErrorType ErrorType { get; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; }

    /// <summary>
    /// 异常信息
    /// </summary>
    public Exception? Exception { get; }

    /// <summary>
    /// 事件时间
    /// </summary>
    public DateTime EventTime { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="errorType">错误类型</param>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="exception">异常信息</param>
    public WillMessageErrorEventArgs(string clientId, WillMessageErrorType errorType, 
        string errorMessage, Exception? exception = null)
    {
        ClientId = clientId ?? throw new ArgumentNullException(nameof(clientId));
        ErrorType = errorType;
        ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));
        Exception = exception;
        EventTime = DateTime.UtcNow;
    }
}

/// <summary>
/// 遗嘱消息错误类型
/// </summary>
public enum WillMessageErrorType
{
    /// <summary>
    /// 注册错误
    /// </summary>
    RegistrationError,

    /// <summary>
    /// 触发错误
    /// </summary>
    TriggerError,

    /// <summary>
    /// 清除错误
    /// </summary>
    ClearError,

    /// <summary>
    /// 存储错误
    /// </summary>
    StorageError,

    /// <summary>
    /// 发布错误
    /// </summary>
    PublishError,

    /// <summary>
    /// 清理错误
    /// </summary>
    CleanupError,

    /// <summary>
    /// 配置错误
    /// </summary>
    ConfigurationError,

    /// <summary>
    /// 网络错误
    /// </summary>
    NetworkError,

    /// <summary>
    /// 未知错误
    /// </summary>
    UnknownError
}
