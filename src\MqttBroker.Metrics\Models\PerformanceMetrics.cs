using System.Text.Json.Serialization;

namespace MqttBroker.Metrics.Models;

/// <summary>
/// 性能指标数据模型
/// </summary>
public class PerformanceMetrics
{
    /// <summary>
    /// 指标时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 连接相关指标
    /// </summary>
    public ConnectionMetrics Connection { get; set; } = new();

    /// <summary>
    /// 消息相关指标
    /// </summary>
    public MessageMetrics Message { get; set; } = new();

    /// <summary>
    /// 主题订阅相关指标
    /// </summary>
    public SubscriptionMetrics Subscription { get; set; } = new();

    /// <summary>
    /// QoS 处理相关指标
    /// </summary>
    public QoSMetrics QoS { get; set; } = new();

    /// <summary>
    /// 系统资源相关指标
    /// </summary>
    public SystemMetrics System { get; set; } = new();

    /// <summary>
    /// 网络相关指标
    /// </summary>
    public NetworkMetrics Network { get; set; } = new();
}

/// <summary>
/// 连接相关指标
/// </summary>
public class ConnectionMetrics
{
    /// <summary>
    /// 当前活跃连接数
    /// </summary>
    public long ActiveConnections { get; set; }

    /// <summary>
    /// 峰值连接数
    /// </summary>
    public long PeakConnections { get; set; }

    /// <summary>
    /// 总连接数（累计）
    /// </summary>
    public long TotalConnections { get; set; }

    /// <summary>
    /// 连接建立速率（连接/秒）
    /// </summary>
    public double ConnectionRate { get; set; }

    /// <summary>
    /// 连接断开速率（断开/秒）
    /// </summary>
    public double DisconnectionRate { get; set; }

    /// <summary>
    /// 平均连接持续时间（秒）
    /// </summary>
    public double AverageConnectionDuration { get; set; }

    /// <summary>
    /// 认证失败次数
    /// </summary>
    public long AuthenticationFailures { get; set; }

    /// <summary>
    /// 连接超时次数
    /// </summary>
    public long ConnectionTimeouts { get; set; }
}

/// <summary>
/// 消息相关指标
/// </summary>
public class MessageMetrics
{
    /// <summary>
    /// 发送消息总数
    /// </summary>
    public long MessagesSent { get; set; }

    /// <summary>
    /// 接收消息总数
    /// </summary>
    public long MessagesReceived { get; set; }

    /// <summary>
    /// 消息发送速率（消息/秒）
    /// </summary>
    public double MessageSendRate { get; set; }

    /// <summary>
    /// 消息接收速率（消息/秒）
    /// </summary>
    public double MessageReceiveRate { get; set; }

    /// <summary>
    /// 平均消息大小（字节）
    /// </summary>
    public double AverageMessageSize { get; set; }

    /// <summary>
    /// 消息路由延迟（毫秒）
    /// </summary>
    public MessageLatencyMetrics RoutingLatency { get; set; } = new();

    /// <summary>
    /// 离线消息数量
    /// </summary>
    public long OfflineMessages { get; set; }

    /// <summary>
    /// 死信队列消息数量
    /// </summary>
    public long DeadLetterMessages { get; set; }

    /// <summary>
    /// 保留消息数量
    /// </summary>
    public long RetainedMessages { get; set; }
}

/// <summary>
/// 消息延迟指标
/// </summary>
public class MessageLatencyMetrics
{
    /// <summary>
    /// 平均延迟（毫秒）
    /// </summary>
    public double Average { get; set; }

    /// <summary>
    /// 最小延迟（毫秒）
    /// </summary>
    public double Min { get; set; }

    /// <summary>
    /// 最大延迟（毫秒）
    /// </summary>
    public double Max { get; set; }

    /// <summary>
    /// P50 延迟（毫秒）
    /// </summary>
    public double P50 { get; set; }

    /// <summary>
    /// P95 延迟（毫秒）
    /// </summary>
    public double P95 { get; set; }

    /// <summary>
    /// P99 延迟（毫秒）
    /// </summary>
    public double P99 { get; set; }
}

/// <summary>
/// 主题订阅相关指标
/// </summary>
public class SubscriptionMetrics
{
    /// <summary>
    /// 总订阅数
    /// </summary>
    public long TotalSubscriptions { get; set; }

    /// <summary>
    /// 活跃主题数
    /// </summary>
    public long ActiveTopics { get; set; }

    /// <summary>
    /// 通配符订阅数
    /// </summary>
    public long WildcardSubscriptions { get; set; }

    /// <summary>
    /// 主题匹配延迟（毫秒）
    /// </summary>
    public MessageLatencyMetrics MatchingLatency { get; set; } = new();

    /// <summary>
    /// 订阅速率（订阅/秒）
    /// </summary>
    public double SubscriptionRate { get; set; }

    /// <summary>
    /// 取消订阅速率（取消订阅/秒）
    /// </summary>
    public double UnsubscriptionRate { get; set; }
}

/// <summary>
/// QoS 处理相关指标
/// </summary>
public class QoSMetrics
{
    /// <summary>
    /// QoS 0 消息数量
    /// </summary>
    public long QoS0Messages { get; set; }

    /// <summary>
    /// QoS 1 消息数量
    /// </summary>
    public long QoS1Messages { get; set; }

    /// <summary>
    /// QoS 2 消息数量
    /// </summary>
    public long QoS2Messages { get; set; }

    /// <summary>
    /// 待确认消息数量
    /// </summary>
    public long PendingAcknowledgments { get; set; }

    /// <summary>
    /// QoS 处理延迟（毫秒）
    /// </summary>
    public MessageLatencyMetrics ProcessingLatency { get; set; } = new();

    /// <summary>
    /// 消息重传次数
    /// </summary>
    public long MessageRetransmissions { get; set; }

    /// <summary>
    /// QoS 处理成功率（百分比）
    /// </summary>
    public double SuccessRate { get; set; }
}

/// <summary>
/// 系统资源相关指标
/// </summary>
public class SystemMetrics
{
    /// <summary>
    /// CPU 使用率（百分比）
    /// </summary>
    public double CpuUsage { get; set; }

    /// <summary>
    /// 内存使用情况
    /// </summary>
    public MemoryMetrics Memory { get; set; } = new();

    /// <summary>
    /// 线程池状态
    /// </summary>
    public ThreadPoolMetrics ThreadPool { get; set; } = new();

    /// <summary>
    /// GC 统计信息
    /// </summary>
    public GCMetrics GarbageCollection { get; set; } = new();
}

/// <summary>
/// 内存使用指标
/// </summary>
public class MemoryMetrics
{
    /// <summary>
    /// 总内存使用量（字节）
    /// </summary>
    public long TotalMemory { get; set; }

    /// <summary>
    /// 工作集内存（字节）
    /// </summary>
    public long WorkingSet { get; set; }

    /// <summary>
    /// 私有内存（字节）
    /// </summary>
    public long PrivateMemory { get; set; }

    /// <summary>
    /// 托管堆内存（字节）
    /// </summary>
    public long ManagedHeap { get; set; }

    /// <summary>
    /// 大对象堆内存（字节）
    /// </summary>
    public long LargeObjectHeap { get; set; }
}

/// <summary>
/// 线程池指标
/// </summary>
public class ThreadPoolMetrics
{
    /// <summary>
    /// 工作线程数
    /// </summary>
    public int WorkerThreads { get; set; }

    /// <summary>
    /// 完成端口线程数
    /// </summary>
    public int CompletionPortThreads { get; set; }

    /// <summary>
    /// 最大工作线程数
    /// </summary>
    public int MaxWorkerThreads { get; set; }

    /// <summary>
    /// 最大完成端口线程数
    /// </summary>
    public int MaxCompletionPortThreads { get; set; }

    /// <summary>
    /// 队列中的工作项数
    /// </summary>
    public long QueuedWorkItems { get; set; }
}

/// <summary>
/// 垃圾回收指标
/// </summary>
public class GCMetrics
{
    /// <summary>
    /// 第0代 GC 次数
    /// </summary>
    public int Gen0Collections { get; set; }

    /// <summary>
    /// 第1代 GC 次数
    /// </summary>
    public int Gen1Collections { get; set; }

    /// <summary>
    /// 第2代 GC 次数
    /// </summary>
    public int Gen2Collections { get; set; }

    /// <summary>
    /// 总分配字节数
    /// </summary>
    public long TotalAllocatedBytes { get; set; }
}

/// <summary>
/// 网络相关指标
/// </summary>
public class NetworkMetrics
{
    /// <summary>
    /// 发送字节数
    /// </summary>
    public long BytesSent { get; set; }

    /// <summary>
    /// 接收字节数
    /// </summary>
    public long BytesReceived { get; set; }

    /// <summary>
    /// 网络发送速率（字节/秒）
    /// </summary>
    public double SendRate { get; set; }

    /// <summary>
    /// 网络接收速率（字节/秒）
    /// </summary>
    public double ReceiveRate { get; set; }

    /// <summary>
    /// 网络错误次数
    /// </summary>
    public long NetworkErrors { get; set; }

    /// <summary>
    /// 连接重置次数
    /// </summary>
    public long ConnectionResets { get; set; }
}
