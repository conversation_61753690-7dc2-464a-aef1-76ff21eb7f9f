using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace MqttBroker.Core.Will;

/// <summary>
/// 内存遗嘱消息存储实现
/// </summary>
public class InMemoryWillMessageStorage : IWillMessageStorage
{
    private readonly ILogger<InMemoryWillMessageStorage> _logger;
    private readonly ConcurrentDictionary<string, WillMessageRegistration> _willMessages;
    private readonly object _statsLock = new();
    private readonly DateTime _startTime;

    // 统计信息
    private long _totalStoreOperations;
    private long _totalDeleteOperations;
    private long _totalQueryOperations;
    private long _totalFailedOperations;
    private DateTime _lastOperationTime;
    private readonly List<long> _operationLatencies = new();
    private bool _disposed;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public InMemoryWillMessageStorage(ILogger<InMemoryWillMessageStorage> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _willMessages = new ConcurrentDictionary<string, WillMessageRegistration>();
        _startTime = DateTime.UtcNow;
        
        _logger.LogInformation("InMemoryWillMessageStorage initialized");
    }

    /// <summary>
    /// 存储遗嘱消息注册信息
    /// </summary>
    public Task<WillMessageStorageResult> StoreWillMessageAsync(WillMessageRegistration registration, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(InMemoryWillMessageStorage));

        var stopwatch = Stopwatch.StartNew();

        try
        {
            if (registration == null)
                throw new ArgumentNullException(nameof(registration));

            if (string.IsNullOrEmpty(registration.ClientId))
                throw new ArgumentException("Client ID cannot be null or empty", nameof(registration));

            // 更新最后修改时间
            registration.UpdateLastModified();

            // 存储遗嘱消息
            _willMessages.AddOrUpdate(registration.ClientId, registration, (key, oldValue) => registration);

            stopwatch.Stop();
            UpdateStatistics(WillMessageStorageOperation.Store, stopwatch.ElapsedMilliseconds, true);

            _logger.LogTrace("Stored will message for client: {ClientId}, topic: {Topic}", 
                registration.ClientId, registration.Topic);

            return Task.FromResult(WillMessageStorageResult.Success(registration.ClientId, WillMessageStorageOperation.Store));
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            UpdateStatistics(WillMessageStorageOperation.Store, stopwatch.ElapsedMilliseconds, false);

            _logger.LogError(ex, "Error storing will message for client: {ClientId}", registration?.ClientId);
            return Task.FromResult(WillMessageStorageResult.Failure(
                registration?.ClientId ?? "unknown", 
                WillMessageStorageOperation.Store, 
                ex.Message));
        }
    }

    /// <summary>
    /// 获取遗嘱消息注册信息
    /// </summary>
    public Task<WillMessageRegistration?> GetWillMessageAsync(string clientId, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(InMemoryWillMessageStorage));

        var stopwatch = Stopwatch.StartNew();

        try
        {
            if (string.IsNullOrEmpty(clientId))
                throw new ArgumentException("Client ID cannot be null or empty", nameof(clientId));

            _willMessages.TryGetValue(clientId, out var registration);

            stopwatch.Stop();
            UpdateStatistics(WillMessageStorageOperation.Store, stopwatch.ElapsedMilliseconds, true, isQuery: true);

            _logger.LogTrace("Retrieved will message for client: {ClientId}, found: {Found}", 
                clientId, registration != null);

            return Task.FromResult(registration?.Clone());
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            UpdateStatistics(WillMessageStorageOperation.Store, stopwatch.ElapsedMilliseconds, false, isQuery: true);

            _logger.LogError(ex, "Error retrieving will message for client: {ClientId}", clientId);
            return Task.FromResult<WillMessageRegistration?>(null);
        }
    }

    /// <summary>
    /// 删除遗嘱消息注册信息
    /// </summary>
    public Task<WillMessageStorageResult> DeleteWillMessageAsync(string clientId, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(InMemoryWillMessageStorage));

        var stopwatch = Stopwatch.StartNew();

        try
        {
            if (string.IsNullOrEmpty(clientId))
                throw new ArgumentException("Client ID cannot be null or empty", nameof(clientId));

            var removed = _willMessages.TryRemove(clientId, out _);

            stopwatch.Stop();
            UpdateStatistics(WillMessageStorageOperation.Delete, stopwatch.ElapsedMilliseconds, true);

            _logger.LogTrace("Deleted will message for client: {ClientId}, existed: {Existed}", 
                clientId, removed);

            return Task.FromResult(WillMessageStorageResult.Success(clientId, WillMessageStorageOperation.Delete));
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            UpdateStatistics(WillMessageStorageOperation.Delete, stopwatch.ElapsedMilliseconds, false);

            _logger.LogError(ex, "Error deleting will message for client: {ClientId}", clientId);
            return Task.FromResult(WillMessageStorageResult.Failure(clientId, WillMessageStorageOperation.Delete, ex.Message));
        }
    }

    /// <summary>
    /// 检查遗嘱消息是否存在
    /// </summary>
    public Task<bool> ExistsAsync(string clientId, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(InMemoryWillMessageStorage));

        try
        {
            if (string.IsNullOrEmpty(clientId))
                return Task.FromResult(false);

            var exists = _willMessages.ContainsKey(clientId);
            
            UpdateStatistics(WillMessageStorageOperation.Store, 0, true, isQuery: true);

            return Task.FromResult(exists);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking will message existence for client: {ClientId}", clientId);
            UpdateStatistics(WillMessageStorageOperation.Store, 0, false, isQuery: true);
            return Task.FromResult(false);
        }
    }

    /// <summary>
    /// 获取所有遗嘱消息注册信息
    /// </summary>
    public Task<IList<WillMessageRegistration>> GetAllWillMessagesAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(InMemoryWillMessageStorage));

        try
        {
            var allWillMessages = _willMessages.Values.Select(w => w.Clone()).ToList();
            
            UpdateStatistics(WillMessageStorageOperation.Store, 0, true, isQuery: true);

            _logger.LogTrace("Retrieved all will messages, count: {Count}", allWillMessages.Count);

            return Task.FromResult<IList<WillMessageRegistration>>(allWillMessages);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all will messages");
            UpdateStatistics(WillMessageStorageOperation.Store, 0, false, isQuery: true);
            return Task.FromResult<IList<WillMessageRegistration>>(new List<WillMessageRegistration>());
        }
    }

    /// <summary>
    /// 获取过期的遗嘱消息注册信息
    /// </summary>
    public Task<IList<WillMessageRegistration>> GetExpiredWillMessagesAsync(DateTime expirationTime, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(InMemoryWillMessageStorage));

        try
        {
            var expiredWillMessages = _willMessages.Values
                .Where(w => w.ExpiresAt.HasValue && w.ExpiresAt.Value <= expirationTime)
                .Select(w => w.Clone())
                .ToList();

            UpdateStatistics(WillMessageStorageOperation.Store, 0, true, isQuery: true);

            _logger.LogTrace("Retrieved expired will messages, count: {Count}, expiration time: {ExpirationTime}", 
                expiredWillMessages.Count, expirationTime);

            return Task.FromResult<IList<WillMessageRegistration>>(expiredWillMessages);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving expired will messages");
            UpdateStatistics(WillMessageStorageOperation.Store, 0, false, isQuery: true);
            return Task.FromResult<IList<WillMessageRegistration>>(new List<WillMessageRegistration>());
        }
    }

    /// <summary>
    /// 批量删除遗嘱消息注册信息
    /// </summary>
    public Task<WillMessageBatchStorageResult> DeleteWillMessagesAsync(IList<string> clientIds, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(InMemoryWillMessageStorage));

        var stopwatch = Stopwatch.StartNew();

        try
        {
            if (clientIds == null)
                throw new ArgumentNullException(nameof(clientIds));

            var successfulClientIds = new List<string>();
            var failedClientIds = new List<string>();

            foreach (var clientId in clientIds)
            {
                try
                {
                    if (!string.IsNullOrEmpty(clientId) && _willMessages.TryRemove(clientId, out _))
                    {
                        successfulClientIds.Add(clientId);
                    }
                    else
                    {
                        failedClientIds.Add(clientId);
                    }
                }
                catch
                {
                    failedClientIds.Add(clientId);
                }
            }

            stopwatch.Stop();
            UpdateStatistics(WillMessageStorageOperation.BatchDelete, stopwatch.ElapsedMilliseconds, successfulClientIds.Count > 0);

            _logger.LogTrace("Batch deleted will messages, successful: {SuccessfulCount}, failed: {FailedCount}", 
                successfulClientIds.Count, failedClientIds.Count);

            if (failedClientIds.Count == 0)
            {
                return Task.FromResult(WillMessageBatchStorageResult.Success(WillMessageStorageOperation.BatchDelete, successfulClientIds));
            }
            else if (successfulClientIds.Count > 0)
            {
                return Task.FromResult(WillMessageBatchStorageResult.PartialSuccess(
                    WillMessageStorageOperation.BatchDelete, successfulClientIds, failedClientIds, 
                    $"Failed to delete {failedClientIds.Count} will messages"));
            }
            else
            {
                return Task.FromResult(WillMessageBatchStorageResult.Failure(
                    WillMessageStorageOperation.BatchDelete, failedClientIds, "Failed to delete all will messages"));
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            UpdateStatistics(WillMessageStorageOperation.BatchDelete, stopwatch.ElapsedMilliseconds, false);

            _logger.LogError(ex, "Error batch deleting will messages");
            return Task.FromResult(WillMessageBatchStorageResult.Failure(
                WillMessageStorageOperation.BatchDelete, clientIds, ex.Message));
        }
    }

    /// <summary>
    /// 获取存储统计信息
    /// </summary>
    public Task<WillMessageStorageStatistics> GetStatisticsAsync()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(InMemoryWillMessageStorage));

        try
        {
            lock (_statsLock)
            {
                var statistics = new WillMessageStorageStatistics
                {
                    TotalWillMessages = _willMessages.Count,
                    TotalStoreOperations = _totalStoreOperations,
                    TotalDeleteOperations = _totalDeleteOperations,
                    TotalQueryOperations = _totalQueryOperations,
                    TotalFailedOperations = _totalFailedOperations,
                    AverageOperationLatencyMs = _operationLatencies.Count > 0 ? _operationLatencies.Average() : 0,
                    LastOperationTime = _lastOperationTime,
                    StartTime = _startTime
                };

                // 按客户端ID分组统计
                foreach (var willMessage in _willMessages.Values)
                {
                    statistics.WillMessagesByClientId[willMessage.ClientId] = 1;
                }

                // 按主题分组统计
                var topicGroups = _willMessages.Values.GroupBy(w => w.Topic);
                foreach (var group in topicGroups)
                {
                    statistics.WillMessagesByTopic[group.Key] = group.Count();
                }

                return Task.FromResult(statistics);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting storage statistics");
            return Task.FromResult(new WillMessageStorageStatistics
            {
                StartTime = _startTime,
                LastOperationTime = _lastOperationTime
            });
        }
    }

    /// <summary>
    /// 清理所有遗嘱消息
    /// </summary>
    public Task<WillMessageStorageResult> ClearAllAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(InMemoryWillMessageStorage));

        var stopwatch = Stopwatch.StartNew();

        try
        {
            var count = _willMessages.Count;
            _willMessages.Clear();

            stopwatch.Stop();
            UpdateStatistics(WillMessageStorageOperation.ClearAll, stopwatch.ElapsedMilliseconds, true);

            _logger.LogInformation("Cleared all will messages, count: {Count}", count);

            return Task.FromResult(WillMessageStorageResult.Success("all", WillMessageStorageOperation.ClearAll));
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            UpdateStatistics(WillMessageStorageOperation.ClearAll, stopwatch.ElapsedMilliseconds, false);

            _logger.LogError(ex, "Error clearing all will messages");
            return Task.FromResult(WillMessageStorageResult.Failure("all", WillMessageStorageOperation.ClearAll, ex.Message));
        }
    }

    /// <summary>
    /// 更新统计信息
    /// </summary>
    private void UpdateStatistics(WillMessageStorageOperation operation, long latencyMs, bool isSuccess, bool isQuery = false)
    {
        lock (_statsLock)
        {
            _lastOperationTime = DateTime.UtcNow;

            if (isQuery)
            {
                _totalQueryOperations++;
            }
            else
            {
                switch (operation)
                {
                    case WillMessageStorageOperation.Store:
                        _totalStoreOperations++;
                        break;
                    case WillMessageStorageOperation.Delete:
                    case WillMessageStorageOperation.BatchDelete:
                    case WillMessageStorageOperation.ClearAll:
                        _totalDeleteOperations++;
                        break;
                }
            }

            if (!isSuccess)
            {
                _totalFailedOperations++;
            }

            if (latencyMs > 0)
            {
                _operationLatencies.Add(latencyMs);

                // 保持最近1000个延迟记录
                if (_operationLatencies.Count > 1000)
                {
                    _operationLatencies.RemoveAt(0);
                }
            }
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;
        _willMessages.Clear();

        _logger.LogInformation("InMemoryWillMessageStorage disposed");
    }
}
