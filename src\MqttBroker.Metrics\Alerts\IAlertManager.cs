using MqttBroker.Metrics.Models;

namespace MqttBroker.Metrics.Alerts;

/// <summary>
/// 告警管理器接口
/// </summary>
public interface IAlertManager
{
    /// <summary>
    /// 添加告警规则
    /// </summary>
    /// <param name="rule">告警规则</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task AddRuleAsync(AlertRule rule, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新告警规则
    /// </summary>
    /// <param name="rule">告警规则</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task UpdateRuleAsync(AlertRule rule, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除告警规则
    /// </summary>
    /// <param name="ruleId">规则 ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task RemoveRuleAsync(string ruleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取所有告警规则
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>告警规则列表</returns>
    Task<IEnumerable<AlertRule>> GetRulesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取指定告警规则
    /// </summary>
    /// <param name="ruleId">规则 ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>告警规则</returns>
    Task<AlertRule?> GetRuleAsync(string ruleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 评估指标并触发告警
    /// </summary>
    /// <param name="metrics">性能指标</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task EvaluateMetricsAsync(PerformanceMetrics metrics, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取活跃告警
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>活跃告警列表</returns>
    Task<IEnumerable<Alert>> GetActiveAlertsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取告警历史
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>告警历史列表</returns>
    Task<IEnumerable<Alert>> GetAlertHistoryAsync(DateTime startTime, DateTime endTime, CancellationToken cancellationToken = default);

    /// <summary>
    /// 确认告警
    /// </summary>
    /// <param name="alertId">告警 ID</param>
    /// <param name="acknowledgedBy">确认人</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task AcknowledgeAlertAsync(string alertId, string acknowledgedBy, CancellationToken cancellationToken = default);

    /// <summary>
    /// 解决告警
    /// </summary>
    /// <param name="alertId">告警 ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task ResolveAlertAsync(string alertId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取告警统计信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>告警统计信息</returns>
    Task<AlertStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 告警触发事件
    /// </summary>
    event EventHandler<AlertTriggeredEventArgs>? AlertTriggered;

    /// <summary>
    /// 告警解决事件
    /// </summary>
    event EventHandler<AlertResolvedEventArgs>? AlertResolved;
}

/// <summary>
/// 告警规则评估器接口
/// </summary>
public interface IAlertRuleEvaluator
{
    /// <summary>
    /// 评估告警规则
    /// </summary>
    /// <param name="rule">告警规则</param>
    /// <param name="metrics">性能指标</param>
    /// <returns>评估结果</returns>
    AlertEvaluationResult EvaluateRule(AlertRule rule, PerformanceMetrics metrics);

    /// <summary>
    /// 检查规则是否支持
    /// </summary>
    /// <param name="rule">告警规则</param>
    /// <returns>是否支持</returns>
    bool SupportsRule(AlertRule rule);
}

/// <summary>
/// 告警存储接口
/// </summary>
public interface IAlertStorage
{
    /// <summary>
    /// 存储告警规则
    /// </summary>
    /// <param name="rule">告警规则</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task StoreRuleAsync(AlertRule rule, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除告警规则
    /// </summary>
    /// <param name="ruleId">规则 ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task DeleteRuleAsync(string ruleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取所有告警规则
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>告警规则列表</returns>
    Task<IEnumerable<AlertRule>> GetRulesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取指定告警规则
    /// </summary>
    /// <param name="ruleId">规则 ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>告警规则</returns>
    Task<AlertRule?> GetRuleAsync(string ruleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 存储告警
    /// </summary>
    /// <param name="alert">告警</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task StoreAlertAsync(Alert alert, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新告警
    /// </summary>
    /// <param name="alert">告警</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task UpdateAlertAsync(Alert alert, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取活跃告警
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>活跃告警列表</returns>
    Task<IEnumerable<Alert>> GetActiveAlertsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取告警历史
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>告警历史列表</returns>
    Task<IEnumerable<Alert>> GetAlertHistoryAsync(DateTime startTime, DateTime endTime, CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理过期告警
    /// </summary>
    /// <param name="retentionPeriod">保留期间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理的告警数量</returns>
    Task<int> CleanupExpiredAlertsAsync(TimeSpan retentionPeriod, CancellationToken cancellationToken = default);
}

/// <summary>
/// 告警评估结果
/// </summary>
public class AlertEvaluationResult
{
    /// <summary>
    /// 是否触发告警
    /// </summary>
    public bool IsTriggered { get; set; }

    /// <summary>
    /// 当前值
    /// </summary>
    public double CurrentValue { get; set; }

    /// <summary>
    /// 阈值
    /// </summary>
    public double Threshold { get; set; }

    /// <summary>
    /// 评估消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 附加数据
    /// </summary>
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}

/// <summary>
/// 告警触发事件参数
/// </summary>
public class AlertTriggeredEventArgs : EventArgs
{
    /// <summary>
    /// 告警
    /// </summary>
    public Alert Alert { get; }

    /// <summary>
    /// 告警规则
    /// </summary>
    public AlertRule Rule { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    public AlertTriggeredEventArgs(Alert alert, AlertRule rule)
    {
        Alert = alert;
        Rule = rule;
    }
}

/// <summary>
/// 告警解决事件参数
/// </summary>
public class AlertResolvedEventArgs : EventArgs
{
    /// <summary>
    /// 告警
    /// </summary>
    public Alert Alert { get; }

    /// <summary>
    /// 告警规则
    /// </summary>
    public AlertRule Rule { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    public AlertResolvedEventArgs(Alert alert, AlertRule rule)
    {
        Alert = alert;
        Rule = rule;
    }
}
