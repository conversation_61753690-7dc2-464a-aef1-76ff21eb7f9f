using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace MqttBroker.Core.QoS;

/// <summary>
/// QoS 服务注册扩展
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加 QoS 处理服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureManager">QoS 管理器配置</param>
    /// <param name="configureAcknowledgment">消息确认配置</param>
    /// <param name="configureRetransmission">消息重传配置</param>
    /// <param name="configureDeduplication">消息去重配置</param>
    /// <param name="configurePerformance">性能配置</param>
    /// <param name="configureLogging">日志配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddQoSProcessing(
        this IServiceCollection services,
        Action<QoSManagerOptions>? configureManager = null,
        Action<MessageAcknowledgmentOptions>? configureAcknowledgment = null,
        Action<MessageRetransmissionOptions>? configureRetransmission = null,
        Action<MessageDeduplicationOptions>? configureDeduplication = null,
        Action<QoSPerformanceOptions>? configurePerformance = null,
        Action<QoSLoggingOptions>? configureLogging = null)
    {
        // 配置选项
        var configuration = new QoSConfiguration();
        
        configureManager?.Invoke(configuration.Manager);
        configureAcknowledgment?.Invoke(configuration.Acknowledgment);
        configureRetransmission?.Invoke(configuration.Retransmission);
        configureDeduplication?.Invoke(configuration.Deduplication);
        configurePerformance?.Invoke(configuration.Performance);
        configureLogging?.Invoke(configuration.Logging);

        // 验证配置
        configuration.Validate();

        // 注册配置
        services.Configure<QoSConfiguration>(options =>
        {
            options.Manager = configuration.Manager;
            options.Acknowledgment = configuration.Acknowledgment;
            options.Retransmission = configuration.Retransmission;
            options.Deduplication = configuration.Deduplication;
            options.Performance = configuration.Performance;
            options.Logging = configuration.Logging;
        });

        services.Configure<QoSManagerOptions>(options =>
        {
            options.EnableQoSProcessing = configuration.Manager.EnableQoSProcessing;
            options.MaxSupportedQoSLevel = configuration.Manager.MaxSupportedQoSLevel;
            options.EnableStatistics = configuration.Manager.EnableStatistics;
            options.StatisticsUpdateIntervalMs = configuration.Manager.StatisticsUpdateIntervalMs;
            options.EnablePerformanceMonitoring = configuration.Manager.EnablePerformanceMonitoring;
            options.MaxConcurrentQoSProcessing = configuration.Manager.MaxConcurrentQoSProcessing;
            options.QoSProcessingTimeoutMs = configuration.Manager.QoSProcessingTimeoutMs;
        });

        services.Configure<MessageAcknowledgmentOptions>(options =>
        {
            options.MaxPendingMessagesPerClient = configuration.Acknowledgment.MaxPendingMessagesPerClient;
            options.AcknowledgmentTimeoutMs = configuration.Acknowledgment.AcknowledgmentTimeoutMs;
            options.EnableAcknowledgmentStatistics = configuration.Acknowledgment.EnableAcknowledgmentStatistics;
            options.StatisticsCleanupIntervalMs = configuration.Acknowledgment.StatisticsCleanupIntervalMs;
            options.AutoCleanupOnClientDisconnect = configuration.Acknowledgment.AutoCleanupOnClientDisconnect;
            options.QoS2MaxWaitTimeMs = configuration.Acknowledgment.QoS2MaxWaitTimeMs;
        });

        services.Configure<MessageRetransmissionOptions>(options =>
        {
            options.EnableRetransmission = configuration.Retransmission.EnableRetransmission;
            options.RetransmissionCheckIntervalMs = configuration.Retransmission.RetransmissionCheckIntervalMs;
            options.RetransmissionTimeoutMs = configuration.Retransmission.RetransmissionTimeoutMs;
            options.MaxRetransmissionAttempts = configuration.Retransmission.MaxRetransmissionAttempts;
            options.RetransmissionBackoffFactor = configuration.Retransmission.RetransmissionBackoffFactor;
            options.MaxRetransmissionIntervalMs = configuration.Retransmission.MaxRetransmissionIntervalMs;
            options.EnableExponentialBackoff = configuration.Retransmission.EnableExponentialBackoff;
            options.RetransmitOnClientReconnect = configuration.Retransmission.RetransmitOnClientReconnect;
            options.MoveToDeadLetterQueueOnFailure = configuration.Retransmission.MoveToDeadLetterQueueOnFailure;
            options.MaxConcurrentRetransmissions = configuration.Retransmission.MaxConcurrentRetransmissions;
        });

        services.Configure<MessageDeduplicationOptions>(options =>
        {
            options.EnableDeduplication = configuration.Deduplication.EnableDeduplication;
            options.DefaultRecordExpirationMs = configuration.Deduplication.DefaultRecordExpirationMs;
            options.ExpiredRecordCleanupIntervalMs = configuration.Deduplication.ExpiredRecordCleanupIntervalMs;
            options.MaxProcessedRecordsPerClient = configuration.Deduplication.MaxProcessedRecordsPerClient;
            options.UseContentHashForDeduplication = configuration.Deduplication.UseContentHashForDeduplication;
            options.RetainRecordsOnClientDisconnect = configuration.Deduplication.RetainRecordsOnClientDisconnect;
            options.MaxMemoryUsageBytes = configuration.Deduplication.MaxMemoryUsageBytes;
            options.EnableDeduplicationStatistics = configuration.Deduplication.EnableDeduplicationStatistics;
            options.MaxBatchCleanupSize = configuration.Deduplication.MaxBatchCleanupSize;
        });

        // 注册服务
        services.TryAddSingleton<IMessageAcknowledgmentService, MessageAcknowledgmentService>();
        services.TryAddSingleton<IMessageRetransmissionService, MessageRetransmissionService>();
        services.TryAddSingleton<IMessageDeduplicationService, MessageDeduplicationService>();
        services.TryAddSingleton<IQoSManager, QoSManager>();

        return services;
    }

    /// <summary>
    /// 添加 QoS 处理服务（使用默认配置）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddQoSProcessing(this IServiceCollection services)
    {
        return services.AddQoSProcessing(
            manager => { },
            acknowledgment => { },
            retransmission => { },
            deduplication => { },
            performance => { },
            logging => { });
    }

    /// <summary>
    /// 添加高性能 QoS 处理服务配置
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddHighPerformanceQoSProcessing(this IServiceCollection services)
    {
        return services.AddQoSProcessing(
            manager =>
            {
                manager.EnableQoSProcessing = true;
                manager.MaxSupportedQoSLevel = Protocol.MqttQoSLevel.ExactlyOnce;
                manager.MaxConcurrentQoSProcessing = 20000;
                manager.QoSProcessingTimeoutMs = 15000;
                manager.EnableStatistics = true;
                manager.EnablePerformanceMonitoring = true;
            },
            acknowledgment =>
            {
                acknowledgment.MaxPendingMessagesPerClient = 2000;
                acknowledgment.AcknowledgmentTimeoutMs = 30000;
                acknowledgment.AutoCleanupOnClientDisconnect = true;
                acknowledgment.QoS2MaxWaitTimeMs = 60000;
            },
            retransmission =>
            {
                retransmission.EnableRetransmission = true;
                retransmission.RetransmissionTimeoutMs = 30000;
                retransmission.MaxRetransmissionAttempts = 5;
                retransmission.EnableExponentialBackoff = true;
                retransmission.MaxConcurrentRetransmissions = 200;
            },
            deduplication =>
            {
                deduplication.EnableDeduplication = true;
                deduplication.DefaultRecordExpirationMs = 1800000; // 30分钟
                deduplication.MaxProcessedRecordsPerClient = 20000;
                deduplication.UseContentHashForDeduplication = true;
                deduplication.MaxMemoryUsageBytes = 200 * 1024 * 1024; // 200MB
            },
            performance =>
            {
                performance.EnableMemoryPoolOptimization = true;
                performance.EnableBatchProcessing = true;
                performance.MaxBatchSize = 200;
                performance.EnableParallelProcessing = true;
                performance.MaxParallelism = Environment.ProcessorCount * 4;
            },
            logging =>
            {
                logging.EnableVerboseLogging = false;
                logging.LogPerformanceMetrics = true;
                logging.LogThresholdMs = 50;
                logging.LogStatistics = true;
            });
    }

    /// <summary>
    /// 添加基础 QoS 处理服务配置（适用于资源受限环境）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddBasicQoSProcessing(this IServiceCollection services)
    {
        return services.AddQoSProcessing(
            manager =>
            {
                manager.EnableQoSProcessing = true;
                manager.MaxSupportedQoSLevel = Protocol.MqttQoSLevel.AtLeastOnce;
                manager.MaxConcurrentQoSProcessing = 1000;
                manager.QoSProcessingTimeoutMs = 60000;
                manager.EnableStatistics = false;
                manager.EnablePerformanceMonitoring = false;
            },
            acknowledgment =>
            {
                acknowledgment.MaxPendingMessagesPerClient = 100;
                acknowledgment.AcknowledgmentTimeoutMs = 120000;
                acknowledgment.AutoCleanupOnClientDisconnect = true;
            },
            retransmission =>
            {
                retransmission.EnableRetransmission = true;
                retransmission.RetransmissionTimeoutMs = 120000;
                retransmission.MaxRetransmissionAttempts = 2;
                retransmission.EnableExponentialBackoff = false;
                retransmission.MaxConcurrentRetransmissions = 50;
            },
            deduplication =>
            {
                deduplication.EnableDeduplication = false; // 禁用去重以节省资源
            },
            performance =>
            {
                performance.EnableMemoryPoolOptimization = false;
                performance.EnableBatchProcessing = false;
                performance.EnableParallelProcessing = false;
            },
            logging =>
            {
                logging.EnableVerboseLogging = false;
                logging.LogPerformanceMetrics = false;
                logging.LogStatistics = false;
            });
    }
}
