using System.Collections.Concurrent;
using System.Diagnostics;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MqttBroker.Core.Client;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Topic;

namespace MqttBroker.Core.Message;

/// <summary>
/// 消息路由引擎实现
/// </summary>
public class MessageRoutingEngine : IMessageRoutingEngine, IDisposable
{
    private readonly ITopicSubscriptionManager _subscriptionManager;
    private readonly IMessageFilterManager _filterManager;
    private readonly IMessagePersistenceService _persistenceService;
    private readonly IDeadLetterQueueService _deadLetterService;
    private readonly ILogger<MessageRoutingEngine> _logger;
    private readonly MessageRoutingOptions _options;
    private readonly ConcurrentDictionary<string, long> _routingCounters;
    private readonly object _statsLock = new();
    private MessageRoutingStatistics _statistics;
    private bool _disposed;

    /// <summary>
    /// 消息路由成功事件
    /// </summary>
    public event EventHandler<MessageRoutedEventArgs>? MessageRouted;

    /// <summary>
    /// 消息路由失败事件
    /// </summary>
    public event EventHandler<MessageRoutingFailedEventArgs>? MessageRoutingFailed;

    /// <summary>
    /// 离线消息存储事件
    /// </summary>
    public event EventHandler<OfflineMessageStoredEventArgs>? OfflineMessageStored;

    /// <summary>
    /// 初始化消息路由引擎
    /// </summary>
    /// <param name="subscriptionManager">订阅管理器</param>
    /// <param name="filterManager">过滤器管理器</param>
    /// <param name="persistenceService">持久化服务</param>
    /// <param name="deadLetterService">死信队列服务</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="options">配置选项</param>
    public MessageRoutingEngine(
        ITopicSubscriptionManager subscriptionManager,
        IMessageFilterManager filterManager,
        IMessagePersistenceService persistenceService,
        IDeadLetterQueueService deadLetterService,
        ILogger<MessageRoutingEngine> logger,
        IOptions<MessageRoutingOptions> options)
    {
        _subscriptionManager = subscriptionManager ?? throw new ArgumentNullException(nameof(subscriptionManager));
        _filterManager = filterManager ?? throw new ArgumentNullException(nameof(filterManager));
        _persistenceService = persistenceService ?? throw new ArgumentNullException(nameof(persistenceService));
        _deadLetterService = deadLetterService ?? throw new ArgumentNullException(nameof(deadLetterService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? new MessageRoutingOptions();
        
        _routingCounters = new ConcurrentDictionary<string, long>();
        _statistics = new MessageRoutingStatistics();
    }

    /// <summary>
    /// 路由消息到订阅者
    /// </summary>
    /// <param name="publishPacket">发布数据包</param>
    /// <param name="publisherClientId">发布者客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>路由结果</returns>
    public async Task<MessageRoutingResult> RouteMessageAsync(MqttPublishPacket publishPacket, string? publisherClientId = null, CancellationToken cancellationToken = default)
    {
        if (publishPacket == null)
            throw new ArgumentNullException(nameof(publishPacket));

        var stopwatch = Stopwatch.StartNew();
        var topicName = publishPacket.Topic;

        try
        {
            _logger.LogDebug("Routing message to topic: {TopicName} from publisher: {PublisherId}", 
                topicName, publisherClientId ?? "system");

            // 获取匹配的订阅者
            var subscribers = await _subscriptionManager.GetSubscribersAsync(topicName, cancellationToken);
            
            if (subscribers.Count == 0)
            {
                _logger.LogDebug("No subscribers found for topic: {TopicName}", topicName);
                stopwatch.Stop();
                
                var emptyResult = MessageRoutingResult.Success(topicName, 0, 0, 0, 0, stopwatch.ElapsedMilliseconds);
                UpdateStatistics(emptyResult);
                return emptyResult;
            }

            _logger.LogDebug("Found {SubscriberCount} subscribers for topic: {TopicName}", 
                subscribers.Count, topicName);

            // 并行处理订阅者
            var routingTasks = subscribers.Select(subscriber => 
                RouteToSubscriberAsync(publishPacket, subscriber, publisherClientId, cancellationToken)).ToArray();

            var routingResults = await Task.WhenAll(routingTasks);
            
            stopwatch.Stop();

            // 统计结果
            var onlineDeliveries = routingResults.Count(r => r.RoutingType == MessageRoutingType.OnlineDelivery && r.IsSuccess);
            var offlineStorages = routingResults.Count(r => r.RoutingType == MessageRoutingType.OfflineStorage && r.IsSuccess);
            var failedDeliveries = routingResults.Count(r => r.RoutingType == MessageRoutingType.Failed);
            var filteredCount = routingResults.Count(r => r.RoutingType == MessageRoutingType.Filtered);

            var result = MessageRoutingResult.Success(topicName, onlineDeliveries, offlineStorages, failedDeliveries, filteredCount, stopwatch.ElapsedMilliseconds);
            result.RoutingDetails = routingResults.ToList();

            // 更新统计信息
            UpdateStatistics(result);

            // 触发事件
            MessageRouted?.Invoke(this, new MessageRoutedEventArgs(result, publishPacket, publisherClientId));

            _logger.LogDebug("Message routing completed for topic: {TopicName}. Online: {OnlineCount}, Offline: {OfflineCount}, Failed: {FailedCount}, Filtered: {FilteredCount} in {ElapsedMs}ms", 
                topicName, onlineDeliveries, offlineStorages, failedDeliveries, filteredCount, stopwatch.ElapsedMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error routing message to topic: {TopicName}", topicName);
            
            var errorResult = MessageRoutingResult.Failure(topicName, ex.Message);
            errorResult.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
            
            UpdateStatistics(errorResult);
            MessageRoutingFailed?.Invoke(this, new MessageRoutingFailedEventArgs(topicName, ex.Message, publishPacket, publisherClientId, ex));
            
            return errorResult;
        }
    }

    /// <summary>
    /// 批量路由消息
    /// </summary>
    /// <param name="messages">消息列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>路由结果列表</returns>
    public async Task<IList<MessageRoutingResult>> RouteMessagesAsync(IList<MessageRoutingRequest> messages, CancellationToken cancellationToken = default)
    {
        if (messages == null)
            throw new ArgumentNullException(nameof(messages));

        _logger.LogDebug("Routing batch of {Count} messages", messages.Count);

        // 根据优先级排序
        var sortedMessages = messages.OrderByDescending(m => m.Priority).ToList();

        var routingTasks = sortedMessages.Select(message => 
            RouteMessageAsync(message.PublishPacket, message.PublisherClientId, cancellationToken)).ToArray();

        var results = await Task.WhenAll(routingTasks);
        
        _logger.LogDebug("Batch routing completed. {SuccessCount}/{TotalCount} messages routed successfully", 
            results.Count(r => r.IsSuccess), results.Length);

        return results.ToList();
    }

    /// <summary>
    /// 处理离线消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理结果</returns>
    public async Task<OfflineMessageResult> ProcessOfflineMessagesAsync(string clientId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(clientId))
            throw new ArgumentException("Client ID cannot be null or empty", nameof(clientId));

        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogDebug("Processing offline messages for client: {ClientId}", clientId);

            // 获取离线消息
            var offlineMessages = await _persistenceService.GetOfflineMessagesAsync(clientId, _options.MaxOfflineMessagesPerClient, cancellationToken);
            
            if (offlineMessages.Count == 0)
            {
                _logger.LogDebug("No offline messages found for client: {ClientId}", clientId);
                stopwatch.Stop();
                
                return new OfflineMessageResult
                {
                    ClientId = clientId,
                    IsSuccess = true,
                    ProcessedMessages = 0,
                    DeliveredMessages = 0,
                    FailedMessages = 0,
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                };
            }

            _logger.LogDebug("Found {MessageCount} offline messages for client: {ClientId}", 
                offlineMessages.Count, clientId);

            var deliveredCount = 0;
            var failedCount = 0;
            var processedMessageIds = new List<string>();

            foreach (var offlineMessage in offlineMessages)
            {
                try
                {
                    var publishPacket = offlineMessage.ToPublishPacket();
                    
                    // 尝试直接分发给客户端
                    var subscriber = await _subscriptionManager.GetSubscriberAsync(clientId, offlineMessage.TopicName, cancellationToken);
                    
                    if (subscriber != null && subscriber.Client?.State == MqttClientState.Authenticated)
                    {
                        await subscriber.Client.SendPacketAsync(publishPacket, cancellationToken);
                        deliveredCount++;
                        processedMessageIds.Add(offlineMessage.MessageId);
                        
                        _logger.LogTrace("Delivered offline message {MessageId} to client: {ClientId}", 
                            offlineMessage.MessageId, clientId);
                    }
                    else
                    {
                        _logger.LogTrace("Client {ClientId} is not available for offline message delivery", clientId);
                        break; // 客户端不可用，停止处理剩余消息
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to deliver offline message {MessageId} to client: {ClientId}", 
                        offlineMessage.MessageId, clientId);
                    
                    failedCount++;
                    
                    // 将失败的消息添加到死信队列
                    var deadLetterMessage = DeadLetterMessage.Create(
                        offlineMessage.ToPublishPacket(), 
                        clientId, 
                        ex.Message, 
                        DeadLetterReason.ClientRejected, 
                        offlineMessage.RetryCount + 1);
                    
                    await _deadLetterService.AddToDeadLetterQueueAsync(deadLetterMessage, cancellationToken);
                    processedMessageIds.Add(offlineMessage.MessageId);
                }
            }

            // 删除已处理的离线消息
            if (processedMessageIds.Count > 0)
            {
                await _persistenceService.DeleteOfflineMessagesAsync(processedMessageIds, cancellationToken);
            }

            stopwatch.Stop();

            var result = new OfflineMessageResult
            {
                ClientId = clientId,
                IsSuccess = true,
                ProcessedMessages = offlineMessages.Count,
                DeliveredMessages = deliveredCount,
                FailedMessages = failedCount,
                ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
            };

            _logger.LogDebug("Offline message processing completed for client: {ClientId}. Delivered: {DeliveredCount}, Failed: {FailedCount} in {ElapsedMs}ms", 
                clientId, deliveredCount, failedCount, stopwatch.ElapsedMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error processing offline messages for client: {ClientId}", clientId);
            
            return new OfflineMessageResult
            {
                ClientId = clientId,
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
            };
        }
    }

    /// <summary>
    /// 获取路由统计信息
    /// </summary>
    /// <returns>路由统计信息</returns>
    public Task<MessageRoutingStatistics> GetStatisticsAsync()
    {
        lock (_statsLock)
        {
            // 创建统计信息的副本
            var stats = new MessageRoutingStatistics
            {
                TotalMessages = _statistics.TotalMessages,
                OnlineDeliveries = _statistics.OnlineDeliveries,
                OfflineStorages = _statistics.OfflineStorages,
                FailedRoutings = _statistics.FailedRoutings,
                FilteredMessages = _statistics.FilteredMessages,
                AverageRoutingLatency = _statistics.AverageRoutingLatency,
                MaxRoutingLatency = _statistics.MaxRoutingLatency,
                MessagesPerSecond = _statistics.MessagesPerSecond,
                DeadLetterMessages = _statistics.DeadLetterMessages
            };

            return Task.FromResult(stats);
        }
    }

    #region 私有方法

    /// <summary>
    /// 路由消息到单个订阅者
    /// </summary>
    /// <param name="publishPacket">发布数据包</param>
    /// <param name="subscriber">订阅者</param>
    /// <param name="publisherClientId">发布者客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅者路由结果</returns>
    private async Task<SubscriberRoutingResult> RouteToSubscriberAsync(MqttPublishPacket publishPacket, TopicSubscriber subscriber, string? publisherClientId, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            // 应用消息过滤器
            var filterContext = MessageFilterContext.Create(publishPacket, subscriber, publisherClientId);
            var filterResult = await _filterManager.ApplyFiltersAsync(filterContext, cancellationToken);

            if (!filterResult.IsAllowed)
            {
                stopwatch.Stop();
                _logger.LogTrace("Message filtered out for subscriber {ClientId}: {Reason}",
                    subscriber.ClientId, filterResult.Reason);

                return new SubscriberRoutingResult
                {
                    ClientId = subscriber.ClientId,
                    TopicFilter = subscriber.TopicFilter,
                    RoutingType = MessageRoutingType.Filtered,
                    IsSuccess = true,
                    ErrorMessage = filterResult.Reason,
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                };
            }

            // 使用过滤器修改后的数据包（如果有）
            var effectivePacket = filterResult.ModifiedPacket ?? publishPacket;

            // 确定使用的QoS级别（取发布QoS和订阅QoS的最小值）
            var effectiveQoS = (MqttQoSLevel)Math.Min((int)effectivePacket.QoSLevel, (int)subscriber.QoSLevel);

            // 检查客户端是否在线
            if (subscriber.Client?.State != MqttClientState.Authenticated)
            {
                // 客户端离线，根据QoS级别决定是否存储
                if (effectiveQoS > MqttQoSLevel.AtMostOnce)
                {
                    var storageResult = await _persistenceService.StoreOfflineMessageAsync(
                        subscriber.ClientId, effectivePacket, effectiveQoS, cancellationToken);

                    stopwatch.Stop();

                    if (storageResult.IsSuccess)
                    {
                        _logger.LogTrace("Message stored offline for subscriber {ClientId}", subscriber.ClientId);

                        // 触发离线消息存储事件
                        OfflineMessageStored?.Invoke(this, new OfflineMessageStoredEventArgs(
                            subscriber.ClientId,
                            effectivePacket.Topic,
                            storageResult.MessageId!,
                            effectiveQoS,
                            effectivePacket.Payload.Length,
                            storageResult.StoredAt));

                        return new SubscriberRoutingResult
                        {
                            ClientId = subscriber.ClientId,
                            TopicFilter = subscriber.TopicFilter,
                            RoutingType = MessageRoutingType.OfflineStorage,
                            IsSuccess = true,
                            QoSLevel = effectiveQoS,
                            ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                        };
                    }
                    else
                    {
                        _logger.LogWarning("Failed to store offline message for subscriber {ClientId}: {Error}",
                            subscriber.ClientId, storageResult.ErrorMessage);

                        return new SubscriberRoutingResult
                        {
                            ClientId = subscriber.ClientId,
                            TopicFilter = subscriber.TopicFilter,
                            RoutingType = MessageRoutingType.Failed,
                            IsSuccess = false,
                            ErrorMessage = storageResult.ErrorMessage,
                            ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                        };
                    }
                }
                else
                {
                    // QoS 0 消息不存储，直接丢弃
                    stopwatch.Stop();
                    _logger.LogTrace("QoS 0 message discarded for offline subscriber {ClientId}", subscriber.ClientId);

                    return new SubscriberRoutingResult
                    {
                        ClientId = subscriber.ClientId,
                        TopicFilter = subscriber.TopicFilter,
                        RoutingType = MessageRoutingType.Failed,
                        IsSuccess = false,
                        ErrorMessage = "Client offline and QoS 0 message discarded",
                        ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                    };
                }
            }

            // 客户端在线，直接分发
            var subscriberPacket = CreateSubscriberPacket(effectivePacket, subscriber, effectiveQoS);

            try
            {
                await subscriber.Client.SendPacketAsync(subscriberPacket, cancellationToken);
                stopwatch.Stop();

                _logger.LogTrace("Message delivered to online subscriber {ClientId} with QoS {QoS} in {ElapsedMs}ms",
                    subscriber.ClientId, effectiveQoS, stopwatch.ElapsedMilliseconds);

                return new SubscriberRoutingResult
                {
                    ClientId = subscriber.ClientId,
                    TopicFilter = subscriber.TopicFilter,
                    RoutingType = MessageRoutingType.OnlineDelivery,
                    IsSuccess = true,
                    QoSLevel = effectiveQoS,
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                };
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogWarning(ex, "Failed to deliver message to online subscriber {ClientId}", subscriber.ClientId);

                // 在线分发失败，根据配置决定是否添加到死信队列
                if (_options.EnableDeadLetterQueue)
                {
                    var deadLetterMessage = DeadLetterMessage.Create(
                        effectivePacket,
                        subscriber.ClientId,
                        ex.Message,
                        DeadLetterReason.NetworkError);

                    await _deadLetterService.AddToDeadLetterQueueAsync(deadLetterMessage, cancellationToken);
                }

                return new SubscriberRoutingResult
                {
                    ClientId = subscriber.ClientId,
                    TopicFilter = subscriber.TopicFilter,
                    RoutingType = MessageRoutingType.Failed,
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                };
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error routing message to subscriber {ClientId}", subscriber.ClientId);

            return new SubscriberRoutingResult
            {
                ClientId = subscriber.ClientId,
                TopicFilter = subscriber.TopicFilter,
                RoutingType = MessageRoutingType.Failed,
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
            };
        }
    }

    /// <summary>
    /// 为订阅者创建PUBLISH数据包
    /// </summary>
    /// <param name="originalPacket">原始发布数据包</param>
    /// <param name="subscriber">订阅者</param>
    /// <param name="effectiveQoS">有效QoS级别</param>
    /// <returns>订阅者数据包</returns>
    private MqttPublishPacket CreateSubscriberPacket(MqttPublishPacket originalPacket, TopicSubscriber subscriber, MqttQoSLevel effectiveQoS)
    {
        // 确定保留标志
        var retainFlag = originalPacket.Retain && !subscriber.Options.RetainAsPublished ? false : originalPacket.Retain;

        // 如果需要数据包标识符（QoS > 0），生成一个新的
        ushort? packetIdentifier = null;
        if (effectiveQoS > MqttQoSLevel.AtMostOnce)
        {
            packetIdentifier = GeneratePacketIdentifier();
        }

        var subscriberPacket = MqttPublishPacket.Create(
            originalPacket.Topic,
            originalPacket.Payload,
            effectiveQoS,
            retainFlag,
            packetIdentifier);

        // 复制MQTT 5.0属性（如果适用）
        if (originalPacket.Properties != null)
        {
            subscriberPacket.Properties = originalPacket.Properties;
        }

        return subscriberPacket;
    }

    /// <summary>
    /// 生成数据包标识符
    /// </summary>
    /// <returns>数据包标识符</returns>
    private ushort GeneratePacketIdentifier()
    {
        // 简单的递增计数器，实际实现中应该考虑线程安全和重用
        return (ushort)(DateTime.UtcNow.Ticks % ushort.MaxValue + 1);
    }

    /// <summary>
    /// 更新统计信息
    /// </summary>
    /// <param name="result">路由结果</param>
    private void UpdateStatistics(MessageRoutingResult result)
    {
        lock (_statsLock)
        {
            _statistics.TotalMessages++;

            if (result.IsSuccess)
            {
                _statistics.OnlineDeliveries += result.OnlineDeliveries;
                _statistics.OfflineStorages += result.OfflineStorages;
                _statistics.FailedRoutings += result.FailedDeliveries;
                _statistics.FilteredMessages += result.FilteredCount;
            }
            else
            {
                _statistics.FailedRoutings++;
            }

            // 更新延迟统计
            if (result.ElapsedMilliseconds > _statistics.MaxRoutingLatency)
            {
                _statistics.MaxRoutingLatency = result.ElapsedMilliseconds;
            }

            // 计算平均延迟（简单移动平均）
            _statistics.AverageRoutingLatency = (_statistics.AverageRoutingLatency * (_statistics.TotalMessages - 1) + result.ElapsedMilliseconds) / _statistics.TotalMessages;
        }
    }

    #endregion

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _routingCounters.Clear();
            _disposed = true;
        }
    }
}

/// <summary>
/// 消息路由配置选项
/// </summary>
public class MessageRoutingOptions
{
    /// <summary>
    /// 最大并发路由数
    /// </summary>
    public int MaxConcurrentRoutings { get; set; } = 1000;

    /// <summary>
    /// 路由超时时间（毫秒）
    /// </summary>
    public int RoutingTimeoutMs { get; set; } = 5000;

    /// <summary>
    /// 是否启用消息过滤
    /// </summary>
    public bool EnableMessageFiltering { get; set; } = true;

    /// <summary>
    /// 是否启用离线消息存储
    /// </summary>
    public bool EnableOfflineMessageStorage { get; set; } = true;

    /// <summary>
    /// 是否启用死信队列
    /// </summary>
    public bool EnableDeadLetterQueue { get; set; } = true;

    /// <summary>
    /// 每个客户端最大离线消息数
    /// </summary>
    public int MaxOfflineMessagesPerClient { get; set; } = 1000;

    /// <summary>
    /// 离线消息过期时间（小时）
    /// </summary>
    public int OfflineMessageExpirationHours { get; set; } = 24;

    /// <summary>
    /// 是否启用批量路由优化
    /// </summary>
    public bool EnableBatchRoutingOptimization { get; set; } = true;

    /// <summary>
    /// 批量路由大小
    /// </summary>
    public int BatchRoutingSize { get; set; } = 100;

    /// <summary>
    /// 是否启用路由统计
    /// </summary>
    public bool EnableRoutingStatistics { get; set; } = true;

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// 重试间隔（毫秒）
    /// </summary>
    public int RetryIntervalMs { get; set; } = 1000;
}
