using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;

namespace MqttBroker.Core.Topic;

/// <summary>
/// 消息分发器接口
/// </summary>
public interface IMessageDispatcher
{
    /// <summary>
    /// 分发消息到匹配的订阅者
    /// </summary>
    /// <param name="publishPacket">发布数据包</param>
    /// <param name="publisherClientId">发布者客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分发结果</returns>
    Task<MessageDispatchResult> DispatchAsync(MqttPublishPacket publishPacket, string? publisherClientId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量分发消息
    /// </summary>
    /// <param name="messages">消息列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分发结果列表</returns>
    Task<IList<MessageDispatchResult>> DispatchBatchAsync(IList<MessageDispatchRequest> messages, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取分发统计信息
    /// </summary>
    /// <returns>分发统计信息</returns>
    Task<MessageDispatchStatistics> GetStatisticsAsync();

    /// <summary>
    /// 消息分发事件
    /// </summary>
    event EventHandler<MessageDispatchedEventArgs>? MessageDispatched;

    /// <summary>
    /// 消息分发失败事件
    /// </summary>
    event EventHandler<MessageDispatchFailedEventArgs>? MessageDispatchFailed;
}

/// <summary>
/// 消息分发请求
/// </summary>
public class MessageDispatchRequest
{
    /// <summary>
    /// 发布数据包
    /// </summary>
    public MqttPublishPacket PublishPacket { get; set; } = null!;

    /// <summary>
    /// 发布者客户端ID
    /// </summary>
    public string? PublisherClientId { get; set; }

    /// <summary>
    /// 创建消息分发请求
    /// </summary>
    /// <param name="publishPacket">发布数据包</param>
    /// <param name="publisherClientId">发布者客户端ID</param>
    /// <returns>消息分发请求</returns>
    public static MessageDispatchRequest Create(MqttPublishPacket publishPacket, string? publisherClientId = null)
    {
        return new MessageDispatchRequest
        {
            PublishPacket = publishPacket,
            PublisherClientId = publisherClientId
        };
    }
}

/// <summary>
/// 消息分发结果
/// </summary>
public class MessageDispatchResult
{
    /// <summary>
    /// 主题名称
    /// </summary>
    public string TopicName { get; set; } = string.Empty;

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 匹配的订阅者数量
    /// </summary>
    public int MatchedSubscribers { get; set; }

    /// <summary>
    /// 成功分发的数量
    /// </summary>
    public int SuccessfulDispatches { get; set; }

    /// <summary>
    /// 失败的分发数量
    /// </summary>
    public int FailedDispatches { get; set; }

    /// <summary>
    /// 分发耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 分发详情
    /// </summary>
    public IList<SubscriberDispatchResult> DispatchDetails { get; set; } = new List<SubscriberDispatchResult>();

    /// <summary>
    /// 创建成功的分发结果
    /// </summary>
    /// <param name="topicName">主题名称</param>
    /// <param name="matchedSubscribers">匹配的订阅者数量</param>
    /// <param name="successfulDispatches">成功分发数量</param>
    /// <param name="elapsedMilliseconds">耗时</param>
    /// <returns>分发结果</returns>
    public static MessageDispatchResult Success(string topicName, int matchedSubscribers, int successfulDispatches, long elapsedMilliseconds)
    {
        return new MessageDispatchResult
        {
            TopicName = topicName,
            IsSuccess = true,
            MatchedSubscribers = matchedSubscribers,
            SuccessfulDispatches = successfulDispatches,
            FailedDispatches = matchedSubscribers - successfulDispatches,
            ElapsedMilliseconds = elapsedMilliseconds
        };
    }

    /// <summary>
    /// 创建失败的分发结果
    /// </summary>
    /// <param name="topicName">主题名称</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>分发结果</returns>
    public static MessageDispatchResult Failure(string topicName, string errorMessage)
    {
        return new MessageDispatchResult
        {
            TopicName = topicName,
            IsSuccess = false,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// 订阅者分发结果
/// </summary>
public class SubscriberDispatchResult
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 主题过滤器
    /// </summary>
    public string TopicFilter { get; set; } = string.Empty;

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 使用的QoS级别
    /// </summary>
    public MqttQoSLevel QoSLevel { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 分发耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }
}

/// <summary>
/// 消息分发统计信息
/// </summary>
public class MessageDispatchStatistics
{
    /// <summary>
    /// 总分发消息数
    /// </summary>
    public long TotalMessages { get; set; }

    /// <summary>
    /// 成功分发数
    /// </summary>
    public long SuccessfulDispatches { get; set; }

    /// <summary>
    /// 失败分发数
    /// </summary>
    public long FailedDispatches { get; set; }

    /// <summary>
    /// 平均分发延迟（毫秒）
    /// </summary>
    public double AverageDispatchLatency { get; set; }

    /// <summary>
    /// 最大分发延迟（毫秒）
    /// </summary>
    public long MaxDispatchLatency { get; set; }

    /// <summary>
    /// 每秒分发消息数
    /// </summary>
    public double MessagesPerSecond { get; set; }

    /// <summary>
    /// 活跃订阅者数
    /// </summary>
    public long ActiveSubscribers { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate => TotalMessages > 0 ? (double)SuccessfulDispatches / TotalMessages * 100 : 0;
}

/// <summary>
/// 消息分发事件参数
/// </summary>
public class MessageDispatchedEventArgs : EventArgs
{
    /// <summary>
    /// 分发结果
    /// </summary>
    public MessageDispatchResult Result { get; }

    /// <summary>
    /// 发布数据包
    /// </summary>
    public MqttPublishPacket PublishPacket { get; }

    /// <summary>
    /// 初始化消息分发事件参数
    /// </summary>
    /// <param name="result">分发结果</param>
    /// <param name="publishPacket">发布数据包</param>
    public MessageDispatchedEventArgs(MessageDispatchResult result, MqttPublishPacket publishPacket)
    {
        Result = result ?? throw new ArgumentNullException(nameof(result));
        PublishPacket = publishPacket ?? throw new ArgumentNullException(nameof(publishPacket));
    }
}

/// <summary>
/// 消息分发失败事件参数
/// </summary>
public class MessageDispatchFailedEventArgs : EventArgs
{
    /// <summary>
    /// 主题名称
    /// </summary>
    public string TopicName { get; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; }

    /// <summary>
    /// 异常信息
    /// </summary>
    public Exception? Exception { get; }

    /// <summary>
    /// 发布数据包
    /// </summary>
    public MqttPublishPacket PublishPacket { get; }

    /// <summary>
    /// 初始化消息分发失败事件参数
    /// </summary>
    /// <param name="topicName">主题名称</param>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="publishPacket">发布数据包</param>
    /// <param name="exception">异常信息</param>
    public MessageDispatchFailedEventArgs(string topicName, string errorMessage, MqttPublishPacket publishPacket, Exception? exception = null)
    {
        TopicName = topicName ?? throw new ArgumentNullException(nameof(topicName));
        ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));
        PublishPacket = publishPacket ?? throw new ArgumentNullException(nameof(publishPacket));
        Exception = exception;
    }
}
