using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;

namespace MqttBroker.Core.Message;

/// <summary>
/// 消息持久化服务接口
/// </summary>
public interface IMessagePersistenceService
{
    /// <summary>
    /// 存储离线消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="publishPacket">发布数据包</param>
    /// <param name="qosLevel">QoS级别</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>存储结果</returns>
    Task<MessageStorageResult> StoreOfflineMessageAsync(string clientId, MqttPublishPacket publishPacket, MqttQoSLevel qosLevel, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取离线消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="maxMessages">最大消息数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>离线消息列表</returns>
    Task<IList<OfflineMessage>> GetOfflineMessagesAsync(string clientId, int maxMessages = 100, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除离线消息
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功删除</returns>
    Task<bool> DeleteOfflineMessageAsync(string messageId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量删除离线消息
    /// </summary>
    /// <param name="messageIds">消息ID列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除结果</returns>
    Task<BatchDeleteResult> DeleteOfflineMessagesAsync(IList<string> messageIds, CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理过期消息
    /// </summary>
    /// <param name="expiredBefore">过期时间点</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理结果</returns>
    Task<MessageCleanupResult> CleanupExpiredMessagesAsync(DateTime expiredBefore, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取客户端离线消息统计
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>统计信息</returns>
    Task<ClientMessageStatistics> GetClientMessageStatisticsAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取全局消息统计
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>全局统计信息</returns>
    Task<GlobalMessageStatistics> GetGlobalMessageStatisticsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 离线消息
/// </summary>
public class OfflineMessage
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 主题名称
    /// </summary>
    public string TopicName { get; set; } = string.Empty;

    /// <summary>
    /// 消息负载
    /// </summary>
    public byte[] Payload { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// QoS级别
    /// </summary>
    public MqttQoSLevel QoSLevel { get; set; }

    /// <summary>
    /// 是否保留消息
    /// </summary>
    public bool Retain { get; set; }

    /// <summary>
    /// 数据包标识符
    /// </summary>
    public ushort? PacketIdentifier { get; set; }

    /// <summary>
    /// 存储时间
    /// </summary>
    public DateTime StoredAt { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// 最后重试时间
    /// </summary>
    public DateTime? LastRetryAt { get; set; }

    /// <summary>
    /// 消息属性（MQTT 5.0）
    /// </summary>
    public Dictionary<string, object>? Properties { get; set; }

    /// <summary>
    /// 转换为MQTT发布数据包
    /// </summary>
    /// <returns>MQTT发布数据包</returns>
    public MqttPublishPacket ToPublishPacket()
    {
        var packet = MqttPublishPacket.Create(TopicName, Payload, QoSLevel, Retain, PacketIdentifier);
        return packet;
    }
}

/// <summary>
/// 消息存储结果
/// </summary>
public class MessageStorageResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 消息ID
    /// </summary>
    public string? MessageId { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 存储时间
    /// </summary>
    public DateTime StoredAt { get; set; }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <param name="storedAt">存储时间</param>
    /// <returns>存储结果</returns>
    public static MessageStorageResult Success(string messageId, DateTime storedAt)
    {
        return new MessageStorageResult
        {
            IsSuccess = true,
            MessageId = messageId,
            StoredAt = storedAt
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>存储结果</returns>
    public static MessageStorageResult Failure(string errorMessage)
    {
        return new MessageStorageResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// 批量删除结果
/// </summary>
public class BatchDeleteResult
{
    /// <summary>
    /// 总数量
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 成功删除数量
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailedCount { get; set; }

    /// <summary>
    /// 失败的消息ID列表
    /// </summary>
    public IList<string> FailedMessageIds { get; set; } = new List<string>();

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 消息清理结果
/// </summary>
public class MessageCleanupResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 清理的消息数量
    /// </summary>
    public int CleanedCount { get; set; }

    /// <summary>
    /// 清理耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 客户端消息统计
/// </summary>
public class ClientMessageStatistics
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 离线消息数量
    /// </summary>
    public int OfflineMessageCount { get; set; }

    /// <summary>
    /// 总消息大小（字节）
    /// </summary>
    public long TotalMessageSize { get; set; }

    /// <summary>
    /// 最早消息时间
    /// </summary>
    public DateTime? EarliestMessageTime { get; set; }

    /// <summary>
    /// 最新消息时间
    /// </summary>
    public DateTime? LatestMessageTime { get; set; }

    /// <summary>
    /// 按QoS级别分组的统计
    /// </summary>
    public Dictionary<MqttQoSLevel, int> MessagesByQoS { get; set; } = new();
}

/// <summary>
/// 全局消息统计
/// </summary>
public class GlobalMessageStatistics
{
    /// <summary>
    /// 总离线消息数量
    /// </summary>
    public long TotalOfflineMessages { get; set; }

    /// <summary>
    /// 有离线消息的客户端数量
    /// </summary>
    public int ClientsWithOfflineMessages { get; set; }

    /// <summary>
    /// 总消息大小（字节）
    /// </summary>
    public long TotalMessageSize { get; set; }

    /// <summary>
    /// 平均每客户端消息数
    /// </summary>
    public double AverageMessagesPerClient { get; set; }

    /// <summary>
    /// 按QoS级别分组的统计
    /// </summary>
    public Dictionary<MqttQoSLevel, long> MessagesByQoS { get; set; } = new();

    /// <summary>
    /// 今日存储的消息数
    /// </summary>
    public long TodayStoredMessages { get; set; }

    /// <summary>
    /// 今日分发的消息数
    /// </summary>
    public long TodayDeliveredMessages { get; set; }
}
