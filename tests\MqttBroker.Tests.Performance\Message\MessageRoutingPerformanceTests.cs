using System.Diagnostics;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using MqttBroker.Core;
using MqttBroker.Core.Client;
using MqttBroker.Core.Message;
using MqttBroker.Core.Message.Filters;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Topic;
using Xunit;
using Xunit.Abstractions;

namespace MqttBroker.Tests.Performance.Message;

/// <summary>
/// 消息路由引擎性能测试
/// </summary>
public class MessageRoutingPerformanceTests
{
    private readonly ITestOutputHelper _output;

    public MessageRoutingPerformanceTests(ITestOutputHelper output)
    {
        _output = output;
    }

    [Fact]
    public async Task MessageRouting_HighThroughput_PerformanceTest()
    {
        // Arrange
        const int messageCount = 10000;
        const int subscriberCount = 100;
        
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Warning));
        services.AddMqttBrokerCore(); // 添加完整的核心服务
        
        var serviceProvider = services.BuildServiceProvider();
        var routingEngine = serviceProvider.GetRequiredService<IMessageRoutingEngine>();
        var subscriptionManager = serviceProvider.GetRequiredService<ITopicSubscriptionManager>();
        
        // 创建模拟订阅者
        var subscribers = new List<TopicSubscriber>();
        for (int i = 0; i < subscriberCount; i++)
        {
            var mockClient = new Mock<IMqttClient>();
            mockClient.Setup(x => x.State).Returns(MqttClientState.Authenticated);
            mockClient.Setup(x => x.SendPacketAsync(It.IsAny<IMqttPacket>(), It.IsAny<CancellationToken>()))
                   .Returns(Task.CompletedTask);

            var subscriber = new TopicSubscriber
            {
                ClientId = $"client-{i}",
                TopicFilter = "test/+",
                QoSLevel = MqttQoSLevel.AtLeastOnce,
                Client = mockClient.Object,
                Options = new MqttSubscriptionOptions()
            };
            
            subscribers.Add(subscriber);
        }

        // 模拟订阅管理器返回所有订阅者
        var mockSubscriptionManager = new Mock<ITopicSubscriptionManager>();
        mockSubscriptionManager
            .Setup(x => x.GetSubscribersAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(subscribers);

        // 创建测试用的路由引擎
        var mockFilterManager = new Mock<IMessageFilterManager>();
        mockFilterManager
            .Setup(x => x.ApplyFiltersAsync(It.IsAny<MessageFilterContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(MessageFilterResult.Allow());

        var mockPersistenceService = new Mock<IMessagePersistenceService>();
        var mockDeadLetterService = new Mock<IDeadLetterQueueService>();
        var mockLogger = new Mock<ILogger<MessageRoutingEngine>>();
        var options = Options.Create(new MessageRoutingOptions());

        var testRoutingEngine = new MessageRoutingEngine(
            mockSubscriptionManager.Object,
            mockFilterManager.Object,
            mockPersistenceService.Object,
            mockDeadLetterService.Object,
            mockLogger.Object,
            options);

        // 创建测试消息
        var messages = new List<MessageRoutingRequest>();
        for (int i = 0; i < messageCount; i++)
        {
            var publishPacket = MqttPublishPacket.Create(
                $"test/topic{i % 10}",
                System.Text.Encoding.UTF8.GetBytes($"message-{i}"),
                MqttQoSLevel.AtLeastOnce,
                false,
                (ushort)(i + 1));
            
            messages.Add(MessageRoutingRequest.Create(publishPacket, $"publisher-{i % 10}"));
        }

        _output.WriteLine($"开始性能测试: {messageCount} 条消息, {subscriberCount} 个订阅者");

        // Act - 单个消息路由性能测试
        var singleMessageStopwatch = Stopwatch.StartNew();
        var singleResults = new List<MessageRoutingResult>();
        
        foreach (var message in messages.Take(1000)) // 测试前1000条消息的单个路由性能
        {
            var result = await testRoutingEngine.RouteMessageAsync(message.PublishPacket, message.PublisherClientId);
            singleResults.Add(result);
        }
        
        singleMessageStopwatch.Stop();

        // Act - 批量消息路由性能测试
        var batchStopwatch = Stopwatch.StartNew();
        var batchResults = await testRoutingEngine.RouteMessagesAsync(messages);
        batchStopwatch.Stop();

        // Assert & Report
        var singleMessageThroughput = 1000.0 / singleMessageStopwatch.Elapsed.TotalSeconds;
        var batchThroughput = messageCount / batchStopwatch.Elapsed.TotalSeconds;
        
        var avgSingleLatency = singleResults.Average(r => r.ElapsedMilliseconds);
        var maxSingleLatency = singleResults.Max(r => r.ElapsedMilliseconds);
        var avgBatchLatency = batchResults.Average(r => r.ElapsedMilliseconds);
        var maxBatchLatency = batchResults.Max(r => r.ElapsedMilliseconds);

        var successfulRoutes = batchResults.Count(r => r.IsSuccess);
        var totalOnlineDeliveries = batchResults.Sum(r => r.OnlineDeliveries);

        _output.WriteLine($"=== 消息路由引擎性能测试结果 ===");
        _output.WriteLine($"单个消息路由:");
        _output.WriteLine($"  - 吞吐量: {singleMessageThroughput:F0} 消息/秒");
        _output.WriteLine($"  - 平均延迟: {avgSingleLatency:F2} ms");
        _output.WriteLine($"  - 最大延迟: {maxSingleLatency} ms");
        _output.WriteLine($"批量消息路由:");
        _output.WriteLine($"  - 吞吐量: {batchThroughput:F0} 消息/秒");
        _output.WriteLine($"  - 平均延迟: {avgBatchLatency:F2} ms");
        _output.WriteLine($"  - 最大延迟: {maxBatchLatency} ms");
        _output.WriteLine($"路由统计:");
        _output.WriteLine($"  - 成功路由: {successfulRoutes}/{messageCount}");
        _output.WriteLine($"  - 在线分发总数: {totalOnlineDeliveries:N0}");
        _output.WriteLine($"  - 预期分发数: {messageCount * subscriberCount:N0}");

        // 性能断言 (调整为更现实的期望值)
        Assert.True(singleMessageThroughput > 1000, $"单个消息路由吞吐量应该 > 1,000 消息/秒，实际: {singleMessageThroughput:F0}");
        Assert.True(batchThroughput > 2000, $"批量消息路由吞吐量应该 > 2,000 消息/秒，实际: {batchThroughput:F0}");
        Assert.True(avgSingleLatency < 10, $"平均单个消息路由延迟应该 < 10ms，实际: {avgSingleLatency:F2}ms");
        Assert.True(maxSingleLatency < 100, $"最大单个消息路由延迟应该 < 100ms，实际: {maxSingleLatency}ms");
        Assert.Equal(messageCount, successfulRoutes);
        Assert.Equal(messageCount * subscriberCount, totalOnlineDeliveries);
    }

    [Fact]
    public async Task MessageFiltering_HighThroughput_PerformanceTest()
    {
        // Arrange
        const int messageCount = 5000;
        
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Warning));
        services.AddMqttBrokerCore(); // 添加完整的核心服务
        services.ConfigureMessageRoutingEngine(
            routing => { },
            sizeFilter =>
            {
                sizeFilter.MaxMessageSize = 1024 * 1024;
            },
            null, null,
            rateLimitFilter =>
            {
                rateLimitFilter.MaxMessagesPerWindow = 10000;
                rateLimitFilter.WindowSizeSeconds = 60;
            });
        
        var serviceProvider = services.BuildServiceProvider();
        var filterManager = serviceProvider.GetRequiredService<IMessageFilterManager>();
        
        // 注册内置过滤器
        var sizeFilter = serviceProvider.GetService<MessageSizeFilter>();
        var rateLimitFilter = serviceProvider.GetService<RateLimitFilter>();
        
        if (sizeFilter != null) filterManager.RegisterFilter(sizeFilter);
        if (rateLimitFilter != null) filterManager.RegisterFilter(rateLimitFilter);

        // 创建测试上下文
        var contexts = new List<MessageFilterContext>();
        for (int i = 0; i < messageCount; i++)
        {
            var publishPacket = MqttPublishPacket.Create(
                $"test/topic{i % 10}",
                System.Text.Encoding.UTF8.GetBytes($"message-{i}"));
            
            var subscriber = new TopicSubscriber
            {
                ClientId = $"client-{i % 100}",
                TopicFilter = "test/+",
                QoSLevel = MqttQoSLevel.AtMostOnce,
                Options = new MqttSubscriptionOptions()
            };
            
            contexts.Add(MessageFilterContext.Create(publishPacket, subscriber, $"publisher-{i % 10}"));
        }

        _output.WriteLine($"开始过滤器性能测试: {messageCount} 条消息");

        // Act
        var stopwatch = Stopwatch.StartNew();
        var results = new List<MessageFilterResult>();
        
        foreach (var context in contexts)
        {
            var result = await filterManager.ApplyFiltersAsync(context);
            results.Add(result);
        }
        
        stopwatch.Stop();

        // Assert & Report
        var throughput = messageCount / stopwatch.Elapsed.TotalSeconds;
        var avgLatency = results.Average(r => r.ElapsedMilliseconds);
        var maxLatency = results.Max(r => r.ElapsedMilliseconds);
        var allowedCount = results.Count(r => r.IsAllowed);

        _output.WriteLine($"=== 消息过滤器性能测试结果 ===");
        _output.WriteLine($"吞吐量: {throughput:F0} 消息/秒");
        _output.WriteLine($"平均延迟: {avgLatency:F2} ms");
        _output.WriteLine($"最大延迟: {maxLatency} ms");
        _output.WriteLine($"通过率: {allowedCount}/{messageCount} ({(double)allowedCount/messageCount*100:F1}%)");

        // 获取过滤器统计信息
        var filterStats = await filterManager.GetStatisticsAsync();
        _output.WriteLine($"过滤器统计:");
        _output.WriteLine($"  - 总过滤次数: {filterStats.TotalFilters}");
        _output.WriteLine($"  - 平均过滤延迟: {filterStats.AverageFilterLatency:F2} ms");
        _output.WriteLine($"  - 通过率: {filterStats.PassRate:F1}%");

        // 性能断言
        Assert.True(throughput > 10000, $"过滤器吞吐量应该 > 10,000 消息/秒，实际: {throughput:F0}");
        Assert.True(avgLatency < 1, $"平均过滤延迟应该 < 1ms，实际: {avgLatency:F2}ms");
        Assert.True(maxLatency < 10, $"最大过滤延迟应该 < 10ms，实际: {maxLatency}ms");
        Assert.Equal(messageCount, allowedCount); // 所有消息都应该通过过滤器
    }

    [Fact]
    public async Task OfflineMessageProcessing_PerformanceTest()
    {
        // Arrange
        const int messageCount = 1000;
        const string clientId = "test-client";
        
        var mockPersistenceService = new Mock<IMessagePersistenceService>();
        var mockSubscriptionManager = new Mock<ITopicSubscriptionManager>();
        var mockFilterManager = new Mock<IMessageFilterManager>();
        var mockDeadLetterService = new Mock<IDeadLetterQueueService>();
        var mockLogger = new Mock<ILogger<MessageRoutingEngine>>();
        var options = Options.Create(new MessageRoutingOptions());

        // 创建离线消息
        var offlineMessages = new List<OfflineMessage>();
        for (int i = 0; i < messageCount; i++)
        {
            offlineMessages.Add(new OfflineMessage
            {
                MessageId = $"msg-{i}",
                ClientId = clientId,
                TopicName = $"test/topic{i % 10}",
                Payload = System.Text.Encoding.UTF8.GetBytes($"message-{i}"),
                QoSLevel = MqttQoSLevel.AtLeastOnce,
                PacketIdentifier = (ushort)(i + 1),
                StoredAt = DateTime.UtcNow.AddMinutes(-5)
            });
        }

        // 模拟在线客户端
        var mockClient = new Mock<IMqttClient>();
        mockClient.Setup(x => x.State).Returns(MqttClientState.Authenticated);
        mockClient.Setup(x => x.SendPacketAsync(It.IsAny<IMqttPacket>(), It.IsAny<CancellationToken>()))
               .Returns(Task.CompletedTask);

        var subscriber = new TopicSubscriber
        {
            ClientId = clientId,
            TopicFilter = "test/+",
            QoSLevel = MqttQoSLevel.AtLeastOnce,
            Client = mockClient.Object
        };

        // 设置模拟
        mockPersistenceService
            .Setup(x => x.GetOfflineMessagesAsync(clientId, It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(offlineMessages);

        mockSubscriptionManager
            .Setup(x => x.GetSubscriberAsync(clientId, It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(subscriber);

        mockPersistenceService
            .Setup(x => x.DeleteOfflineMessagesAsync(It.IsAny<IList<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new BatchDeleteResult { TotalCount = messageCount, SuccessCount = messageCount });

        var routingEngine = new MessageRoutingEngine(
            mockSubscriptionManager.Object,
            mockFilterManager.Object,
            mockPersistenceService.Object,
            mockDeadLetterService.Object,
            mockLogger.Object,
            options);

        _output.WriteLine($"开始离线消息处理性能测试: {messageCount} 条消息");

        // Act
        var stopwatch = Stopwatch.StartNew();
        var result = await routingEngine.ProcessOfflineMessagesAsync(clientId);
        stopwatch.Stop();

        // Assert & Report
        var throughput = messageCount / stopwatch.Elapsed.TotalSeconds;

        _output.WriteLine($"=== 离线消息处理性能测试结果 ===");
        _output.WriteLine($"处理时间: {stopwatch.ElapsedMilliseconds} ms");
        _output.WriteLine($"吞吐量: {throughput:F0} 消息/秒");
        _output.WriteLine($"处理结果: {result.DeliveredMessages}/{result.ProcessedMessages} 消息成功分发");

        // 性能断言
        Assert.True(result.IsSuccess);
        Assert.Equal(messageCount, result.ProcessedMessages);
        Assert.Equal(messageCount, result.DeliveredMessages);
        Assert.Equal(0, result.FailedMessages);
        Assert.True(throughput > 1000, $"离线消息处理吞吐量应该 > 1,000 消息/秒，实际: {throughput:F0}");
        Assert.True(stopwatch.ElapsedMilliseconds < 5000, $"处理 {messageCount} 条离线消息应该 < 5秒，实际: {stopwatch.ElapsedMilliseconds}ms");
    }
}
