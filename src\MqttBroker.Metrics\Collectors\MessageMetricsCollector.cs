using MqttBroker.Metrics.Models;
using System.Collections.Concurrent;

namespace MqttBroker.Metrics.Collectors;

/// <summary>
/// 消息指标收集器实现
/// </summary>
public class MessageMetricsCollector : IMessageMetricsCollector
{
    private readonly object _lockObject = new();
    private readonly ConcurrentQueue<double> _routingLatencies = new();
    private readonly ConcurrentDictionary<string, int> _offlineMessagesByClient = new();
    private readonly ConcurrentDictionary<string, int> _retainedMessagesByTopic = new();

    private long _messagesSent;
    private long _messagesReceived;
    private long _totalMessageSize;
    private long _messageCount;
    private long _deadLetterMessages;

    // 速率计算相关
    private DateTime _lastRateCalculation = DateTime.UtcNow;
    private long _lastMessagesSent;
    private long _lastMessagesReceived;

    /// <summary>
    /// 收集消息指标
    /// </summary>
    public MessageMetrics CollectMessageMetrics()
    {
        lock (_lockObject)
        {
            var now = DateTime.UtcNow;
            var timeDiff = (now - _lastRateCalculation).TotalSeconds;

            var sendRate = timeDiff > 0 ? (_messagesSent - _lastMessagesSent) / timeDiff : 0;
            var receiveRate = timeDiff > 0 ? (_messagesReceived - _lastMessagesReceived) / timeDiff : 0;

            _lastRateCalculation = now;
            _lastMessagesSent = _messagesSent;
            _lastMessagesReceived = _messagesReceived;

            var latencyMetrics = CalculateLatencyMetrics();

            return new MessageMetrics
            {
                MessagesSent = _messagesSent,
                MessagesReceived = _messagesReceived,
                MessageSendRate = sendRate,
                MessageReceiveRate = receiveRate,
                AverageMessageSize = _messageCount > 0 ? (double)_totalMessageSize / _messageCount : 0,
                RoutingLatency = latencyMetrics,
                OfflineMessages = _offlineMessagesByClient.Values.Sum(),
                DeadLetterMessages = _deadLetterMessages,
                RetainedMessages = _retainedMessagesByTopic.Count
            };
        }
    }

    /// <summary>
    /// 记录消息发送
    /// </summary>
    public void RecordMessageSent(int messageSize, TimeSpan routingLatency)
    {
        Interlocked.Increment(ref _messagesSent);
        Interlocked.Add(ref _totalMessageSize, messageSize);
        Interlocked.Increment(ref _messageCount);

        _routingLatencies.Enqueue(routingLatency.TotalMilliseconds);

        // 保持延迟队列大小在合理范围内
        while (_routingLatencies.Count > 10000)
        {
            _routingLatencies.TryDequeue(out _);
        }
    }

    /// <summary>
    /// 记录消息接收
    /// </summary>
    public void RecordMessageReceived(int messageSize)
    {
        Interlocked.Increment(ref _messagesReceived);
        Interlocked.Add(ref _totalMessageSize, messageSize);
        Interlocked.Increment(ref _messageCount);
    }

    /// <summary>
    /// 记录离线消息
    /// </summary>
    public void RecordOfflineMessage(string clientId)
    {
        _offlineMessagesByClient.AddOrUpdate(clientId, 1, (key, value) => value + 1);
    }

    /// <summary>
    /// 记录死信消息
    /// </summary>
    public void RecordDeadLetterMessage(string reason)
    {
        Interlocked.Increment(ref _deadLetterMessages);
    }

    /// <summary>
    /// 记录保留消息
    /// </summary>
    public void RecordRetainedMessage(string topic)
    {
        _retainedMessagesByTopic.AddOrUpdate(topic, 1, (key, value) => 1);
    }

    /// <summary>
    /// 计算延迟指标
    /// </summary>
    private MessageLatencyMetrics CalculateLatencyMetrics()
    {
        var latencies = _routingLatencies.ToArray();
        if (latencies.Length == 0)
        {
            return new MessageLatencyMetrics();
        }

        Array.Sort(latencies);

        return new MessageLatencyMetrics
        {
            Average = latencies.Average(),
            Min = latencies.Min(),
            Max = latencies.Max(),
            P50 = GetPercentile(latencies, 0.5),
            P95 = GetPercentile(latencies, 0.95),
            P99 = GetPercentile(latencies, 0.99)
        };
    }

    /// <summary>
    /// 获取百分位数
    /// </summary>
    private static double GetPercentile(double[] sortedValues, double percentile)
    {
        if (sortedValues.Length == 0) return 0;
        if (sortedValues.Length == 1) return sortedValues[0];

        var index = percentile * (sortedValues.Length - 1);
        var lower = (int)Math.Floor(index);
        var upper = (int)Math.Ceiling(index);

        if (lower == upper)
        {
            return sortedValues[lower];
        }

        var weight = index - lower;
        return sortedValues[lower] * (1 - weight) + sortedValues[upper] * weight;
    }
}

/// <summary>
/// 订阅指标收集器实现
/// </summary>
public class SubscriptionMetricsCollector : ISubscriptionMetricsCollector
{
    private readonly object _lockObject = new();
    private readonly ConcurrentQueue<double> _matchingLatencies = new();
    private readonly ConcurrentDictionary<string, HashSet<string>> _subscriptionsByClient = new();
    private readonly ConcurrentDictionary<string, DateTime> _activeTopics = new();

    private long _totalSubscriptions;
    private long _wildcardSubscriptions;

    // 速率计算相关
    private DateTime _lastRateCalculation = DateTime.UtcNow;
    private long _lastSubscriptionCount;
    private long _lastUnsubscriptionCount;
    private long _unsubscriptionCount;

    /// <summary>
    /// 收集订阅指标
    /// </summary>
    public SubscriptionMetrics CollectSubscriptionMetrics()
    {
        lock (_lockObject)
        {
            var now = DateTime.UtcNow;
            var timeDiff = (now - _lastRateCalculation).TotalSeconds;

            var subscriptionRate = timeDiff > 0 ? (_totalSubscriptions - _lastSubscriptionCount) / timeDiff : 0;
            var unsubscriptionRate = timeDiff > 0 ? (_unsubscriptionCount - _lastUnsubscriptionCount) / timeDiff : 0;

            _lastRateCalculation = now;
            _lastSubscriptionCount = _totalSubscriptions;
            _lastUnsubscriptionCount = _unsubscriptionCount;

            var matchingLatency = CalculateMatchingLatencyMetrics();

            return new SubscriptionMetrics
            {
                TotalSubscriptions = _totalSubscriptions,
                ActiveTopics = _activeTopics.Count,
                WildcardSubscriptions = _wildcardSubscriptions,
                MatchingLatency = matchingLatency,
                SubscriptionRate = subscriptionRate,
                UnsubscriptionRate = unsubscriptionRate
            };
        }
    }

    /// <summary>
    /// 记录订阅
    /// </summary>
    public void RecordSubscription(string clientId, string topicFilter, bool isWildcard)
    {
        _subscriptionsByClient.AddOrUpdate(
            clientId,
            new HashSet<string> { topicFilter },
            (key, existing) =>
            {
                existing.Add(topicFilter);
                return existing;
            });

        Interlocked.Increment(ref _totalSubscriptions);

        if (isWildcard)
        {
            Interlocked.Increment(ref _wildcardSubscriptions);
        }
    }

    /// <summary>
    /// 记录取消订阅
    /// </summary>
    public void RecordUnsubscription(string clientId, string topicFilter)
    {
        if (_subscriptionsByClient.TryGetValue(clientId, out var subscriptions))
        {
            subscriptions.Remove(topicFilter);
            if (subscriptions.Count == 0)
            {
                _subscriptionsByClient.TryRemove(clientId, out _);
            }
        }

        Interlocked.Increment(ref _unsubscriptionCount);
    }

    /// <summary>
    /// 记录主题匹配延迟
    /// </summary>
    public void RecordTopicMatchingLatency(TimeSpan latency)
    {
        _matchingLatencies.Enqueue(latency.TotalMilliseconds);

        // 保持延迟队列大小在合理范围内
        while (_matchingLatencies.Count > 10000)
        {
            _matchingLatencies.TryDequeue(out _);
        }
    }

    /// <summary>
    /// 记录活跃主题
    /// </summary>
    public void RecordActiveTopic(string topic)
    {
        _activeTopics[topic] = DateTime.UtcNow;
    }

    /// <summary>
    /// 计算匹配延迟指标
    /// </summary>
    private MessageLatencyMetrics CalculateMatchingLatencyMetrics()
    {
        var latencies = _matchingLatencies.ToArray();
        if (latencies.Length == 0)
        {
            return new MessageLatencyMetrics();
        }

        Array.Sort(latencies);

        return new MessageLatencyMetrics
        {
            Average = latencies.Average(),
            Min = latencies.Min(),
            Max = latencies.Max(),
            P50 = GetPercentile(latencies, 0.5),
            P95 = GetPercentile(latencies, 0.95),
            P99 = GetPercentile(latencies, 0.99)
        };
    }

    /// <summary>
    /// 获取百分位数
    /// </summary>
    private static double GetPercentile(double[] sortedValues, double percentile)
    {
        if (sortedValues.Length == 0) return 0;
        if (sortedValues.Length == 1) return sortedValues[0];

        var index = percentile * (sortedValues.Length - 1);
        var lower = (int)Math.Floor(index);
        var upper = (int)Math.Ceiling(index);

        if (lower == upper)
        {
            return sortedValues[lower];
        }

        var weight = index - lower;
        return sortedValues[lower] * (1 - weight) + sortedValues[upper] * weight;
    }
}
