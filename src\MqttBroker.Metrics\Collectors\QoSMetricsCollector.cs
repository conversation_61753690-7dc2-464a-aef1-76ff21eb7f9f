using MqttBroker.Metrics.Models;
using System.Collections.Concurrent;

namespace MqttBroker.Metrics.Collectors;

/// <summary>
/// QoS 指标收集器实现
/// </summary>
public class QoSMetricsCollector : IQoSMetricsCollector
{
    private readonly object _lockObject = new();
    private readonly ConcurrentQueue<double> _processingLatencies = new();
    private readonly ConcurrentDictionary<string, HashSet<ushort>> _pendingAcknowledgments = new();

    private long _qos0Messages;
    private long _qos1Messages;
    private long _qos2Messages;
    private long _messageRetransmissions;
    private long _successfulProcessing;
    private long _totalProcessing;

    /// <summary>
    /// 收集 QoS 指标
    /// </summary>
    public QoSMetrics CollectQoSMetrics()
    {
        lock (_lockObject)
        {
            var processingLatency = CalculateProcessingLatencyMetrics();
            var pendingCount = _pendingAcknowledgments.Values.Sum(set => set.Count);
            var successRate = _totalProcessing > 0 ? (double)_successfulProcessing / _totalProcessing * 100 : 100;

            return new QoSMetrics
            {
                QoS0Messages = _qos0Messages,
                QoS1Messages = _qos1Messages,
                QoS2Messages = _qos2Messages,
                PendingAcknowledgments = pendingCount,
                ProcessingLatency = processingLatency,
                MessageRetransmissions = _messageRetransmissions,
                SuccessRate = successRate
            };
        }
    }

    /// <summary>
    /// 记录 QoS 消息处理
    /// </summary>
    public void RecordQoSProcessing(int qosLevel, TimeSpan processingLatency, bool success)
    {
        switch (qosLevel)
        {
            case 0:
                Interlocked.Increment(ref _qos0Messages);
                break;
            case 1:
                Interlocked.Increment(ref _qos1Messages);
                break;
            case 2:
                Interlocked.Increment(ref _qos2Messages);
                break;
        }

        _processingLatencies.Enqueue(processingLatency.TotalMilliseconds);

        // 保持延迟队列大小在合理范围内
        while (_processingLatencies.Count > 10000)
        {
            _processingLatencies.TryDequeue(out _);
        }

        Interlocked.Increment(ref _totalProcessing);
        if (success)
        {
            Interlocked.Increment(ref _successfulProcessing);
        }
    }

    /// <summary>
    /// 记录待确认消息
    /// </summary>
    public void RecordPendingAcknowledgment(string clientId, ushort messageId)
    {
        _pendingAcknowledgments.AddOrUpdate(
            clientId,
            new HashSet<ushort> { messageId },
            (key, existing) =>
            {
                existing.Add(messageId);
                return existing;
            });
    }

    /// <summary>
    /// 记录消息确认
    /// </summary>
    public void RecordMessageAcknowledged(string clientId, ushort messageId)
    {
        if (_pendingAcknowledgments.TryGetValue(clientId, out var pendingMessages))
        {
            pendingMessages.Remove(messageId);
            if (pendingMessages.Count == 0)
            {
                _pendingAcknowledgments.TryRemove(clientId, out _);
            }
        }
    }

    /// <summary>
    /// 记录消息重传
    /// </summary>
    public void RecordMessageRetransmission(string clientId, ushort messageId)
    {
        Interlocked.Increment(ref _messageRetransmissions);
    }

    /// <summary>
    /// 计算处理延迟指标
    /// </summary>
    private MessageLatencyMetrics CalculateProcessingLatencyMetrics()
    {
        var latencies = _processingLatencies.ToArray();
        if (latencies.Length == 0)
        {
            return new MessageLatencyMetrics();
        }

        Array.Sort(latencies);

        return new MessageLatencyMetrics
        {
            Average = latencies.Average(),
            Min = latencies.Min(),
            Max = latencies.Max(),
            P50 = GetPercentile(latencies, 0.5),
            P95 = GetPercentile(latencies, 0.95),
            P99 = GetPercentile(latencies, 0.99)
        };
    }

    /// <summary>
    /// 获取百分位数
    /// </summary>
    private static double GetPercentile(double[] sortedValues, double percentile)
    {
        if (sortedValues.Length == 0) return 0;
        if (sortedValues.Length == 1) return sortedValues[0];

        var index = percentile * (sortedValues.Length - 1);
        var lower = (int)Math.Floor(index);
        var upper = (int)Math.Ceiling(index);

        if (lower == upper)
        {
            return sortedValues[lower];
        }

        var weight = index - lower;
        return sortedValues[lower] * (1 - weight) + sortedValues[upper] * weight;
    }
}

/// <summary>
/// 系统指标收集器实现
/// </summary>
public class SystemMetricsCollector : ISystemMetricsCollector
{
    private readonly System.Diagnostics.Process _currentProcess;

    /// <summary>
    /// 构造函数
    /// </summary>
    public SystemMetricsCollector()
    {
        _currentProcess = System.Diagnostics.Process.GetCurrentProcess();
    }

    /// <summary>
    /// 收集系统指标
    /// </summary>
    public SystemMetrics CollectSystemMetrics()
    {
        try
        {
            var memoryMetrics = CollectMemoryMetrics();
            var threadPoolMetrics = CollectThreadPoolMetrics();
            var gcMetrics = CollectGCMetrics();
            var cpuUsage = GetCpuUsage();

            return new SystemMetrics
            {
                CpuUsage = cpuUsage,
                Memory = memoryMetrics,
                ThreadPool = threadPoolMetrics,
                GarbageCollection = gcMetrics
            };
        }
        catch (Exception)
        {
            // 如果收集系统指标失败，返回默认值
            return new SystemMetrics();
        }
    }

    /// <summary>
    /// 收集内存指标
    /// </summary>
    private MemoryMetrics CollectMemoryMetrics()
    {
        _currentProcess.Refresh();

        return new MemoryMetrics
        {
            TotalMemory = GC.GetTotalMemory(false),
            WorkingSet = _currentProcess.WorkingSet64,
            PrivateMemory = _currentProcess.PrivateMemorySize64,
            ManagedHeap = GC.GetTotalMemory(false),
            LargeObjectHeap = GC.GetTotalMemory(false) // 简化实现，实际应该获取 LOH 大小
        };
    }

    /// <summary>
    /// 收集线程池指标
    /// </summary>
    private ThreadPoolMetrics CollectThreadPoolMetrics()
    {
        ThreadPool.GetAvailableThreads(out int workerThreads, out int completionPortThreads);
        ThreadPool.GetMaxThreads(out int maxWorkerThreads, out int maxCompletionPortThreads);

        return new ThreadPoolMetrics
        {
            WorkerThreads = maxWorkerThreads - workerThreads,
            CompletionPortThreads = maxCompletionPortThreads - completionPortThreads,
            MaxWorkerThreads = maxWorkerThreads,
            MaxCompletionPortThreads = maxCompletionPortThreads,
            QueuedWorkItems = ThreadPool.ThreadCount // 简化实现
        };
    }

    /// <summary>
    /// 收集 GC 指标
    /// </summary>
    private GCMetrics CollectGCMetrics()
    {
        return new GCMetrics
        {
            Gen0Collections = GC.CollectionCount(0),
            Gen1Collections = GC.CollectionCount(1),
            Gen2Collections = GC.CollectionCount(2),
            TotalAllocatedBytes = GC.GetTotalAllocatedBytes(false)
        };
    }

    /// <summary>
    /// 获取 CPU 使用率
    /// </summary>
    private double GetCpuUsage()
    {
        try
        {
            _currentProcess.Refresh();
            return _currentProcess.TotalProcessorTime.TotalMilliseconds / Environment.ProcessorCount / Environment.TickCount * 100;
        }
        catch
        {
            return 0; // 如果无法获取 CPU 使用率，返回 0
        }
    }
}

/// <summary>
/// 网络指标收集器实现
/// </summary>
public class NetworkMetricsCollector : INetworkMetricsCollector
{
    private readonly object _lockObject = new();

    private long _bytesSent;
    private long _bytesReceived;
    private long _networkErrors;
    private long _connectionResets;

    // 速率计算相关
    private DateTime _lastRateCalculation = DateTime.UtcNow;
    private long _lastBytesSent;
    private long _lastBytesReceived;

    /// <summary>
    /// 收集网络指标
    /// </summary>
    public NetworkMetrics CollectNetworkMetrics()
    {
        lock (_lockObject)
        {
            var now = DateTime.UtcNow;
            var timeDiff = (now - _lastRateCalculation).TotalSeconds;

            var sendRate = timeDiff > 0 ? (_bytesSent - _lastBytesSent) / timeDiff : 0;
            var receiveRate = timeDiff > 0 ? (_bytesReceived - _lastBytesReceived) / timeDiff : 0;

            _lastRateCalculation = now;
            _lastBytesSent = _bytesSent;
            _lastBytesReceived = _bytesReceived;

            return new NetworkMetrics
            {
                BytesSent = _bytesSent,
                BytesReceived = _bytesReceived,
                SendRate = sendRate,
                ReceiveRate = receiveRate,
                NetworkErrors = _networkErrors,
                ConnectionResets = _connectionResets
            };
        }
    }

    /// <summary>
    /// 记录网络发送
    /// </summary>
    public void RecordBytesSent(long bytes)
    {
        Interlocked.Add(ref _bytesSent, bytes);
    }

    /// <summary>
    /// 记录网络接收
    /// </summary>
    public void RecordBytesReceived(long bytes)
    {
        Interlocked.Add(ref _bytesReceived, bytes);
    }

    /// <summary>
    /// 记录网络错误
    /// </summary>
    public void RecordNetworkError(string errorType)
    {
        Interlocked.Increment(ref _networkErrors);
    }

    /// <summary>
    /// 记录连接重置
    /// </summary>
    public void RecordConnectionReset()
    {
        Interlocked.Increment(ref _connectionResets);
    }
}
