﻿using System;
using System.Collections.Generic;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("简单MQTT测试客户端启动...");

        try
        {
            Console.WriteLine("尝试连接到 127.0.0.1:1883...");

            using var client = new TcpClient();

            // 设置连接超时
            var connectTask = client.ConnectAsync("127.0.0.1", 1883);
            var timeoutTask = Task.Delay(5000);

            var completedTask = await Task.WhenAny(connectTask, timeoutTask);

            if (completedTask == timeoutTask)
            {
                Console.WriteLine("❌ 连接超时");
                return;
            }

            if (connectTask.IsFaulted)
            {
                Console.WriteLine($"❌ 连接失败: {connectTask.Exception?.GetBaseException().Message}");
                return;
            }

            Console.WriteLine("✅ TCP连接成功建立");

            var stream = client.GetStream();

            // 发送简单的CONNECT包
            var connectPacket = CreateSimpleConnectPacket();
            Console.WriteLine($"发送CONNECT包: {BitConverter.ToString(connectPacket)}");

            await stream.WriteAsync(connectPacket);
            Console.WriteLine("✅ CONNECT包已发送");

            // 等待CONNACK响应
            var buffer = new byte[1024];
            var readTask = stream.ReadAsync(buffer, 0, buffer.Length);
            var readTimeoutTask = Task.Delay(5000);

            var readCompletedTask = await Task.WhenAny(readTask, readTimeoutTask);

            if (readCompletedTask == readTimeoutTask)
            {
                Console.WriteLine("❌ 等待CONNACK响应超时");
                return;
            }

            var bytesRead = await readTask;

            if (bytesRead > 0)
            {
                Console.WriteLine($"✅ 收到响应: {bytesRead} 字节");
                Console.WriteLine($"响应数据: {BitConverter.ToString(buffer, 0, bytesRead)}");

                if (buffer[0] == 0x20) // CONNACK包类型
                {
                    var returnCode = buffer[3];
                    if (returnCode == 0x00)
                    {
                        Console.WriteLine("🎉 MQTT连接成功！");
                    }
                    else
                    {
                        Console.WriteLine($"❌ MQTT连接被拒绝，返回码: {returnCode}");
                    }
                }
                else
                {
                    Console.WriteLine($"❓ 收到意外的包类型: 0x{buffer[0]:X2}");
                }
            }
            else
            {
                Console.WriteLine("❌ 连接被服务器关闭");
            }

        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 发生异常: {ex.Message}");
            Console.WriteLine($"异常类型: {ex.GetType().Name}");
            if (ex.InnerException != null)
            {
                Console.WriteLine($"内部异常: {ex.InnerException.Message}");
            }
        }

        Console.WriteLine("测试完成，按任意键退出...");
        Console.ReadKey();
    }

    static byte[] CreateSimpleConnectPacket()
    {
        var packet = new List<byte>();

        // 固定头部
        packet.Add(0x10); // CONNECT包类型

        // 可变头部和载荷
        var payload = new List<byte>();

        // 协议名称 "MQTT"
        payload.Add(0x00); // 长度高字节
        payload.Add(0x04); // 长度低字节
        payload.AddRange(Encoding.UTF8.GetBytes("MQTT"));

        // 协议版本
        payload.Add(0x04); // MQTT 3.1.1

        // 连接标志
        payload.Add(0x00); // 无特殊标志

        // Keep Alive
        payload.Add(0x00); // 高字节
        payload.Add(0x3C); // 低字节 (60秒)

        // 客户端ID "TestClient"
        var clientId = "TestClient";
        var clientIdBytes = Encoding.UTF8.GetBytes(clientId);
        payload.Add(0x00); // 长度高字节
        payload.Add((byte)clientIdBytes.Length); // 长度低字节
        payload.AddRange(clientIdBytes);

        // 剩余长度
        packet.Add((byte)payload.Count);
        packet.AddRange(payload);

        return packet.ToArray();
    }
}
