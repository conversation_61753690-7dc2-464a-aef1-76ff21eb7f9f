using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Topic;
using Xunit;

namespace MqttBroker.Tests.Unit.Topic;

/// <summary>
/// 主题树单元测试
/// </summary>
public class TopicTreeTests
{
    [Fact]
    public void AddSubscriber_ValidTopicFilter_ShouldAddSuccessfully()
    {
        // Arrange
        var topicTree = new TopicTree();
        var subscriber = CreateTestSubscriber("client1", "sensors/temperature", MqttQoSLevel.AtLeastOnce);

        // Act
        topicTree.AddSubscriber("sensors/temperature", subscriber);

        // Assert
        var subscribers = topicTree.GetMatchingSubscribers("sensors/temperature");
        Assert.Single(subscribers);
        Assert.Equal("client1", subscribers.First().ClientId);
    }

    [Fact]
    public void GetMatchingSubscribers_ExactMatch_ShouldReturnMatchingSubscribers()
    {
        // Arrange
        var topicTree = new TopicTree();
        var subscriber1 = CreateTestSubscriber("client1", "sensors/temperature", MqttQoSLevel.AtLeastOnce);
        var subscriber2 = CreateTestSubscriber("client2", "sensors/humidity", MqttQoSLevel.AtMostOnce);

        topicTree.AddSubscriber("sensors/temperature", subscriber1);
        topicTree.AddSubscriber("sensors/humidity", subscriber2);

        // Act
        var subscribers = topicTree.GetMatchingSubscribers("sensors/temperature");

        // Assert
        Assert.Single(subscribers);
        Assert.Equal("client1", subscribers.First().ClientId);
    }

    [Fact]
    public void GetMatchingSubscribers_SingleLevelWildcard_ShouldReturnMatchingSubscribers()
    {
        // Arrange
        var topicTree = new TopicTree();
        var subscriber = CreateTestSubscriber("client1", "sensors/+/temperature", MqttQoSLevel.AtLeastOnce);

        topicTree.AddSubscriber("sensors/+/temperature", subscriber);

        // Act
        var subscribers1 = topicTree.GetMatchingSubscribers("sensors/room1/temperature");
        var subscribers2 = topicTree.GetMatchingSubscribers("sensors/room2/temperature");
        var subscribers3 = topicTree.GetMatchingSubscribers("sensors/room1/humidity"); // 不匹配

        // Assert
        Assert.Single(subscribers1);
        Assert.Equal("client1", subscribers1.First().ClientId);
        
        Assert.Single(subscribers2);
        Assert.Equal("client1", subscribers2.First().ClientId);
        
        Assert.Empty(subscribers3);
    }

    [Fact]
    public void GetMatchingSubscribers_MultiLevelWildcard_ShouldReturnMatchingSubscribers()
    {
        // Arrange
        var topicTree = new TopicTree();
        var subscriber = CreateTestSubscriber("client1", "sensors/#", MqttQoSLevel.AtLeastOnce);

        topicTree.AddSubscriber("sensors/#", subscriber);

        // Act
        var subscribers1 = topicTree.GetMatchingSubscribers("sensors/temperature");
        var subscribers2 = topicTree.GetMatchingSubscribers("sensors/room1/temperature");
        var subscribers3 = topicTree.GetMatchingSubscribers("sensors/room1/device1/temperature");
        var subscribers4 = topicTree.GetMatchingSubscribers("devices/sensor1"); // 不匹配

        // Assert
        Assert.Single(subscribers1);
        Assert.Single(subscribers2);
        Assert.Single(subscribers3);
        Assert.Empty(subscribers4);
    }

    [Fact]
    public void GetMatchingSubscribers_MultipleSubscribers_ShouldReturnAllMatching()
    {
        // Arrange
        var topicTree = new TopicTree();
        var subscriber1 = CreateTestSubscriber("client1", "sensors/temperature", MqttQoSLevel.AtLeastOnce);
        var subscriber2 = CreateTestSubscriber("client2", "sensors/+", MqttQoSLevel.AtMostOnce);
        var subscriber3 = CreateTestSubscriber("client3", "sensors/#", MqttQoSLevel.ExactlyOnce);

        topicTree.AddSubscriber("sensors/temperature", subscriber1);
        topicTree.AddSubscriber("sensors/+", subscriber2);
        topicTree.AddSubscriber("sensors/#", subscriber3);

        // Act
        var subscribers = topicTree.GetMatchingSubscribers("sensors/temperature");

        // Assert
        Assert.Equal(3, subscribers.Count);
        var clientIds = subscribers.Select(s => s.ClientId).ToHashSet();
        Assert.Contains("client1", clientIds);
        Assert.Contains("client2", clientIds);
        Assert.Contains("client3", clientIds);
    }

    [Fact]
    public void RemoveSubscriber_ExistingSubscriber_ShouldRemoveSuccessfully()
    {
        // Arrange
        var topicTree = new TopicTree();
        var subscriber = CreateTestSubscriber("client1", "sensors/temperature", MqttQoSLevel.AtLeastOnce);

        topicTree.AddSubscriber("sensors/temperature", subscriber);

        // Act
        var removed = topicTree.RemoveSubscriber("sensors/temperature", "client1");

        // Assert
        Assert.True(removed);
        var subscribers = topicTree.GetMatchingSubscribers("sensors/temperature");
        Assert.Empty(subscribers);
    }

    [Fact]
    public void RemoveSubscriber_NonExistentSubscriber_ShouldReturnFalse()
    {
        // Arrange
        var topicTree = new TopicTree();

        // Act
        var removed = topicTree.RemoveSubscriber("sensors/temperature", "client1");

        // Assert
        Assert.False(removed);
    }

    [Fact]
    public void GetClientSubscriptions_ExistingClient_ShouldReturnSubscriptions()
    {
        // Arrange
        var topicTree = new TopicTree();
        var subscriber1 = CreateTestSubscriber("client1", "sensors/temperature", MqttQoSLevel.AtLeastOnce);
        var subscriber2 = CreateTestSubscriber("client1", "sensors/humidity", MqttQoSLevel.AtMostOnce);
        var subscriber3 = CreateTestSubscriber("client2", "devices/status", MqttQoSLevel.ExactlyOnce);

        topicTree.AddSubscriber("sensors/temperature", subscriber1);
        topicTree.AddSubscriber("sensors/humidity", subscriber2);
        topicTree.AddSubscriber("devices/status", subscriber3);

        // Act
        var subscriptions = topicTree.GetClientSubscriptions("client1");

        // Assert
        Assert.Equal(2, subscriptions.Count);
        var topicFilters = subscriptions.Select(s => s.TopicFilter).ToHashSet();
        Assert.Contains("sensors/temperature", topicFilters);
        Assert.Contains("sensors/humidity", topicFilters);
    }

    [Fact]
    public void RemoveClientSubscriptions_ExistingClient_ShouldRemoveAllSubscriptions()
    {
        // Arrange
        var topicTree = new TopicTree();
        var subscriber1 = CreateTestSubscriber("client1", "sensors/temperature", MqttQoSLevel.AtLeastOnce);
        var subscriber2 = CreateTestSubscriber("client1", "sensors/humidity", MqttQoSLevel.AtMostOnce);
        var subscriber3 = CreateTestSubscriber("client2", "devices/status", MqttQoSLevel.ExactlyOnce);

        topicTree.AddSubscriber("sensors/temperature", subscriber1);
        topicTree.AddSubscriber("sensors/humidity", subscriber2);
        topicTree.AddSubscriber("devices/status", subscriber3);

        // Act
        var removedCount = topicTree.RemoveClientSubscriptions("client1");

        // Assert
        Assert.Equal(2, removedCount);
        
        var client1Subscriptions = topicTree.GetClientSubscriptions("client1");
        Assert.Empty(client1Subscriptions);
        
        var client2Subscriptions = topicTree.GetClientSubscriptions("client2");
        Assert.Single(client2Subscriptions);
    }

    [Fact]
    public void GetStatistics_WithSubscriptions_ShouldReturnValidStatistics()
    {
        // Arrange
        var topicTree = new TopicTree();
        var subscriber1 = CreateTestSubscriber("client1", "sensors/temperature", MqttQoSLevel.AtLeastOnce);
        var subscriber2 = CreateTestSubscriber("client2", "sensors/humidity", MqttQoSLevel.AtMostOnce);

        topicTree.AddSubscriber("sensors/temperature", subscriber1);
        topicTree.AddSubscriber("sensors/humidity", subscriber2);

        // Act
        var statistics = topicTree.GetStatistics();

        // Assert
        Assert.True(statistics.TotalNodes > 0);
        Assert.True(statistics.NodesWithSubscribers > 0);
        Assert.Equal(2, statistics.TotalSubscribers);
        Assert.True(statistics.AverageSubscribersPerNode > 0);
    }

    [Fact]
    public void AddSubscriber_SameClientDifferentTopics_ShouldAddBothSubscriptions()
    {
        // Arrange
        var topicTree = new TopicTree();
        var subscriber1 = CreateTestSubscriber("client1", "sensors/temperature", MqttQoSLevel.AtLeastOnce);
        var subscriber2 = CreateTestSubscriber("client1", "sensors/humidity", MqttQoSLevel.AtMostOnce);

        // Act
        topicTree.AddSubscriber("sensors/temperature", subscriber1);
        topicTree.AddSubscriber("sensors/humidity", subscriber2);

        // Assert
        var tempSubscribers = topicTree.GetMatchingSubscribers("sensors/temperature");
        var humiditySubscribers = topicTree.GetMatchingSubscribers("sensors/humidity");
        
        Assert.Single(tempSubscribers);
        Assert.Single(humiditySubscribers);
        Assert.Equal("client1", tempSubscribers.First().ClientId);
        Assert.Equal("client1", humiditySubscribers.First().ClientId);
    }

    [Fact]
    public void AddSubscriber_SameClientSameTopic_ShouldUpdateSubscription()
    {
        // Arrange
        var topicTree = new TopicTree();
        var subscriber1 = CreateTestSubscriber("client1", "sensors/temperature", MqttQoSLevel.AtMostOnce);
        var subscriber2 = CreateTestSubscriber("client1", "sensors/temperature", MqttQoSLevel.AtLeastOnce);

        // Act
        topicTree.AddSubscriber("sensors/temperature", subscriber1);
        topicTree.AddSubscriber("sensors/temperature", subscriber2); // 更新订阅

        // Assert
        var subscribers = topicTree.GetMatchingSubscribers("sensors/temperature");
        Assert.Single(subscribers);
        Assert.Equal(MqttQoSLevel.AtLeastOnce, subscribers.First().QoSLevel);
    }

    private static TopicSubscriber CreateTestSubscriber(string clientId, string topicFilter, MqttQoSLevel qosLevel)
    {
        return new TopicSubscriber
        {
            ClientId = clientId,
            TopicFilter = topicFilter,
            QoSLevel = qosLevel,
            SubscribedAt = DateTime.UtcNow,
            Options = new MqttSubscriptionOptions()
        };
    }
}
