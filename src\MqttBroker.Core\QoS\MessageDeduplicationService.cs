using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using System.Collections.Concurrent;

namespace MqttBroker.Core.QoS;

/// <summary>
/// 消息去重服务实现，负责 QoS 2 消息的去重处理
/// </summary>
public class MessageDeduplicationService : IMessageDeduplicationService, IDisposable
{
    private readonly ILogger<MessageDeduplicationService> _logger;
    private readonly MessageDeduplicationOptions _options;

    // 已处理消息记录存储：ClientId -> MessageId -> ProcessedMessageRecord
    private readonly ConcurrentDictionary<string, ConcurrentDictionary<ushort, ProcessedMessageRecord>> _processedMessages = new();

    // 统计信息
    private long _totalCheckedMessages;
    private long _totalDuplicateMessages;
    private long _totalExpiredRecordsCleanedUp;

    private readonly Timer? _cleanupTimer;
    private bool _isStarted;
    private bool _disposed;

    /// <summary>
    /// 初始化消息去重服务
    /// </summary>
    public MessageDeduplicationService(
        ILogger<MessageDeduplicationService> logger,
        IOptions<MessageDeduplicationOptions> options)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));

        if (_options.EnableDeduplication)
        {
            _cleanupTimer = new Timer(CleanupExpiredRecordsCallback, null,
                Timeout.InfiniteTimeSpan, Timeout.InfiniteTimeSpan);
        }

        _logger.LogInformation("Message Deduplication Service initialized with options: Enabled={Enabled}, ExpirationMs={Expiration}, UseContentHash={UseContentHash}",
            _options.EnableDeduplication, _options.DefaultRecordExpirationMs, _options.UseContentHashForDeduplication);
    }

    /// <summary>
    /// 启动去重服务
    /// </summary>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (_isStarted || !_options.EnableDeduplication)
        {
            _logger.LogInformation("Deduplication service is already started or disabled");
            return;
        }

        try
        {
            _logger.LogInformation("Starting Message Deduplication Service...");

            // 启动清理定时器
            _cleanupTimer?.Change(
                TimeSpan.FromMilliseconds(_options.ExpiredRecordCleanupIntervalMs),
                TimeSpan.FromMilliseconds(_options.ExpiredRecordCleanupIntervalMs));

            _isStarted = true;
            _logger.LogInformation("Message Deduplication Service started successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start Message Deduplication Service");
            throw;
        }
    }

    /// <summary>
    /// 停止去重服务
    /// </summary>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (!_isStarted)
        {
            _logger.LogInformation("Deduplication service is not started");
            return;
        }

        try
        {
            _logger.LogInformation("Stopping Message Deduplication Service...");

            // 停止清理定时器
            _cleanupTimer?.Change(Timeout.InfiniteTimeSpan, Timeout.InfiniteTimeSpan);

            _isStarted = false;
            _logger.LogInformation("Message Deduplication Service stopped successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to stop Message Deduplication Service");
            throw;
        }
    }

    /// <summary>
    /// 检查消息是否为重复消息
    /// </summary>
    public async Task<DeduplicationResult> CheckDuplicateAsync(string clientId, ushort messageId, MqttPublishPacket publishPacket)
    {
        if (string.IsNullOrEmpty(clientId) || !_options.EnableDeduplication)
        {
            return DeduplicationResult.NotDuplicate(clientId, messageId);
        }

        try
        {
            Interlocked.Increment(ref _totalCheckedMessages);

            if (!_processedMessages.TryGetValue(clientId, out var clientRecords))
            {
                return DeduplicationResult.NotDuplicate(clientId, messageId);
            }

            if (!clientRecords.TryGetValue(messageId, out var record))
            {
                return DeduplicationResult.NotDuplicate(clientId, messageId);
            }

            // 检查记录是否已过期
            if (record.IsExpired)
            {
                // 移除过期记录
                clientRecords.TryRemove(messageId, out _);
                return DeduplicationResult.NotDuplicate(clientId, messageId);
            }

            // 如果启用内容哈希验证
            if (_options.UseContentHashForDeduplication)
            {
                var messageHash = ProcessedMessageRecord.GenerateMessageHash(publishPacket);
                if (record.MessageHash != messageHash)
                {
                    _logger.LogWarning("Message ID {MessageId} for client {ClientId} exists but content hash differs. Possible message ID reuse.",
                        messageId, clientId);
                    return DeduplicationResult.NotDuplicate(clientId, messageId);
                }
            }

            // 更新重复检测统计
            record.DuplicateDetectionCount++;
            record.LastDuplicateDetectedAt = DateTime.UtcNow;

            Interlocked.Increment(ref _totalDuplicateMessages);

            _logger.LogTrace("Duplicate message detected for client {ClientId}, MessageId: {MessageId}, OriginalTime: {OriginalTime}",
                clientId, messageId, record.ProcessedAt);

            return DeduplicationResult.Duplicate(clientId, messageId, record.ProcessedAt, record.MessageHash);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking duplicate message for client {ClientId}, MessageId: {MessageId}", clientId, messageId);
            return DeduplicationResult.NotDuplicate(clientId, messageId);
        }
    }

    /// <summary>
    /// 记录已处理的消息
    /// </summary>
    public async Task<bool> RecordProcessedMessageAsync(string clientId, ushort messageId, MqttPublishPacket publishPacket, TimeSpan? expirationTime = null)
    {
        if (string.IsNullOrEmpty(clientId) || !_options.EnableDeduplication)
        {
            return true;
        }

        try
        {
            var clientRecords = _processedMessages.GetOrAdd(clientId, _ => new ConcurrentDictionary<ushort, ProcessedMessageRecord>());

            // 检查客户端记录数量限制
            if (clientRecords.Count >= _options.MaxProcessedRecordsPerClient)
            {
                _logger.LogWarning("Client {ClientId} has reached maximum processed records limit: {Limit}", 
                    clientId, _options.MaxProcessedRecordsPerClient);
                
                // 清理一些旧记录
                await CleanupOldestRecordsForClient(clientId, clientRecords.Count / 2);
            }

            var expiration = expirationTime ?? TimeSpan.FromMilliseconds(_options.DefaultRecordExpirationMs);
            var record = new ProcessedMessageRecord
            {
                ClientId = clientId,
                MessageId = messageId,
                Topic = publishPacket.Topic,
                QoSLevel = publishPacket.QoSLevel,
                ProcessedAt = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.Add(expiration)
            };

            if (_options.UseContentHashForDeduplication)
            {
                record.MessageHash = ProcessedMessageRecord.GenerateMessageHash(publishPacket);
            }

            var added = clientRecords.TryAdd(messageId, record);
            
            if (added)
            {
                _logger.LogTrace("Recorded processed message for client {ClientId}, MessageId: {MessageId}, Topic: {Topic}",
                    clientId, messageId, publishPacket.Topic);
            }
            else
            {
                _logger.LogTrace("Message {MessageId} already recorded for client {ClientId}", messageId, clientId);
            }

            return added;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording processed message for client {ClientId}, MessageId: {MessageId}", clientId, messageId);
            return false;
        }
    }

    /// <summary>
    /// 移除已处理的消息记录
    /// </summary>
    public async Task<bool> RemoveProcessedMessageAsync(string clientId, ushort messageId)
    {
        if (string.IsNullOrEmpty(clientId))
            return false;

        try
        {
            if (_processedMessages.TryGetValue(clientId, out var clientRecords))
            {
                var removed = clientRecords.TryRemove(messageId, out var record);
                
                if (removed)
                {
                    _logger.LogTrace("Removed processed message record {MessageId} for client {ClientId}", messageId, clientId);
                }

                return removed;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing processed message record {MessageId} for client {ClientId}", messageId, clientId);
            return false;
        }
    }

    /// <summary>
    /// 清理客户端的所有已处理消息记录
    /// </summary>
    public async Task<int> ClearClientProcessedMessagesAsync(string clientId)
    {
        if (string.IsNullOrEmpty(clientId))
            return 0;

        try
        {
            if (_processedMessages.TryRemove(clientId, out var clientRecords))
            {
                var count = clientRecords.Count;
                _logger.LogInformation("Cleared {Count} processed message records for client {ClientId}", count, clientId);
                return count;
            }

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing processed message records for client {ClientId}", clientId);
            return 0;
        }
    }

    /// <summary>
    /// 清理过期的消息记录
    /// </summary>
    public async Task<int> CleanupExpiredRecordsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var cleanedCount = 0;
            var clientsToRemove = new List<string>();

            foreach (var kvp in _processedMessages)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                var clientId = kvp.Key;
                var clientRecords = kvp.Value;
                var expiredRecords = new List<ushort>();

                foreach (var recordKvp in clientRecords)
                {
                    if (recordKvp.Value.IsExpired)
                    {
                        expiredRecords.Add(recordKvp.Key);
                    }
                }

                // 移除过期记录
                foreach (var messageId in expiredRecords)
                {
                    if (clientRecords.TryRemove(messageId, out _))
                    {
                        cleanedCount++;
                    }
                }

                // 如果客户端没有记录了，标记为待移除
                if (clientRecords.IsEmpty)
                {
                    clientsToRemove.Add(clientId);
                }
            }

            // 移除空的客户端记录
            foreach (var clientId in clientsToRemove)
            {
                _processedMessages.TryRemove(clientId, out _);
            }

            if (cleanedCount > 0)
            {
                Interlocked.Add(ref _totalExpiredRecordsCleanedUp, cleanedCount);
                _logger.LogInformation("Cleaned up {Count} expired processed message records", cleanedCount);
            }

            return cleanedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up expired records");
            return 0;
        }
    }

    /// <summary>
    /// 获取去重统计信息
    /// </summary>
    public async Task<DeduplicationStatistics> GetStatisticsAsync()
    {
        try
        {
            var statistics = new DeduplicationStatistics
            {
                TotalCheckedMessages = Interlocked.Read(ref _totalCheckedMessages),
                TotalDuplicateMessages = Interlocked.Read(ref _totalDuplicateMessages),
                TotalExpiredRecordsCleanedUp = Interlocked.Read(ref _totalExpiredRecordsCleanedUp),
                ClientsWithProcessedMessages = _processedMessages.Count,
                LastCleanupTime = DateTime.UtcNow
            };

            var allRecords = new List<ProcessedMessageRecord>();
            foreach (var clientRecords in _processedMessages.Values)
            {
                allRecords.AddRange(clientRecords.Values);
            }

            statistics.CurrentProcessedMessageRecords = allRecords.Count;

            if (allRecords.Any())
            {
                statistics.AverageRecordAge = allRecords.Average(r => r.Age.TotalMilliseconds);
                statistics.OldestRecordAge = allRecords.Max(r => r.Age.TotalMilliseconds);
            }

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting deduplication statistics");
            return new DeduplicationStatistics();
        }
    }

    /// <summary>
    /// 获取客户端的已处理消息记录
    /// </summary>
    public async Task<IList<ProcessedMessageRecord>> GetClientProcessedMessagesAsync(string clientId)
    {
        if (string.IsNullOrEmpty(clientId))
            return new List<ProcessedMessageRecord>();

        try
        {
            if (_processedMessages.TryGetValue(clientId, out var clientRecords))
            {
                return clientRecords.Values.ToList();
            }

            return new List<ProcessedMessageRecord>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting processed messages for client {ClientId}", clientId);
            return new List<ProcessedMessageRecord>();
        }
    }

    /// <summary>
    /// 清理客户端最旧的记录
    /// </summary>
    private async Task CleanupOldestRecordsForClient(string clientId, int countToRemove)
    {
        try
        {
            if (!_processedMessages.TryGetValue(clientId, out var clientRecords))
                return;

            var oldestRecords = clientRecords.Values
                .OrderBy(r => r.ProcessedAt)
                .Take(countToRemove)
                .ToList();

            foreach (var record in oldestRecords)
            {
                clientRecords.TryRemove(record.MessageId, out _);
            }

            _logger.LogTrace("Cleaned up {Count} oldest records for client {ClientId}", oldestRecords.Count, clientId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up oldest records for client {ClientId}", clientId);
        }
    }

    /// <summary>
    /// 清理过期记录定时器回调
    /// </summary>
    private async void CleanupExpiredRecordsCallback(object? state)
    {
        try
        {
            await CleanupExpiredRecordsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in cleanup timer callback");
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            _cleanupTimer?.Dispose();
            _processedMessages.Clear();
            
            _disposed = true;
            _logger.LogInformation("Message Deduplication Service disposed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing Message Deduplication Service");
        }
    }
}
