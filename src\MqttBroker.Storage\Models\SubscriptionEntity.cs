using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Topic;
using MqttBroker.Core.QoS;
using System.Text.Json;

namespace MqttBroker.Storage.Models;

/// <summary>
/// 订阅实体
/// </summary>
[Table("Subscriptions")]
public class SubscriptionEntity
{
    /// <summary>
    /// 订阅ID（主键）
    /// </summary>
    [Key]
    public long Id { get; set; }

    /// <summary>
    /// 客户端ID（外键）
    /// </summary>
    [Required]
    [MaxLength(256)]
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 主题过滤器
    /// </summary>
    [Required]
    [MaxLength(1024)]
    public string TopicFilter { get; set; } = string.Empty;

    /// <summary>
    /// QoS级别
    /// </summary>
    public int QoSLevel { get; set; }

    /// <summary>
    /// 订阅选项（JSON 序列化）
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? OptionsJson { get; set; }

    /// <summary>
    /// 订阅时间
    /// </summary>
    public DateTime SubscribedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 是否活跃
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 最后匹配时间
    /// </summary>
    public DateTime? LastMatchedAt { get; set; }

    /// <summary>
    /// 匹配次数
    /// </summary>
    public long MatchCount { get; set; }

    /// <summary>
    /// 会话实体（导航属性）
    /// </summary>
    [ForeignKey(nameof(ClientId))]
    public virtual SessionEntity? Session { get; set; }

    /// <summary>
    /// 转换为客户端订阅
    /// </summary>
    /// <returns>客户端订阅</returns>
    public ClientSubscription ToClientSubscription()
    {
        var subscription = new ClientSubscription
        {
            ClientId = ClientId,
            TopicFilter = TopicFilter,
            QoSLevel = (MqttQoSLevel)QoSLevel,
            SubscribedAt = SubscribedAt
        };

        // 反序列化订阅选项
        if (!string.IsNullOrEmpty(OptionsJson))
        {
            try
            {
                subscription.Options = JsonSerializer.Deserialize<MqttSubscriptionOptions>(OptionsJson) ?? new MqttSubscriptionOptions();
            }
            catch
            {
                subscription.Options = new MqttSubscriptionOptions();
            }
        }
        else
        {
            subscription.Options = new MqttSubscriptionOptions();
        }

        return subscription;
    }

    /// <summary>
    /// 从客户端订阅创建实体
    /// </summary>
    /// <param name="subscription">客户端订阅</param>
    /// <returns>订阅实体</returns>
    public static SubscriptionEntity FromClientSubscription(ClientSubscription subscription)
    {
        var entity = new SubscriptionEntity
        {
            ClientId = subscription.ClientId,
            TopicFilter = subscription.TopicFilter,
            QoSLevel = (int)subscription.QoSLevel,
            SubscribedAt = subscription.SubscribedAt,
            IsActive = true,
            MatchCount = 0
        };

        // 序列化订阅选项
        if (subscription.Options != null)
        {
            try
            {
                entity.OptionsJson = JsonSerializer.Serialize(subscription.Options);
            }
            catch
            {
                // 忽略序列化错误
            }
        }

        return entity;
    }

    /// <summary>
    /// 更新匹配统计
    /// </summary>
    public void UpdateMatchStatistics()
    {
        LastMatchedAt = DateTime.UtcNow;
        MatchCount++;
    }

    /// <summary>
    /// 检查订阅是否匹配主题
    /// </summary>
    /// <param name="topic">主题名称</param>
    /// <returns>是否匹配</returns>
    public bool MatchesTopic(string topic)
    {
        if (string.IsNullOrEmpty(topic) || string.IsNullOrEmpty(TopicFilter))
            return false;

        // 简单的主题匹配逻辑（实际应该使用 MqttTopicMatcher）
        if (TopicFilter == topic)
            return true;

        // 处理通配符
        if (TopicFilter.Contains('+') || TopicFilter.Contains('#'))
        {
            // 这里应该使用完整的 MQTT 主题匹配算法
            // 为了简化，这里只做基本匹配
            return TopicFilter.Replace("+", "[^/]+").Replace("#", ".*") == topic;
        }

        return false;
    }
}

/// <summary>
/// 未确认消息实体
/// </summary>
[Table("PendingMessages")]
public class PendingMessageEntity
{
    /// <summary>
    /// 消息ID（主键）
    /// </summary>
    [Key]
    public long Id { get; set; }

    /// <summary>
    /// 客户端ID（外键）
    /// </summary>
    [Required]
    [MaxLength(256)]
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// MQTT 消息ID
    /// </summary>
    public ushort MessageId { get; set; }

    /// <summary>
    /// 主题名称
    /// </summary>
    [Required]
    [MaxLength(1024)]
    public string Topic { get; set; } = string.Empty;

    /// <summary>
    /// 消息载荷
    /// </summary>
    [Column(TypeName = "BLOB")]
    public byte[] Payload { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// QoS级别
    /// </summary>
    public int QoSLevel { get; set; }

    /// <summary>
    /// 是否保留
    /// </summary>
    public bool Retain { get; set; }

    /// <summary>
    /// 是否重复
    /// </summary>
    public bool Duplicate { get; set; }

    /// <summary>
    /// QoS 2 消息状态
    /// </summary>
    public int QoS2State { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后重传时间
    /// </summary>
    public DateTime? LastRetransmissionAt { get; set; }

    /// <summary>
    /// 重传次数
    /// </summary>
    public int RetransmissionCount { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// 消息属性（JSON 序列化）
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? PropertiesJson { get; set; }

    /// <summary>
    /// 会话实体（导航属性）
    /// </summary>
    [ForeignKey(nameof(ClientId))]
    public virtual SessionEntity? Session { get; set; }

    /// <summary>
    /// 转换为待确认消息
    /// </summary>
    /// <returns>待确认消息</returns>
    public PendingMessage ToPendingMessage()
    {
        var publishPacket = MqttPublishPacket.Create(Topic, Payload, (MqttQoSLevel)QoSLevel, Retain, MessageId);
        publishPacket.Duplicate = Duplicate;

        var pendingMessage = new PendingMessage
        {
            ClientId = ClientId,
            MessageId = MessageId,
            PublishPacket = publishPacket,
            QoSLevel = (MqttQoSLevel)QoSLevel,
            QoS2State = (QoS2MessageState)QoS2State,
            CreatedAt = CreatedAt,
            LastRetransmissionAt = LastRetransmissionAt,
            RetransmissionCount = RetransmissionCount
        };

        return pendingMessage;
    }

    /// <summary>
    /// 从待确认消息创建实体
    /// </summary>
    /// <param name="pendingMessage">待确认消息</param>
    /// <returns>未确认消息实体</returns>
    public static PendingMessageEntity FromPendingMessage(PendingMessage pendingMessage)
    {
        var entity = new PendingMessageEntity
        {
            ClientId = pendingMessage.ClientId,
            MessageId = pendingMessage.MessageId,
            Topic = pendingMessage.PublishPacket.Topic,
            Payload = pendingMessage.PublishPacket.Payload,
            QoSLevel = (int)pendingMessage.QoSLevel,
            Retain = pendingMessage.PublishPacket.Retain,
            Duplicate = pendingMessage.PublishPacket.Duplicate,
            QoS2State = (int)pendingMessage.QoS2State,
            CreatedAt = pendingMessage.CreatedAt,
            LastRetransmissionAt = pendingMessage.LastRetransmissionAt,
            RetransmissionCount = pendingMessage.RetransmissionCount,
            ExpiresAt = DateTime.UtcNow.AddHours(24) // 默认24小时过期
        };

        return entity;
    }

    /// <summary>
    /// 更新重传信息
    /// </summary>
    public void UpdateRetransmissionInfo()
    {
        LastRetransmissionAt = DateTime.UtcNow;
        RetransmissionCount++;
    }

    /// <summary>
    /// 检查消息是否过期
    /// </summary>
    /// <returns>是否过期</returns>
    public bool IsExpired()
    {
        return ExpiresAt.HasValue && DateTime.UtcNow > ExpiresAt.Value;
    }
}
