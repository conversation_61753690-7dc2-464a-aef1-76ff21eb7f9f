using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MqttBroker.Core.Message.Filters;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;

namespace MqttBroker.Core.Message.Examples;

/// <summary>
/// 消息路由引擎使用示例
/// </summary>
public class MessageRoutingExample
{
    /// <summary>
    /// 配置消息路由引擎服务
    /// </summary>
    /// <param name="services">服务集合</param>
    public static void ConfigureServices(IServiceCollection services)
    {
        // 添加基础服务
        services.AddLogging(builder => builder.AddConsole());

        // 添加消息路由引擎
        services.AddMessageRoutingEngine(
            routing =>
            {
                routing.MaxConcurrentRoutings = 2000;
                routing.RoutingTimeoutMs = 3000;
                routing.EnableMessageFiltering = true;
                routing.EnableOfflineMessageStorage = true;
                routing.EnableDeadLetterQueue = true;
                routing.MaxOfflineMessagesPerClient = 500;
                routing.OfflineMessageExpirationHours = 48;
                routing.EnableBatchRoutingOptimization = true;
                routing.BatchRoutingSize = 50;
                routing.MaxRetryAttempts = 5;
                routing.RetryIntervalMs = 2000;
            },
            filters =>
            {
                // 启用消息大小过滤器
                filters.EnableMessageSizeFilter = true;
                filters.MaxMessageSize = 512 * 1024; // 512KB

                // 启用主题黑名单过滤器
                filters.EnableTopicBlacklistFilter = true;
                filters.BlacklistedTopics.Add("admin/internal");
                filters.BlacklistedTopics.Add("system/debug");
                filters.BlacklistedPatterns.Add("temp/*");
                filters.BlacklistedPatterns.Add("*/private");

                // 启用内容验证过滤器
                filters.EnableContentValidationFilter = true;
                filters.ValidateContent = true;
                filters.AllowEmptyMessages = false;
                filters.RequireValidJson = false;
                filters.ForbiddenWords.Add("spam");
                filters.ForbiddenWords.Add("malware");

                // 启用频率限制过滤器
                filters.EnableRateLimitFilter = true;
                filters.EnableRateLimit = true;
                filters.WindowSizeSeconds = 30;
                filters.MaxMessagesPerWindow = 100;
            });

        // 添加自定义过滤器
        services.AddMessageFilter<CustomSecurityFilter>();

        // 配置过滤器选项
        services.ConfigureMessageRoutingEngine(
            routing =>
            {
                routing.EnableRoutingStatistics = true;
            },
            sizeFilter =>
            {
                sizeFilter.MaxMessageSize = 1024 * 1024; // 1MB
            },
            blacklistFilter =>
            {
                blacklistFilter.BlacklistedTopics.Add("forbidden/topic");
            },
            contentFilter =>
            {
                contentFilter.RequireValidJson = true;
            },
            rateLimitFilter =>
            {
                rateLimitFilter.MaxMessagesPerWindow = 200;
            });
    }

    /// <summary>
    /// 演示消息路由功能
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    public static async Task DemonstrateMessageRoutingAsync(IServiceProvider serviceProvider)
    {
        var routingEngine = serviceProvider.GetRequiredService<IMessageRoutingEngine>();
        var logger = serviceProvider.GetRequiredService<ILogger<MessageRoutingExample>>();

        // 订阅事件
        routingEngine.MessageRouted += (sender, args) =>
        {
            logger.LogInformation("Message routed successfully: Topic={Topic}, Online={Online}, Offline={Offline}",
                args.Result.TopicName, args.Result.OnlineDeliveries, args.Result.OfflineStorages);
        };

        routingEngine.MessageRoutingFailed += (sender, args) =>
        {
            logger.LogWarning("Message routing failed: Topic={Topic}, Error={Error}",
                args.TopicName, args.ErrorMessage);
        };

        routingEngine.OfflineMessageStored += (sender, args) =>
        {
            logger.LogInformation("Offline message stored: Client={ClientId}, Topic={Topic}, MessageId={MessageId}",
                args.ClientId, args.TopicName, args.MessageId);
        };

        // 创建测试消息
        var testMessages = new[]
        {
            MqttPublishPacket.Create("sensors/temperature", "25.5"u8.ToArray(), MqttQoSLevel.AtLeastOnce),
            MqttPublishPacket.Create("sensors/humidity", "60.2"u8.ToArray(), MqttQoSLevel.AtMostOnce),
            MqttPublishPacket.Create("devices/status", "{\"online\": true}"u8.ToArray(), MqttQoSLevel.ExactlyOnce),
            MqttPublishPacket.Create("alerts/fire", "EMERGENCY"u8.ToArray(), MqttQoSLevel.AtLeastOnce)
        };

        // 单个消息路由
        logger.LogInformation("=== 单个消息路由测试 ===");
        foreach (var message in testMessages)
        {
            var result = await routingEngine.RouteMessageAsync(message, "test-publisher");
            
            logger.LogInformation("Routed message to topic '{Topic}': Success={Success}, Online={Online}, Offline={Offline}, Failed={Failed}",
                message.Topic, result.IsSuccess, result.OnlineDeliveries, result.OfflineStorages, result.FailedDeliveries);
        }

        // 批量消息路由
        logger.LogInformation("=== 批量消息路由测试 ===");
        var batchRequests = testMessages.Select(msg => 
            MessageRoutingRequest.Create(msg, "batch-publisher", MessageRoutingPriority.Normal)).ToList();

        var batchResults = await routingEngine.RouteMessagesAsync(batchRequests);
        
        logger.LogInformation("Batch routing completed: {SuccessCount}/{TotalCount} messages routed successfully",
            batchResults.Count(r => r.IsSuccess), batchResults.Count);

        // 离线消息处理
        logger.LogInformation("=== 离线消息处理测试 ===");
        var offlineResult = await routingEngine.ProcessOfflineMessagesAsync("offline-client");
        
        logger.LogInformation("Offline message processing: Processed={Processed}, Delivered={Delivered}, Failed={Failed}",
            offlineResult.ProcessedMessages, offlineResult.DeliveredMessages, offlineResult.FailedMessages);

        // 获取统计信息
        logger.LogInformation("=== 路由统计信息 ===");
        var statistics = await routingEngine.GetStatisticsAsync();
        
        logger.LogInformation("Routing Statistics:");
        logger.LogInformation("  Total Messages: {Total}", statistics.TotalMessages);
        logger.LogInformation("  Online Deliveries: {Online}", statistics.OnlineDeliveries);
        logger.LogInformation("  Offline Storages: {Offline}", statistics.OfflineStorages);
        logger.LogInformation("  Failed Routings: {Failed}", statistics.FailedRoutings);
        logger.LogInformation("  Filtered Messages: {Filtered}", statistics.FilteredMessages);
        logger.LogInformation("  Success Rate: {SuccessRate:F2}%", statistics.SuccessRate);
        logger.LogInformation("  Average Latency: {AvgLatency:F2}ms", statistics.AverageRoutingLatency);
        logger.LogInformation("  Max Latency: {MaxLatency}ms", statistics.MaxRoutingLatency);
    }

    /// <summary>
    /// 演示过滤器功能
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    public static async Task DemonstrateMessageFiltersAsync(IServiceProvider serviceProvider)
    {
        var filterManager = serviceProvider.GetRequiredService<IMessageFilterManager>();
        var logger = serviceProvider.GetRequiredService<ILogger<MessageRoutingExample>>();

        logger.LogInformation("=== 消息过滤器测试 ===");

        // 注册内置过滤器
        var sizeFilter = serviceProvider.GetService<MessageSizeFilter>();
        var blacklistFilter = serviceProvider.GetService<TopicBlacklistFilter>();
        var contentFilter = serviceProvider.GetService<ContentValidationFilter>();
        var rateLimitFilter = serviceProvider.GetService<RateLimitFilter>();
        var customFilter = serviceProvider.GetService<CustomSecurityFilter>();

        if (sizeFilter != null) filterManager.RegisterFilter(sizeFilter);
        if (blacklistFilter != null) filterManager.RegisterFilter(blacklistFilter);
        if (contentFilter != null) filterManager.RegisterFilter(contentFilter);
        if (rateLimitFilter != null) filterManager.RegisterFilter(rateLimitFilter);
        if (customFilter != null) filterManager.RegisterFilter(customFilter);

        // 获取过滤器列表
        var filters = filterManager.GetFilters();
        logger.LogInformation("Registered {FilterCount} filters:", filters.Count);
        foreach (var filter in filters)
        {
            logger.LogInformation("  - {FilterName} (Priority: {Priority}, Enabled: {Enabled})",
                filter.Name, filter.Priority, filter.IsEnabled);
        }

        // 测试过滤器
        var testPacket = MqttPublishPacket.Create("test/topic", "test message"u8.ToArray(), MqttQoSLevel.AtLeastOnce);
        var testSubscriber = new Topic.TopicSubscriber
        {
            ClientId = "test-client",
            TopicFilter = "test/+",
            QoSLevel = MqttQoSLevel.AtLeastOnce
        };

        var filterContext = MessageFilterContext.Create(testPacket, testSubscriber, "test-publisher");
        var filterResult = await filterManager.ApplyFiltersAsync(filterContext);

        logger.LogInformation("Filter result: Allowed={Allowed}, Reason={Reason}, ElapsedMs={ElapsedMs}",
            filterResult.IsAllowed, filterResult.Reason, filterResult.ElapsedMilliseconds);

        // 获取过滤器统计信息
        var filterStats = await filterManager.GetStatisticsAsync();
        logger.LogInformation("Filter Statistics:");
        logger.LogInformation("  Total Filters: {Total}", filterStats.TotalFilters);
        logger.LogInformation("  Allowed Messages: {Allowed}", filterStats.AllowedMessages);
        logger.LogInformation("  Denied Messages: {Denied}", filterStats.DeniedMessages);
        logger.LogInformation("  Pass Rate: {PassRate:F2}%", filterStats.PassRate);
        logger.LogInformation("  Average Latency: {AvgLatency:F2}ms", filterStats.AverageFilterLatency);
    }
}

/// <summary>
/// 自定义安全过滤器示例
/// </summary>
public class CustomSecurityFilter : IMessageFilter
{
    private readonly ILogger<CustomSecurityFilter> _logger;

    public string Name => "CustomSecurityFilter";
    public int Priority => 50;
    public bool IsEnabled { get; set; } = true;

    public CustomSecurityFilter(ILogger<CustomSecurityFilter> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public Task<MessageFilterResult> FilterAsync(MessageFilterContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            // 示例：检查是否为敏感主题
            if (context.PublishPacket.Topic.Contains("security") || context.PublishPacket.Topic.Contains("admin"))
            {
                _logger.LogWarning("Security-sensitive topic detected: {Topic}", context.PublishPacket.Topic);
                
                // 可以在这里添加额外的安全检查逻辑
                // 例如：验证发布者权限、记录安全日志等
            }

            // 允许消息通过
            return Task.FromResult(MessageFilterResult.Allow());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in CustomSecurityFilter");
            return Task.FromResult(MessageFilterResult.Allow());
        }
    }
}
