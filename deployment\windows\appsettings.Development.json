{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "MqttBroker": "Debug"}, "Console": {"IncludeScopes": true, "TimestampFormat": "yyyy-MM-dd HH:mm:ss.fff ", "LogToStandardErrorThreshold": "Error"}, "File": {"Path": "C:\\MqttBroker\\logs\\mqtt-broker-.log", "RollingInterval": "Day", "RetainedFileCountLimit": 7, "FileSizeLimitBytes": 52428800, "IncludeScopes": true, "OutputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}, "MqttBroker": {"Network": {"Tcp": {"Enabled": true, "Port": 1883, "Address": "127.0.0.1", "Backlog": 100, "NoDelay": true}, "Tls": {"Enabled": false, "Port": 8883, "Address": "127.0.0.1", "CertificatePath": "", "CertificatePassword": "", "RequireClientCertificate": false}, "WebSocket": {"Enabled": true, "Port": 8080, "SecurePort": 8443, "Path": "/mqtt", "SubProtocols": ["mqtt", "mqttv3.1", "mqttv3.1.1", "mqttv5.0"]}, "Connection": {"MaxConnections": 1000, "ConnectionTimeout": 30, "KeepAliveTimeout": 60, "ReceiveBufferSize": 8192, "SendBufferSize": 8192, "EnableRateLimit": true, "MaxConnectionsPerSecond": 50}, "Performance": {"UseMemoryPool": true, "MaxPooledBufferSize": 65536, "EnableZeroCopy": false, "WorkerThreads": 0, "IOThreads": 0, "EnableBackpressure": true, "BackpressureThreshold": 1048576, "BatchSize": 50}}, "Storage": {"Provider": "SQLite", "ConnectionString": "Data Source=C:\\MqttBroker\\data\\mqtt_broker.db;Cache=Shared;", "EnablePersistence": true, "Redis": {"ConnectionString": "localhost:6379", "Database": 0, "KeyPrefix": "mqtt:", "EnableCaching": false}}, "Security": {"AllowAnonymous": true, "RequireAuthentication": false, "EnableTls": false, "Authentication": {"Provider": "None", "TokenExpiration": "24:00:00", "RefreshTokenExpiration": "7.00:00:00"}, "Authorization": {"EnableTopicAuthorization": false, "DefaultPolicy": "Allow"}}, "Performance": {"MaxMessageSize": 1048576, "MaxTopicLength": 1024, "MaxClientIdLength": 128, "MessageRetentionDays": 7, "SessionExpirationHours": 24}, "Clustering": {"Enabled": false, "NodeId": "dev-node-1", "DiscoveryMethod": "None"}, "Metrics": {"EnableMetrics": true, "CollectionIntervalMs": 10000, "EnableDetailedMetrics": true, "Export": {"EnablePrometheusExport": false, "PrometheusPort": 9091, "PrometheusPath": "/metrics", "EnableFileExport": true, "FileExportPath": "C:\\MqttBroker\\logs\\metrics", "FileExportInterval": "00:05:00"}, "Alerts": {"EnableAlerts": false, "EvaluationIntervalSeconds": 60}}, "HealthChecks": {"Enabled": true, "Port": 9090, "Path": "/health", "DetailedPath": "/health/detailed", "Checks": ["Database", "Memory", "Disk"]}, "Development": {"EnableSwagger": true, "EnableDetailedErrors": true, "EnableSensitiveDataLogging": true, "MockData": {"EnableMockClients": false, "MockClientCount": 10, "MockMessageInterval": 5000}}}, "AllowedHosts": "*", "Kestrel": {"Endpoints": {"Http": {"Url": "http://localhost:9090"}}}}