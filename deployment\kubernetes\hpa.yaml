apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: mqtt-broker-hpa
  namespace: mqtt-system
  labels:
    app: mqtt-broker
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: mqtt-broker
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: mqtt_broker_connections_current
      target:
        type: AverageValue
        averageValue: "5000"
  - type: Pods
    pods:
      metric:
        name: mqtt_broker_messages_per_second
      target:
        type: AverageValue
        averageValue: "1000"
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
      - type: Pods
        value: 1
        periodSeconds: 60
      selectPolicy: Min
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Max

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: redis-hpa
  namespace: mqtt-system
  labels:
    app: redis
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: redis
  minReplicas: 2
  maxReplicas: 6
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 75
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 85
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 600
      policies:
      - type: Pods
        value: 1
        periodSeconds: 180
    scaleUp:
      stabilizationWindowSeconds: 120
      policies:
      - type: Pods
        value: 1
        periodSeconds: 60

---
# 垂直 Pod 自动扩缩容 (VPA)
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: mqtt-broker-vpa
  namespace: mqtt-system
  labels:
    app: mqtt-broker
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: mqtt-broker
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: mqtt-broker
      minAllowed:
        cpu: 500m
        memory: 1Gi
      maxAllowed:
        cpu: 4
        memory: 8Gi
      controlledResources: ["cpu", "memory"]
      controlledValues: RequestsAndLimits

---
# Pod 中断预算 (PDB)
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: mqtt-broker-pdb
  namespace: mqtt-system
  labels:
    app: mqtt-broker
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: mqtt-broker

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: postgres-pdb
  namespace: mqtt-system
  labels:
    app: postgres
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: postgres

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: redis-pdb
  namespace: mqtt-system
  labels:
    app: redis
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: redis

---
# 自定义指标适配器配置 (需要安装 Prometheus Adapter)
apiVersion: v1
kind: ConfigMap
metadata:
  name: adapter-config
  namespace: mqtt-system
data:
  config.yaml: |
    rules:
    - seriesQuery: 'mqtt_broker_connections_current{namespace!="",pod!=""}'
      resources:
        overrides:
          namespace: {resource: "namespace"}
          pod: {resource: "pod"}
      name:
        matches: "^mqtt_broker_connections_current"
        as: "mqtt_broker_connections_current"
      metricsQuery: 'mqtt_broker_connections_current{<<.LabelMatchers>>}'
    
    - seriesQuery: 'rate(mqtt_broker_messages_total{namespace!="",pod!=""}[2m])'
      resources:
        overrides:
          namespace: {resource: "namespace"}
          pod: {resource: "pod"}
      name:
        matches: "^rate.*"
        as: "mqtt_broker_messages_per_second"
      metricsQuery: 'rate(mqtt_broker_messages_total{<<.LabelMatchers>>}[2m])'

---
# KEDA ScaledObject (可选，需要安装 KEDA)
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: mqtt-broker-scaledobject
  namespace: mqtt-system
spec:
  scaleTargetRef:
    name: mqtt-broker
  minReplicaCount: 3
  maxReplicaCount: 20
  triggers:
  - type: prometheus
    metadata:
      serverAddress: http://prometheus:9090
      metricName: mqtt_broker_connections_current
      threshold: '5000'
      query: mqtt_broker_connections_current
  - type: prometheus
    metadata:
      serverAddress: http://prometheus:9090
      metricName: mqtt_broker_cpu_usage
      threshold: '70'
      query: rate(container_cpu_usage_seconds_total{pod=~"mqtt-broker-.*"}[2m]) * 100
  - type: prometheus
    metadata:
      serverAddress: http://prometheus:9090
      metricName: mqtt_broker_memory_usage
      threshold: '80'
      query: (container_memory_working_set_bytes{pod=~"mqtt-broker-.*"} / container_spec_memory_limit_bytes{pod=~"mqtt-broker-.*"}) * 100
