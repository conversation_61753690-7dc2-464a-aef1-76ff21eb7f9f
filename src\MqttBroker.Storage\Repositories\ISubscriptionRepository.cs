using MqttBroker.Core.Session;
using MqttBroker.Core.Topic;

namespace MqttBroker.Storage.Repositories;

/// <summary>
/// 订阅仓储接口
/// </summary>
public interface ISubscriptionRepository
{
    /// <summary>
    /// 保存客户端订阅
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="subscriptions">订阅列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<SessionOperationResult> SaveSubscriptionsAsync(string clientId, IEnumerable<ClientSubscription> subscriptions, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取客户端订阅
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅列表</returns>
    Task<IEnumerable<ClientSubscription>> GetSubscriptionsAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除客户端订阅
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="topicFilter">主题过滤器</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<SessionOperationResult> DeleteSubscriptionAsync(string clientId, string topicFilter, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除客户端所有订阅
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<SessionOperationResult> DeleteAllSubscriptionsAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取主题的所有订阅者
    /// </summary>
    /// <param name="topic">主题名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅者列表</returns>
    Task<IEnumerable<ClientSubscription>> GetSubscribersByTopicAsync(string topic, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新订阅匹配统计
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="topicFilter">主题过滤器</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<SessionOperationResult> UpdateMatchStatisticsAsync(string clientId, string topicFilter, CancellationToken cancellationToken = default);
}
