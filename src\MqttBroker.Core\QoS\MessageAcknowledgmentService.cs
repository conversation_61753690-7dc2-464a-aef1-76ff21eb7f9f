using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using System.Collections.Concurrent;

namespace MqttBroker.Core.QoS;

/// <summary>
/// 消息确认服务实现，负责管理 QoS 1 和 QoS 2 的消息确认流程
/// </summary>
public class MessageAcknowledgmentService : IMessageAcknowledgmentService, IDisposable
{
    private readonly ILogger<MessageAcknowledgmentService> _logger;
    private readonly MessageAcknowledgmentOptions _options;

    // 客户端待确认消息存储：ClientId -> MessageId -> PendingMessage
    private readonly ConcurrentDictionary<string, ConcurrentDictionary<ushort, PendingMessage>> _pendingMessages = new();
    
    // 统计信息
    private long _totalAcknowledgedMessages;
    private long _totalAcknowledgedQoS1Messages;
    private long _totalAcknowledgedQoS2Messages;

    private readonly Timer? _cleanupTimer;
    private bool _disposed;

    /// <summary>
    /// 初始化消息确认服务
    /// </summary>
    public MessageAcknowledgmentService(
        ILogger<MessageAcknowledgmentService> logger,
        IOptions<MessageAcknowledgmentOptions> options)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));

        // 启动清理定时器
        _cleanupTimer = new Timer(CleanupExpiredMessages, null,
            TimeSpan.FromMilliseconds(_options.StatisticsCleanupIntervalMs),
            TimeSpan.FromMilliseconds(_options.StatisticsCleanupIntervalMs));

        _logger.LogInformation("Message Acknowledgment Service initialized with options: MaxPendingPerClient={MaxPending}, TimeoutMs={Timeout}",
            _options.MaxPendingMessagesPerClient, _options.AcknowledgmentTimeoutMs);
    }

    /// <summary>
    /// 添加待确认的消息
    /// </summary>
    public async Task<bool> AddPendingMessageAsync(string clientId, ushort messageId, MqttPublishPacket publishPacket, MqttQoSLevel qosLevel)
    {
        if (string.IsNullOrEmpty(clientId))
            throw new ArgumentException("Client ID cannot be null or empty", nameof(clientId));

        if (qosLevel == MqttQoSLevel.AtMostOnce)
        {
            _logger.LogTrace("QoS 0 message does not require acknowledgment for client {ClientId}", clientId);
            return true;
        }

        try
        {
            var clientMessages = _pendingMessages.GetOrAdd(clientId, _ => new ConcurrentDictionary<ushort, PendingMessage>());

            // 检查客户端待确认消息数量限制
            if (clientMessages.Count >= _options.MaxPendingMessagesPerClient)
            {
                _logger.LogWarning("Client {ClientId} has reached maximum pending messages limit: {Limit}", 
                    clientId, _options.MaxPendingMessagesPerClient);
                return false;
            }

            var pendingMessage = new PendingMessage
            {
                ClientId = clientId,
                MessageId = messageId,
                PublishPacket = publishPacket,
                QoSLevel = qosLevel,
                QoS2State = qosLevel == MqttQoSLevel.ExactlyOnce ? QoS2MessageState.WaitingForPubRec : QoS2MessageState.Completed,
                CreatedAt = DateTime.UtcNow
            };

            var added = clientMessages.TryAdd(messageId, pendingMessage);
            
            if (added)
            {
                _logger.LogTrace("Added pending {QoS} message for client {ClientId}, MessageId: {MessageId}",
                    qosLevel, clientId, messageId);
            }
            else
            {
                _logger.LogWarning("Message with ID {MessageId} already exists for client {ClientId}", messageId, clientId);
            }

            return added;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding pending message for client {ClientId}, MessageId: {MessageId}", clientId, messageId);
            return false;
        }
    }

    /// <summary>
    /// 确认 QoS 1 消息（收到 PUBACK）
    /// </summary>
    public async Task<AcknowledgmentResult> AcknowledgeQoS1MessageAsync(string clientId, ushort messageId)
    {
        if (string.IsNullOrEmpty(clientId))
            return AcknowledgmentResult.Failure(clientId, messageId, "Client ID cannot be null or empty");

        try
        {
            if (!_pendingMessages.TryGetValue(clientId, out var clientMessages))
            {
                _logger.LogTrace("No pending messages found for client {ClientId}", clientId);
                return AcknowledgmentResult.Failure(clientId, messageId, "No pending messages for client");
            }

            if (!clientMessages.TryRemove(messageId, out var pendingMessage))
            {
                _logger.LogTrace("Message {MessageId} not found in pending messages for client {ClientId}", messageId, clientId);
                return AcknowledgmentResult.Failure(clientId, messageId, "Message not found in pending messages");
            }

            if (pendingMessage.QoSLevel != MqttQoSLevel.AtLeastOnce)
            {
                _logger.LogWarning("Attempted to acknowledge non-QoS1 message {MessageId} for client {ClientId}, QoS: {QoS}",
                    messageId, clientId, pendingMessage.QoSLevel);
                return AcknowledgmentResult.Failure(clientId, messageId, $"Message is not QoS 1, actual QoS: {pendingMessage.QoSLevel}");
            }

            // 更新统计信息
            Interlocked.Increment(ref _totalAcknowledgedMessages);
            Interlocked.Increment(ref _totalAcknowledgedQoS1Messages);

            _logger.LogTrace("QoS 1 message {MessageId} acknowledged for client {ClientId}, Age: {Age}ms",
                messageId, clientId, pendingMessage.Age.TotalMilliseconds);

            return AcknowledgmentResult.Success(clientId, messageId, pendingMessage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error acknowledging QoS 1 message {MessageId} for client {ClientId}", messageId, clientId);
            return AcknowledgmentResult.Failure(clientId, messageId, $"Internal error: {ex.Message}");
        }
    }

    /// <summary>
    /// 确认 QoS 2 消息第一阶段（收到 PUBREC）
    /// </summary>
    public async Task<AcknowledgmentResult> AcknowledgeQoS2Phase1Async(string clientId, ushort messageId)
    {
        if (string.IsNullOrEmpty(clientId))
            return AcknowledgmentResult.Failure(clientId, messageId, "Client ID cannot be null or empty");

        try
        {
            if (!_pendingMessages.TryGetValue(clientId, out var clientMessages))
            {
                _logger.LogTrace("No pending messages found for client {ClientId}", clientId);
                return AcknowledgmentResult.Failure(clientId, messageId, "No pending messages for client");
            }

            if (!clientMessages.TryGetValue(messageId, out var pendingMessage))
            {
                _logger.LogTrace("Message {MessageId} not found in pending messages for client {ClientId}", messageId, clientId);
                return AcknowledgmentResult.Failure(clientId, messageId, "Message not found in pending messages");
            }

            if (pendingMessage.QoSLevel != MqttQoSLevel.ExactlyOnce)
            {
                _logger.LogWarning("Attempted to acknowledge non-QoS2 message {MessageId} for client {ClientId}, QoS: {QoS}",
                    messageId, clientId, pendingMessage.QoSLevel);
                return AcknowledgmentResult.Failure(clientId, messageId, $"Message is not QoS 2, actual QoS: {pendingMessage.QoSLevel}");
            }

            if (pendingMessage.QoS2State != QoS2MessageState.WaitingForPubRec)
            {
                _logger.LogWarning("QoS 2 message {MessageId} for client {ClientId} is not in WaitingForPubRec state, current state: {State}",
                    messageId, clientId, pendingMessage.QoS2State);
                return AcknowledgmentResult.Failure(clientId, messageId, $"Message is not in WaitingForPubRec state, current state: {pendingMessage.QoS2State}");
            }

            // 更新状态为等待 PUBCOMP
            pendingMessage.QoS2State = QoS2MessageState.WaitingForPubComp;

            _logger.LogTrace("QoS 2 message {MessageId} phase 1 acknowledged for client {ClientId}, moving to WaitingForPubComp state",
                messageId, clientId);

            return AcknowledgmentResult.Success(clientId, messageId, pendingMessage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error acknowledging QoS 2 phase 1 message {MessageId} for client {ClientId}", messageId, clientId);
            return AcknowledgmentResult.Failure(clientId, messageId, $"Internal error: {ex.Message}");
        }
    }

    /// <summary>
    /// 确认 QoS 2 消息第二阶段（收到 PUBCOMP）
    /// </summary>
    public async Task<AcknowledgmentResult> AcknowledgeQoS2Phase2Async(string clientId, ushort messageId)
    {
        if (string.IsNullOrEmpty(clientId))
            return AcknowledgmentResult.Failure(clientId, messageId, "Client ID cannot be null or empty");

        try
        {
            if (!_pendingMessages.TryGetValue(clientId, out var clientMessages))
            {
                _logger.LogTrace("No pending messages found for client {ClientId}", clientId);
                return AcknowledgmentResult.Failure(clientId, messageId, "No pending messages for client");
            }

            if (!clientMessages.TryRemove(messageId, out var pendingMessage))
            {
                _logger.LogTrace("Message {MessageId} not found in pending messages for client {ClientId}", messageId, clientId);
                return AcknowledgmentResult.Failure(clientId, messageId, "Message not found in pending messages");
            }

            if (pendingMessage.QoSLevel != MqttQoSLevel.ExactlyOnce)
            {
                _logger.LogWarning("Attempted to acknowledge non-QoS2 message {MessageId} for client {ClientId}, QoS: {QoS}",
                    messageId, clientId, pendingMessage.QoSLevel);
                return AcknowledgmentResult.Failure(clientId, messageId, $"Message is not QoS 2, actual QoS: {pendingMessage.QoSLevel}");
            }

            if (pendingMessage.QoS2State != QoS2MessageState.WaitingForPubComp)
            {
                _logger.LogWarning("QoS 2 message {MessageId} for client {ClientId} is not in WaitingForPubComp state, current state: {State}",
                    messageId, clientId, pendingMessage.QoS2State);
                return AcknowledgmentResult.Failure(clientId, messageId, $"Message is not in WaitingForPubComp state, current state: {pendingMessage.QoS2State}");
            }

            // 更新统计信息
            Interlocked.Increment(ref _totalAcknowledgedMessages);
            Interlocked.Increment(ref _totalAcknowledgedQoS2Messages);

            _logger.LogTrace("QoS 2 message {MessageId} fully acknowledged for client {ClientId}, Age: {Age}ms",
                messageId, clientId, pendingMessage.Age.TotalMilliseconds);

            return AcknowledgmentResult.Success(clientId, messageId, pendingMessage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error acknowledging QoS 2 phase 2 message {MessageId} for client {ClientId}", messageId, clientId);
            return AcknowledgmentResult.Failure(clientId, messageId, $"Internal error: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取待确认的消息
    /// </summary>
    public Task<PendingMessage?> GetPendingMessageAsync(string clientId, ushort messageId)
    {
        if (string.IsNullOrEmpty(clientId))
            return null;

        try
        {
            if (_pendingMessages.TryGetValue(clientId, out var clientMessages) &&
                clientMessages.TryGetValue(messageId, out var pendingMessage))
            {
                return Task.FromResult<PendingMessage?>(pendingMessage);
            }

            return Task.FromResult<PendingMessage?>(null);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pending message {MessageId} for client {ClientId}", messageId, clientId);
            return Task.FromResult<PendingMessage?>(null);
        }
    }

    /// <summary>
    /// 获取客户端的所有待确认消息
    /// </summary>
    public async Task<IList<PendingMessage>> GetClientPendingMessagesAsync(string clientId)
    {
        if (string.IsNullOrEmpty(clientId))
            return new List<PendingMessage>();

        try
        {
            if (_pendingMessages.TryGetValue(clientId, out var clientMessages))
            {
                return clientMessages.Values.ToList();
            }

            return new List<PendingMessage>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pending messages for client {ClientId}", clientId);
            return new List<PendingMessage>();
        }
    }

    /// <summary>
    /// 获取超时的待确认消息
    /// </summary>
    public async Task<IList<PendingMessage>> GetTimedOutMessagesAsync(TimeSpan timeoutThreshold)
    {
        var timedOutMessages = new List<PendingMessage>();

        try
        {
            foreach (var clientMessages in _pendingMessages.Values)
            {
                foreach (var pendingMessage in clientMessages.Values)
                {
                    if (pendingMessage.IsTimedOut(timeoutThreshold))
                    {
                        timedOutMessages.Add(pendingMessage);
                    }
                }
            }

            _logger.LogTrace("Found {Count} timed out messages with threshold {Threshold}ms",
                timedOutMessages.Count, timeoutThreshold.TotalMilliseconds);

            return timedOutMessages;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting timed out messages");
            return new List<PendingMessage>();
        }
    }

    /// <summary>
    /// 移除待确认消息
    /// </summary>
    public async Task<bool> RemovePendingMessageAsync(string clientId, ushort messageId)
    {
        if (string.IsNullOrEmpty(clientId))
            return false;

        try
        {
            if (_pendingMessages.TryGetValue(clientId, out var clientMessages))
            {
                var removed = clientMessages.TryRemove(messageId, out var pendingMessage);

                if (removed)
                {
                    _logger.LogTrace("Removed pending message {MessageId} for client {ClientId}", messageId, clientId);
                }

                return removed;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing pending message {MessageId} for client {ClientId}", messageId, clientId);
            return false;
        }
    }

    /// <summary>
    /// 清理客户端的所有待确认消息
    /// </summary>
    public async Task<int> ClearClientPendingMessagesAsync(string clientId)
    {
        if (string.IsNullOrEmpty(clientId))
            return 0;

        try
        {
            if (_pendingMessages.TryRemove(clientId, out var clientMessages))
            {
                var count = clientMessages.Count;
                _logger.LogInformation("Cleared {Count} pending messages for client {ClientId}", count, clientId);
                return count;
            }

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing pending messages for client {ClientId}", clientId);
            return 0;
        }
    }

    /// <summary>
    /// 获取确认统计信息
    /// </summary>
    public async Task<AcknowledgmentStatistics> GetStatisticsAsync()
    {
        try
        {
            var statistics = new AcknowledgmentStatistics
            {
                TotalAcknowledgedMessages = Interlocked.Read(ref _totalAcknowledgedMessages),
                TotalAcknowledgedQoS1Messages = Interlocked.Read(ref _totalAcknowledgedQoS1Messages),
                TotalAcknowledgedQoS2Messages = Interlocked.Read(ref _totalAcknowledgedQoS2Messages),
                ClientsWithPendingMessages = _pendingMessages.Count
            };

            var allPendingMessages = new List<PendingMessage>();
            foreach (var clientMessages in _pendingMessages.Values)
            {
                allPendingMessages.AddRange(clientMessages.Values);
            }

            statistics.TotalPendingMessages = allPendingMessages.Count;
            statistics.PendingQoS1Messages = allPendingMessages.Count(m => m.QoSLevel == MqttQoSLevel.AtLeastOnce);
            statistics.PendingQoS2Messages = allPendingMessages.Count(m => m.QoSLevel == MqttQoSLevel.ExactlyOnce);
            statistics.QoS2WaitingForPubRec = allPendingMessages.Count(m => m.QoS2State == QoS2MessageState.WaitingForPubRec);
            statistics.QoS2WaitingForPubComp = allPendingMessages.Count(m => m.QoS2State == QoS2MessageState.WaitingForPubComp);

            if (allPendingMessages.Any())
            {
                statistics.AverageMessageAge = allPendingMessages.Average(m => m.Age.TotalMilliseconds);
                statistics.OldestMessageAge = allPendingMessages.Max(m => m.Age.TotalMilliseconds);
            }

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting acknowledgment statistics");
            return new AcknowledgmentStatistics();
        }
    }

    /// <summary>
    /// 清理过期消息
    /// </summary>
    private async void CleanupExpiredMessages(object? state)
    {
        try
        {
            var timeout = TimeSpan.FromMilliseconds(_options.AcknowledgmentTimeoutMs);
            var expiredMessages = await GetTimedOutMessagesAsync(timeout);

            var cleanedCount = 0;
            foreach (var expiredMessage in expiredMessages)
            {
                if (await RemovePendingMessageAsync(expiredMessage.ClientId, expiredMessage.MessageId))
                {
                    cleanedCount++;
                    _logger.LogWarning("Removed expired pending message {MessageId} for client {ClientId}, Age: {Age}ms",
                        expiredMessage.MessageId, expiredMessage.ClientId, expiredMessage.Age.TotalMilliseconds);
                }
            }

            if (cleanedCount > 0)
            {
                _logger.LogInformation("Cleaned up {Count} expired pending messages", cleanedCount);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during expired message cleanup");
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            _cleanupTimer?.Dispose();
            _pendingMessages.Clear();

            _disposed = true;
            _logger.LogInformation("Message Acknowledgment Service disposed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing Message Acknowledgment Service");
        }
    }
}
