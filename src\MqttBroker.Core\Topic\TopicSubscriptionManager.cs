using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MqttBroker.Core.Client;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;

namespace MqttBroker.Core.Topic;

/// <summary>
/// 主题订阅管理器实现
/// </summary>
public class TopicSubscriptionManager : ITopicSubscriptionManager, IDisposable
{
    private readonly TopicTree _topicTree;
    private readonly IMqttTopicMatcher _topicMatcher;
    private readonly ILogger<TopicSubscriptionManager> _logger;
    private readonly TopicSubscriptionOptions _options;
    private readonly ConcurrentDictionary<string, ConcurrentDictionary<string, ClientSubscription>> _clientSubscriptions;
    private readonly object _lock = new();
    private bool _disposed;

    /// <summary>
    /// 订阅者添加事件
    /// </summary>
    public event EventHandler<SubscriberAddedEventArgs>? SubscriberAdded;

    /// <summary>
    /// 订阅者移除事件
    /// </summary>
    public event EventHandler<SubscriberRemovedEventArgs>? SubscriberRemoved;

    /// <summary>
    /// 初始化主题订阅管理器
    /// </summary>
    /// <param name="topicMatcher">主题匹配器</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="options">配置选项</param>
    public TopicSubscriptionManager(
        IMqttTopicMatcher topicMatcher,
        ILogger<TopicSubscriptionManager> logger,
        IOptions<TopicSubscriptionOptions> options)
    {
        _topicMatcher = topicMatcher ?? throw new ArgumentNullException(nameof(topicMatcher));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? new TopicSubscriptionOptions();
        
        _topicTree = new TopicTree();
        _clientSubscriptions = new ConcurrentDictionary<string, ConcurrentDictionary<string, ClientSubscription>>();
    }

    /// <summary>
    /// 订阅主题
    /// </summary>
    /// <param name="client">客户端</param>
    /// <param name="subscription">订阅信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅结果</returns>
    public async Task<SubscriptionResult> SubscribeAsync(IMqttClient client, MqttSubscription subscription, CancellationToken cancellationToken = default)
    {
        if (client == null)
            throw new ArgumentNullException(nameof(client));
        if (subscription == null)
            throw new ArgumentNullException(nameof(subscription));

        try
        {
            // 验证主题过滤器
            if (!_topicMatcher.IsValidTopicFilter(subscription.TopicFilter))
            {
                _logger.LogWarning("Invalid topic filter: {TopicFilter} for client: {ClientId}", 
                    subscription.TopicFilter, client.ClientId);
                return SubscriptionResult.Failure(subscription.TopicFilter, MqttReasonCode.TopicFilterInvalid, "Invalid topic filter");
            }

            // 检查订阅限制
            if (!await CheckSubscriptionLimitsAsync(client.ClientId, cancellationToken))
            {
                _logger.LogWarning("Subscription limit exceeded for client: {ClientId}", client.ClientId);
                return SubscriptionResult.Failure(subscription.TopicFilter, MqttReasonCode.QuotaExceeded, "Subscription limit exceeded");
            }

            // 确定授权的QoS级别
            var grantedQoSLevel = DetermineGrantedQoSLevel(subscription.QoSLevel);

            // 创建订阅者
            var subscriber = new TopicSubscriber
            {
                ClientId = client.ClientId,
                TopicFilter = subscription.TopicFilter,
                QoSLevel = grantedQoSLevel,
                Options = subscription.Options,
                SubscribedAt = DateTime.UtcNow,
                Client = client
            };

            // 添加到主题树
            _topicTree.AddSubscriber(subscription.TopicFilter, subscriber);

            // 添加到客户端订阅索引
            var clientSubscriptions = _clientSubscriptions.GetOrAdd(client.ClientId, 
                _ => new ConcurrentDictionary<string, ClientSubscription>());
            
            var clientSubscription = new ClientSubscription
            {
                ClientId = client.ClientId,
                TopicFilter = subscription.TopicFilter,
                QoSLevel = grantedQoSLevel,
                Options = subscription.Options,
                SubscribedAt = DateTime.UtcNow
            };

            clientSubscriptions.AddOrUpdate(subscription.TopicFilter, clientSubscription, (key, existing) => clientSubscription);

            // 触发事件
            SubscriberAdded?.Invoke(this, new SubscriberAddedEventArgs(subscriber));

            _logger.LogDebug("Client {ClientId} subscribed to {TopicFilter} with QoS {QoSLevel}", 
                client.ClientId, subscription.TopicFilter, grantedQoSLevel);

            return SubscriptionResult.Success(subscription.TopicFilter, grantedQoSLevel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error subscribing client {ClientId} to {TopicFilter}", 
                client.ClientId, subscription.TopicFilter);
            return SubscriptionResult.Failure(subscription.TopicFilter, MqttReasonCode.UnspecifiedError, ex.Message);
        }
    }

    /// <summary>
    /// 批量订阅主题
    /// </summary>
    /// <param name="client">客户端</param>
    /// <param name="subscriptions">订阅信息列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅结果列表</returns>
    public async Task<IList<SubscriptionResult>> SubscribeAsync(IMqttClient client, IList<MqttSubscription> subscriptions, CancellationToken cancellationToken = default)
    {
        if (client == null)
            throw new ArgumentNullException(nameof(client));
        if (subscriptions == null)
            throw new ArgumentNullException(nameof(subscriptions));

        var results = new List<SubscriptionResult>();

        foreach (var subscription in subscriptions)
        {
            var result = await SubscribeAsync(client, subscription, cancellationToken);
            results.Add(result);
        }

        return results;
    }

    /// <summary>
    /// 取消订阅主题
    /// </summary>
    /// <param name="client">客户端</param>
    /// <param name="topicFilter">主题过滤器</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>取消订阅结果</returns>
    public async Task<UnsubscriptionResult> UnsubscribeAsync(IMqttClient client, string topicFilter, CancellationToken cancellationToken = default)
    {
        if (client == null)
            throw new ArgumentNullException(nameof(client));
        if (string.IsNullOrEmpty(topicFilter))
            throw new ArgumentException("Topic filter cannot be null or empty", nameof(topicFilter));

        try
        {
            // 从主题树移除订阅者
            var removed = _topicTree.RemoveSubscriber(topicFilter, client.ClientId);

            // 从客户端订阅索引移除
            if (_clientSubscriptions.TryGetValue(client.ClientId, out var clientSubscriptions))
            {
                clientSubscriptions.TryRemove(topicFilter, out _);
                
                // 如果客户端没有任何订阅，移除客户端条目
                if (clientSubscriptions.IsEmpty)
                {
                    _clientSubscriptions.TryRemove(client.ClientId, out _);
                }
            }

            if (removed)
            {
                // 触发事件
                SubscriberRemoved?.Invoke(this, new SubscriberRemovedEventArgs(client.ClientId, topicFilter));

                _logger.LogDebug("Client {ClientId} unsubscribed from {TopicFilter}", client.ClientId, topicFilter);
                return UnsubscriptionResult.Success(topicFilter);
            }
            else
            {
                _logger.LogWarning("Client {ClientId} attempted to unsubscribe from non-existent subscription: {TopicFilter}", 
                    client.ClientId, topicFilter);
                return UnsubscriptionResult.Failure(topicFilter, MqttReasonCode.NoSubscriptionExisted, "No subscription existed");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unsubscribing client {ClientId} from {TopicFilter}", client.ClientId, topicFilter);
            return UnsubscriptionResult.Failure(topicFilter, MqttReasonCode.UnspecifiedError, ex.Message);
        }
    }

    /// <summary>
    /// 批量取消订阅主题
    /// </summary>
    /// <param name="client">客户端</param>
    /// <param name="topicFilters">主题过滤器列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>取消订阅结果列表</returns>
    public async Task<IList<UnsubscriptionResult>> UnsubscribeAsync(IMqttClient client, IList<string> topicFilters, CancellationToken cancellationToken = default)
    {
        if (client == null)
            throw new ArgumentNullException(nameof(client));
        if (topicFilters == null)
            throw new ArgumentNullException(nameof(topicFilters));

        var results = new List<UnsubscriptionResult>();

        foreach (var topicFilter in topicFilters)
        {
            var result = await UnsubscribeAsync(client, topicFilter, cancellationToken);
            results.Add(result);
        }

        return results;
    }

    /// <summary>
    /// 获取匹配指定主题的所有订阅者
    /// </summary>
    /// <param name="topicName">主题名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>匹配的订阅者列表</returns>
    public Task<IList<TopicSubscriber>> GetSubscribersAsync(string topicName, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(topicName))
            return Task.FromResult<IList<TopicSubscriber>>(new List<TopicSubscriber>());

        try
        {
            var subscribers = _topicTree.GetMatchingSubscribers(topicName);
            
            // 过滤掉已断开连接的客户端
            var activeSubscribers = subscribers.Where(s => s.Client?.State == MqttClientState.Authenticated).ToList();
            
            return Task.FromResult<IList<TopicSubscriber>>(activeSubscribers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscribers for topic: {TopicName}", topicName);
            return Task.FromResult<IList<TopicSubscriber>>(new List<TopicSubscriber>());
        }
    }

    /// <summary>
    /// 获取指定客户端对指定主题的订阅者信息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="topicName">主题名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅者信息，如果不存在则返回null</returns>
    public Task<TopicSubscriber?> GetSubscriberAsync(string clientId, string topicName, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(clientId) || string.IsNullOrEmpty(topicName))
            return Task.FromResult<TopicSubscriber?>(null);

        try
        {
            // 获取匹配主题的所有订阅者
            var subscribers = _topicTree.GetMatchingSubscribers(topicName);

            // 查找指定客户端的订阅者
            var subscriber = subscribers.FirstOrDefault(s => s.ClientId == clientId && s.Client?.State == MqttClientState.Authenticated);

            return Task.FromResult(subscriber);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscriber for client: {ClientId}, topic: {TopicName}", clientId, topicName);
            return Task.FromResult<TopicSubscriber?>(null);
        }
    }

    /// <summary>
    /// 获取客户端的所有订阅
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>客户端订阅列表</returns>
    public Task<IList<ClientSubscription>> GetClientSubscriptionsAsync(string clientId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(clientId))
            return Task.FromResult<IList<ClientSubscription>>(new List<ClientSubscription>());

        try
        {
            if (_clientSubscriptions.TryGetValue(clientId, out var clientSubscriptions))
            {
                return Task.FromResult<IList<ClientSubscription>>(clientSubscriptions.Values.ToList());
            }

            return Task.FromResult<IList<ClientSubscription>>(new List<ClientSubscription>());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscriptions for client: {ClientId}", clientId);
            return Task.FromResult<IList<ClientSubscription>>(new List<ClientSubscription>());
        }
    }

    /// <summary>
    /// 清理客户端的所有订阅
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理任务</returns>
    public Task CleanupClientSubscriptionsAsync(string clientId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(clientId))
            return Task.CompletedTask;

        try
        {
            // 从主题树移除客户端的所有订阅
            var removedCount = _topicTree.RemoveClientSubscriptions(clientId);

            // 从客户端订阅索引移除
            _clientSubscriptions.TryRemove(clientId, out _);

            if (removedCount > 0)
            {
                _logger.LogDebug("Cleaned up {Count} subscriptions for client: {ClientId}", removedCount, clientId);
            }

            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up subscriptions for client: {ClientId}", clientId);
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 获取订阅统计信息
    /// </summary>
    /// <returns>订阅统计信息</returns>
    public Task<SubscriptionStatistics> GetStatisticsAsync()
    {
        try
        {
            var treeStats = _topicTree.GetStatistics();
            var activeClients = _clientSubscriptions.Count;
            var totalSubscriptions = _clientSubscriptions.Values.Sum(cs => cs.Count);
            
            var wildcardSubscriptions = _clientSubscriptions.Values
                .SelectMany(cs => cs.Values)
                .Count(s => s.TopicFilter.Contains('+') || s.TopicFilter.Contains('#'));
            
            var systemTopicSubscriptions = _clientSubscriptions.Values
                .SelectMany(cs => cs.Values)
                .Count(s => s.TopicFilter.StartsWith(MqttProtocolConstants.SystemTopics.Prefix));

            var statistics = new SubscriptionStatistics
            {
                TotalSubscriptions = totalSubscriptions,
                ActiveClients = activeClients,
                TopicCount = treeStats.NodesWithSubscribers,
                WildcardSubscriptions = wildcardSubscriptions,
                SystemTopicSubscriptions = systemTopicSubscriptions
            };

            return Task.FromResult(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscription statistics");
            return Task.FromResult(new SubscriptionStatistics());
        }
    }

    #region 私有方法

    /// <summary>
    /// 检查订阅限制
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否允许订阅</returns>
    private async Task<bool> CheckSubscriptionLimitsAsync(string clientId, CancellationToken cancellationToken)
    {
        if (_options.MaxSubscriptionsPerClient <= 0)
            return true;

        var clientSubscriptions = await GetClientSubscriptionsAsync(clientId, cancellationToken);
        return clientSubscriptions.Count < _options.MaxSubscriptionsPerClient;
    }

    /// <summary>
    /// 确定授权的QoS级别
    /// </summary>
    /// <param name="requestedQoSLevel">请求的QoS级别</param>
    /// <returns>授权的QoS级别</returns>
    private MqttQoSLevel DetermineGrantedQoSLevel(MqttQoSLevel requestedQoSLevel)
    {
        // 根据服务器配置限制最大QoS级别
        var maxQoSLevel = _options.MaxQoSLevel;
        return requestedQoSLevel > maxQoSLevel ? maxQoSLevel : requestedQoSLevel;
    }

    #endregion

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _clientSubscriptions.Clear();
            _disposed = true;
        }
    }
}

/// <summary>
/// 主题订阅配置选项
/// </summary>
public class TopicSubscriptionOptions
{
    /// <summary>
    /// 每个客户端的最大订阅数（0表示无限制）
    /// </summary>
    public int MaxSubscriptionsPerClient { get; set; } = 1000;

    /// <summary>
    /// 服务器支持的最大QoS级别
    /// </summary>
    public MqttQoSLevel MaxQoSLevel { get; set; } = MqttQoSLevel.ExactlyOnce;

    /// <summary>
    /// 是否允许系统主题订阅
    /// </summary>
    public bool AllowSystemTopicSubscriptions { get; set; } = true;

    /// <summary>
    /// 是否允许通配符订阅
    /// </summary>
    public bool AllowWildcardSubscriptions { get; set; } = true;
}
