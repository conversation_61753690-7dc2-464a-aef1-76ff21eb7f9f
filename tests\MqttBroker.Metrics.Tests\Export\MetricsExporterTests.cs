using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using MqttBroker.Metrics.Configuration;
using MqttBroker.Metrics.Export;
using MqttBroker.Metrics.Models;
using System.Text.Json;
using Xunit;

namespace MqttBroker.Metrics.Tests.Export;

/// <summary>
/// JSON 指标导出器测试
/// </summary>
public class JsonMetricsExporterTests
{
    private readonly Mock<ILogger<JsonMetricsExporter>> _mockLogger;
    private readonly JsonMetricsExporter _exporter;

    public JsonMetricsExporterTests()
    {
        _mockLogger = new Mock<ILogger<JsonMetricsExporter>>();
        _exporter = new JsonMetricsExporter(_mockLogger.Object);
    }

    [Fact]
    public void Format_ShouldReturnJson()
    {
        // Act
        var format = _exporter.Format;

        // Assert
        Assert.Equal("json", format);
    }

    [Fact]
    public async Task ExportCurrentMetricsAsync_ShouldReturnValidJson()
    {
        // Arrange
        var metrics = new PerformanceMetrics
        {
            Timestamp = new DateTime(2024, 1, 1, 12, 0, 0, DateTimeKind.Utc),
            Connection = new ConnectionMetrics { ActiveConnections = 10 },
            Message = new MessageMetrics { MessagesSent = 100 }
        };

        // Act
        var result = await _exporter.ExportCurrentMetricsAsync(metrics);

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result);
        
        // 验证是否为有效的 JSON
        var deserializedMetrics = JsonSerializer.Deserialize<PerformanceMetrics>(result, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });
        
        Assert.NotNull(deserializedMetrics);
        Assert.Equal(10, deserializedMetrics.Connection.ActiveConnections);
        Assert.Equal(100, deserializedMetrics.Message.MessagesSent);
    }

    [Fact]
    public async Task ExportHistoricalMetricsAsync_ShouldReturnValidJsonWithMetadata()
    {
        // Arrange
        var metricsList = new List<PerformanceMetrics>
        {
            new() { Timestamp = DateTime.UtcNow.AddMinutes(-2), Connection = new ConnectionMetrics { ActiveConnections = 5 } },
            new() { Timestamp = DateTime.UtcNow.AddMinutes(-1), Connection = new ConnectionMetrics { ActiveConnections = 10 } }
        };

        // Act
        var result = await _exporter.ExportHistoricalMetricsAsync(metricsList);

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result);
        
        // 验证包含元数据
        Assert.Contains("exportedAt", result);
        Assert.Contains("totalRecords", result);
        Assert.Contains("timeRange", result);
        Assert.Contains("metrics", result);
        
        // 验证记录数 (考虑 JSON 格式化)
        Assert.Contains("totalRecords", result);
        Assert.Contains("2", result);
    }

    [Fact]
    public async Task ExportCurrentMetricsAsync_WithNullMetrics_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _exporter.ExportCurrentMetricsAsync(null!));
    }
}

/// <summary>
/// CSV 指标导出器测试
/// </summary>
public class CsvMetricsExporterTests
{
    private readonly Mock<ILogger<CsvMetricsExporter>> _mockLogger;
    private readonly CsvMetricsExporter _exporter;

    public CsvMetricsExporterTests()
    {
        _mockLogger = new Mock<ILogger<CsvMetricsExporter>>();
        _exporter = new CsvMetricsExporter(_mockLogger.Object);
    }

    [Fact]
    public void Format_ShouldReturnCsv()
    {
        // Act
        var format = _exporter.Format;

        // Assert
        Assert.Equal("csv", format);
    }

    [Fact]
    public async Task ExportCurrentMetricsAsync_ShouldReturnValidCsv()
    {
        // Arrange
        var metrics = new PerformanceMetrics
        {
            Timestamp = new DateTime(2024, 1, 1, 12, 0, 0, DateTimeKind.Utc),
            Connection = new ConnectionMetrics { ActiveConnections = 10, PeakConnections = 15, TotalConnections = 100 },
            Message = new MessageMetrics { MessagesSent = 200, MessagesReceived = 180, MessageSendRate = 5.5, MessageReceiveRate = 4.8 },
            Subscription = new SubscriptionMetrics { TotalSubscriptions = 50, ActiveTopics = 25 },
            QoS = new QoSMetrics { QoS0Messages = 100, QoS1Messages = 80, QoS2Messages = 20 },
            System = new SystemMetrics { CpuUsage = 25.5, Memory = new MemoryMetrics { TotalMemory = 1024000 } },
            Network = new NetworkMetrics { BytesSent = 2048, BytesReceived = 1536 }
        };

        // Act
        var result = await _exporter.ExportCurrentMetricsAsync(metrics);

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result);
        
        var lines = result.Split(Environment.NewLine, StringSplitOptions.RemoveEmptyEntries);
        Assert.Equal(2, lines.Length); // 头部 + 1行数据
        
        // 验证头部
        Assert.Contains("Timestamp", lines[0]);
        Assert.Contains("ActiveConnections", lines[0]);
        Assert.Contains("MessagesSent", lines[0]);
        
        // 验证数据行
        Assert.Contains("2024-01-01 12:00:00", lines[1]);
        Assert.Contains("10", lines[1]);
        Assert.Contains("200", lines[1]);
    }

    [Fact]
    public async Task ExportHistoricalMetricsAsync_ShouldReturnMultipleRows()
    {
        // Arrange
        var metricsList = new List<PerformanceMetrics>
        {
            new() 
            { 
                Timestamp = new DateTime(2024, 1, 1, 12, 0, 0, DateTimeKind.Utc),
                Connection = new ConnectionMetrics { ActiveConnections = 5 },
                Message = new MessageMetrics { MessagesSent = 100 },
                Subscription = new SubscriptionMetrics(),
                QoS = new QoSMetrics(),
                System = new SystemMetrics { Memory = new MemoryMetrics() },
                Network = new NetworkMetrics()
            },
            new() 
            { 
                Timestamp = new DateTime(2024, 1, 1, 12, 1, 0, DateTimeKind.Utc),
                Connection = new ConnectionMetrics { ActiveConnections = 10 },
                Message = new MessageMetrics { MessagesSent = 200 },
                Subscription = new SubscriptionMetrics(),
                QoS = new QoSMetrics(),
                System = new SystemMetrics { Memory = new MemoryMetrics() },
                Network = new NetworkMetrics()
            }
        };

        // Act
        var result = await _exporter.ExportHistoricalMetricsAsync(metricsList);

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result);
        
        var lines = result.Split(Environment.NewLine, StringSplitOptions.RemoveEmptyEntries);
        Assert.Equal(3, lines.Length); // 头部 + 2行数据
        
        Assert.Contains("5", lines[1]); // 第一行数据
        Assert.Contains("10", lines[2]); // 第二行数据
    }

    [Fact]
    public async Task ExportCurrentMetricsAsync_WithNullMetrics_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _exporter.ExportCurrentMetricsAsync(null!));
    }
}

/// <summary>
/// 指标导出管理器测试
/// </summary>
public class MetricsExportManagerTests
{
    private readonly Mock<ILogger<MetricsExportManager>> _mockLogger;
    private readonly IOptions<MetricsExportOptions> _options;

    public MetricsExportManagerTests()
    {
        _mockLogger = new Mock<ILogger<MetricsExportManager>>();
        _options = Options.Create(new MetricsExportOptions
        {
            EnableRestApi = true,
            EnableFileExport = true
        });
    }

    [Fact]
    public void RegisterExporter_ShouldAddExporter()
    {
        // Arrange
        var manager = new MetricsExportManager(_mockLogger.Object, _options);
        var mockExporter = new Mock<IMetricsExporter>();
        mockExporter.Setup(x => x.Format).Returns("test");

        // Act
        manager.RegisterExporter(mockExporter.Object);

        // Assert
        var formats = manager.GetSupportedFormats();
        Assert.Contains("test", formats);
    }

    [Fact]
    public async Task ExportCurrentMetricsAsync_WithRegisteredExporter_ShouldReturnData()
    {
        // Arrange
        var manager = new MetricsExportManager(_mockLogger.Object, _options);
        var mockExporter = new Mock<IMetricsExporter>();
        mockExporter.Setup(x => x.Format).Returns("test");
        mockExporter.Setup(x => x.ExportCurrentMetricsAsync(It.IsAny<PerformanceMetrics>(), It.IsAny<CancellationToken>()))
                   .ReturnsAsync("test data");

        manager.RegisterExporter(mockExporter.Object);

        var metrics = new PerformanceMetrics();

        // Act
        var result = await manager.ExportCurrentMetricsAsync(metrics, "test");

        // Assert
        Assert.Equal("test data", result);
        mockExporter.Verify(x => x.ExportCurrentMetricsAsync(metrics, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ExportCurrentMetricsAsync_WithUnsupportedFormat_ShouldThrowNotSupportedException()
    {
        // Arrange
        var manager = new MetricsExportManager(_mockLogger.Object, _options);
        var metrics = new PerformanceMetrics();

        // Act & Assert
        await Assert.ThrowsAsync<NotSupportedException>(() => manager.ExportCurrentMetricsAsync(metrics, "unsupported"));
    }

    [Fact]
    public async Task ExportToFileAsync_ShouldCreateFileWithData()
    {
        // Arrange
        var manager = new MetricsExportManager(_mockLogger.Object, _options);
        var tempFile = Path.GetTempFileName();
        var testData = "test data content";

        try
        {
            // Act
            await manager.ExportToFileAsync(testData, tempFile);

            // Assert
            Assert.True(File.Exists(tempFile));
            var fileContent = await File.ReadAllTextAsync(tempFile);
            Assert.Equal(testData, fileContent);
        }
        finally
        {
            // Cleanup
            if (File.Exists(tempFile))
            {
                File.Delete(tempFile);
            }
        }
    }

    [Fact]
    public void GetSupportedFormats_WithNoExporters_ShouldReturnEmpty()
    {
        // Arrange
        var manager = new MetricsExportManager(_mockLogger.Object, _options);

        // Act
        var formats = manager.GetSupportedFormats();

        // Assert
        Assert.Empty(formats);
    }
}
