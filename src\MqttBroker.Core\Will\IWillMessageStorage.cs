namespace MqttBroker.Core.Will;

/// <summary>
/// 遗嘱消息存储接口
/// </summary>
public interface IWillMessageStorage : IDisposable
{
    /// <summary>
    /// 存储遗嘱消息注册信息
    /// </summary>
    /// <param name="registration">遗嘱消息注册信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>存储结果</returns>
    Task<WillMessageStorageResult> StoreWillMessageAsync(WillMessageRegistration registration, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取遗嘱消息注册信息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>遗嘱消息注册信息，如果不存在则返回null</returns>
    Task<WillMessageRegistration?> GetWillMessageAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除遗嘱消息注册信息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除结果</returns>
    Task<WillMessageStorageResult> DeleteWillMessageAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查遗嘱消息是否存在
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>如果存在则返回true，否则返回false</returns>
    Task<bool> ExistsAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取所有遗嘱消息注册信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>所有遗嘱消息注册信息</returns>
    Task<IList<WillMessageRegistration>> GetAllWillMessagesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取过期的遗嘱消息注册信息
    /// </summary>
    /// <param name="expirationTime">过期时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>过期的遗嘱消息注册信息</returns>
    Task<IList<WillMessageRegistration>> GetExpiredWillMessagesAsync(DateTime expirationTime, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量删除遗嘱消息注册信息
    /// </summary>
    /// <param name="clientIds">客户端ID列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除结果</returns>
    Task<WillMessageBatchStorageResult> DeleteWillMessagesAsync(IList<string> clientIds, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取存储统计信息
    /// </summary>
    /// <returns>存储统计信息</returns>
    Task<WillMessageStorageStatistics> GetStatisticsAsync();

    /// <summary>
    /// 清理所有遗嘱消息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理结果</returns>
    Task<WillMessageStorageResult> ClearAllAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 遗嘱消息存储结果
/// </summary>
public class WillMessageStorageResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 操作类型
    /// </summary>
    public WillMessageStorageOperation Operation { get; set; }

    /// <summary>
    /// 操作时间
    /// </summary>
    public DateTime OperationTime { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    public static WillMessageStorageResult Success(string clientId, WillMessageStorageOperation operation)
    {
        return new WillMessageStorageResult
        {
            IsSuccess = true,
            ClientId = clientId,
            Operation = operation,
            OperationTime = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    public static WillMessageStorageResult Failure(string clientId, WillMessageStorageOperation operation, string errorMessage)
    {
        return new WillMessageStorageResult
        {
            IsSuccess = false,
            ClientId = clientId,
            Operation = operation,
            OperationTime = DateTime.UtcNow,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// 遗嘱消息批量存储结果
/// </summary>
public class WillMessageBatchStorageResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 操作类型
    /// </summary>
    public WillMessageStorageOperation Operation { get; set; }

    /// <summary>
    /// 操作时间
    /// </summary>
    public DateTime OperationTime { get; set; }

    /// <summary>
    /// 成功处理的客户端ID列表
    /// </summary>
    public IList<string> SuccessfulClientIds { get; set; } = new List<string>();

    /// <summary>
    /// 失败的客户端ID列表
    /// </summary>
    public IList<string> FailedClientIds { get; set; } = new List<string>();

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    public static WillMessageBatchStorageResult Success(WillMessageStorageOperation operation, IList<string> successfulClientIds)
    {
        return new WillMessageBatchStorageResult
        {
            IsSuccess = true,
            Operation = operation,
            OperationTime = DateTime.UtcNow,
            SuccessfulClientIds = successfulClientIds
        };
    }

    /// <summary>
    /// 创建部分成功结果
    /// </summary>
    public static WillMessageBatchStorageResult PartialSuccess(WillMessageStorageOperation operation, 
        IList<string> successfulClientIds, IList<string> failedClientIds, string errorMessage)
    {
        return new WillMessageBatchStorageResult
        {
            IsSuccess = successfulClientIds.Count > 0,
            Operation = operation,
            OperationTime = DateTime.UtcNow,
            SuccessfulClientIds = successfulClientIds,
            FailedClientIds = failedClientIds,
            ErrorMessage = errorMessage
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    public static WillMessageBatchStorageResult Failure(WillMessageStorageOperation operation, IList<string> failedClientIds, string errorMessage)
    {
        return new WillMessageBatchStorageResult
        {
            IsSuccess = false,
            Operation = operation,
            OperationTime = DateTime.UtcNow,
            FailedClientIds = failedClientIds,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// 遗嘱消息存储操作类型
/// </summary>
public enum WillMessageStorageOperation
{
    /// <summary>
    /// 存储
    /// </summary>
    Store,

    /// <summary>
    /// 删除
    /// </summary>
    Delete,

    /// <summary>
    /// 批量删除
    /// </summary>
    BatchDelete,

    /// <summary>
    /// 清理所有
    /// </summary>
    ClearAll
}

/// <summary>
/// 遗嘱消息存储统计信息
/// </summary>
public class WillMessageStorageStatistics
{
    /// <summary>
    /// 总遗嘱消息数量
    /// </summary>
    public int TotalWillMessages { get; set; }

    /// <summary>
    /// 存储操作总数
    /// </summary>
    public long TotalStoreOperations { get; set; }

    /// <summary>
    /// 删除操作总数
    /// </summary>
    public long TotalDeleteOperations { get; set; }

    /// <summary>
    /// 查询操作总数
    /// </summary>
    public long TotalQueryOperations { get; set; }

    /// <summary>
    /// 失败操作总数
    /// </summary>
    public long TotalFailedOperations { get; set; }

    /// <summary>
    /// 平均操作延迟（毫秒）
    /// </summary>
    public double AverageOperationLatencyMs { get; set; }

    /// <summary>
    /// 最后操作时间
    /// </summary>
    public DateTime LastOperationTime { get; set; }

    /// <summary>
    /// 存储启动时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 按客户端ID分组的遗嘱消息数量
    /// </summary>
    public Dictionary<string, int> WillMessagesByClientId { get; set; } = new();

    /// <summary>
    /// 按主题分组的遗嘱消息数量
    /// </summary>
    public Dictionary<string, int> WillMessagesByTopic { get; set; } = new();
}
