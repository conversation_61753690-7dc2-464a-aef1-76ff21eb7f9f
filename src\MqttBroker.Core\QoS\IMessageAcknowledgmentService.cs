using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;

namespace MqttBroker.Core.QoS;

/// <summary>
/// 消息确认服务接口，负责管理 QoS 1 和 QoS 2 的消息确认流程
/// </summary>
public interface IMessageAcknowledgmentService
{
    /// <summary>
    /// 添加待确认的消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="messageId">消息ID</param>
    /// <param name="publishPacket">发布数据包</param>
    /// <param name="qosLevel">QoS级别</param>
    /// <returns>是否添加成功</returns>
    Task<bool> AddPendingMessageAsync(string clientId, ushort messageId, MqttPublishPacket publishPacket, MqttQoSLevel qosLevel);

    /// <summary>
    /// 确认 QoS 1 消息（收到 PUBACK）
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="messageId">消息ID</param>
    /// <returns>确认结果</returns>
    Task<AcknowledgmentResult> AcknowledgeQoS1MessageAsync(string clientId, ushort messageId);

    /// <summary>
    /// 确认 QoS 2 消息第一阶段（收到 PUBREC）
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="messageId">消息ID</param>
    /// <returns>确认结果</returns>
    Task<AcknowledgmentResult> AcknowledgeQoS2Phase1Async(string clientId, ushort messageId);

    /// <summary>
    /// 确认 QoS 2 消息第二阶段（收到 PUBCOMP）
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="messageId">消息ID</param>
    /// <returns>确认结果</returns>
    Task<AcknowledgmentResult> AcknowledgeQoS2Phase2Async(string clientId, ushort messageId);

    /// <summary>
    /// 获取待确认的消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="messageId">消息ID</param>
    /// <returns>待确认消息信息</returns>
    Task<PendingMessage?> GetPendingMessageAsync(string clientId, ushort messageId);

    /// <summary>
    /// 获取客户端的所有待确认消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>待确认消息列表</returns>
    Task<IList<PendingMessage>> GetClientPendingMessagesAsync(string clientId);

    /// <summary>
    /// 获取超时的待确认消息
    /// </summary>
    /// <param name="timeoutThreshold">超时阈值</param>
    /// <returns>超时的待确认消息列表</returns>
    Task<IList<PendingMessage>> GetTimedOutMessagesAsync(TimeSpan timeoutThreshold);

    /// <summary>
    /// 移除待确认消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="messageId">消息ID</param>
    /// <returns>是否移除成功</returns>
    Task<bool> RemovePendingMessageAsync(string clientId, ushort messageId);

    /// <summary>
    /// 清理客户端的所有待确认消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>清理的消息数量</returns>
    Task<int> ClearClientPendingMessagesAsync(string clientId);

    /// <summary>
    /// 获取确认统计信息
    /// </summary>
    /// <returns>确认统计信息</returns>
    Task<AcknowledgmentStatistics> GetStatisticsAsync();
}

/// <summary>
/// 待确认消息信息
/// </summary>
public class PendingMessage
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 消息ID
    /// </summary>
    public ushort MessageId { get; set; }

    /// <summary>
    /// 发布数据包
    /// </summary>
    public MqttPublishPacket PublishPacket { get; set; } = null!;

    /// <summary>
    /// QoS级别
    /// </summary>
    public MqttQoSLevel QoSLevel { get; set; }

    /// <summary>
    /// QoS 2 消息状态
    /// </summary>
    public QoS2MessageState QoS2State { get; set; } = QoS2MessageState.WaitingForPubRec;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后重传时间
    /// </summary>
    public DateTime? LastRetransmissionAt { get; set; }

    /// <summary>
    /// 重传次数
    /// </summary>
    public int RetransmissionCount { get; set; }

    /// <summary>
    /// 是否已超时
    /// </summary>
    public bool IsTimedOut(TimeSpan timeout) => DateTime.UtcNow - CreatedAt > timeout;

    /// <summary>
    /// 获取消息年龄
    /// </summary>
    public TimeSpan Age => DateTime.UtcNow - CreatedAt;
}

/// <summary>
/// QoS 2 消息状态枚举
/// </summary>
public enum QoS2MessageState
{
    /// <summary>
    /// 等待 PUBREC
    /// </summary>
    WaitingForPubRec,

    /// <summary>
    /// 等待 PUBCOMP
    /// </summary>
    WaitingForPubComp,

    /// <summary>
    /// 已完成
    /// </summary>
    Completed
}

/// <summary>
/// 确认结果
/// </summary>
public class AcknowledgmentResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 消息ID
    /// </summary>
    public ushort MessageId { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 确认的消息信息
    /// </summary>
    public PendingMessage? AcknowledgedMessage { get; set; }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    public static AcknowledgmentResult Success(string clientId, ushort messageId, PendingMessage? acknowledgedMessage = null)
    {
        return new AcknowledgmentResult
        {
            IsSuccess = true,
            ClientId = clientId,
            MessageId = messageId,
            AcknowledgedMessage = acknowledgedMessage
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    public static AcknowledgmentResult Failure(string clientId, ushort messageId, string errorMessage)
    {
        return new AcknowledgmentResult
        {
            IsSuccess = false,
            ClientId = clientId,
            MessageId = messageId,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// 确认统计信息
/// </summary>
public class AcknowledgmentStatistics
{
    /// <summary>
    /// 总待确认消息数量
    /// </summary>
    public int TotalPendingMessages { get; set; }

    /// <summary>
    /// QoS 1 待确认消息数量
    /// </summary>
    public int PendingQoS1Messages { get; set; }

    /// <summary>
    /// QoS 2 待确认消息数量
    /// </summary>
    public int PendingQoS2Messages { get; set; }

    /// <summary>
    /// 等待 PUBREC 的 QoS 2 消息数量
    /// </summary>
    public int QoS2WaitingForPubRec { get; set; }

    /// <summary>
    /// 等待 PUBCOMP 的 QoS 2 消息数量
    /// </summary>
    public int QoS2WaitingForPubComp { get; set; }

    /// <summary>
    /// 有待确认消息的客户端数量
    /// </summary>
    public int ClientsWithPendingMessages { get; set; }

    /// <summary>
    /// 平均消息年龄（毫秒）
    /// </summary>
    public double AverageMessageAge { get; set; }

    /// <summary>
    /// 最老的消息年龄（毫秒）
    /// </summary>
    public double OldestMessageAge { get; set; }

    /// <summary>
    /// 总确认的消息数量
    /// </summary>
    public long TotalAcknowledgedMessages { get; set; }

    /// <summary>
    /// 总确认的 QoS 1 消息数量
    /// </summary>
    public long TotalAcknowledgedQoS1Messages { get; set; }

    /// <summary>
    /// 总确认的 QoS 2 消息数量
    /// </summary>
    public long TotalAcknowledgedQoS2Messages { get; set; }
}
