using System;
using System.Buffers;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Extensions.Logging;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using Xunit;
using Xunit.Abstractions;

namespace MqttBroker.Tests.Unit.Protocol
{
    /// <summary>
    /// 测试MQTT数据包解析的健壮性
    /// </summary>
    public class PacketParsingRobustnessTests
    {
        private readonly ITestOutputHelper _output;
        private readonly ILogger<MqttPacketParser> _logger;
        private readonly MqttPacketParser _parser;

        public PacketParsingRobustnessTests(ITestOutputHelper output)
        {
            _output = output;
            _logger = new TestLogger<MqttPacketParser>(output);
            _parser = new MqttPacketParser(_logger);
        }

        [Fact]
        public void ParsePacket_WithMalformedStringLength_ShouldReturnNull()
        {
            // 构造一个恶意的CONNECT包，字符串长度字段指向超出缓冲区的位置
            var buffer = new byte[]
            {
                0x10, // CONNECT包类型
                0x0A, // 剩余长度 = 10字节
                0x05, 0x11, // 字符串长度 = 1297 (0x0511) - 这会导致原来的错误
                0x4D, 0x51, 0x54, 0x54, // "MQTT"的前4个字节
                0x04, // 协议版本
                0x00  // 连接标志
            };

            var sequence = new ReadOnlySequence<byte>(buffer);
            var result = _parser.ParsePacket(sequence, MqttProtocolVersion.Version311);

            // 应该返回null而不是抛出异常
            Assert.Null(result);
        }

        [Fact]
        public void ParsePacket_WithValidConnectPacket_ShouldParseSuccessfully()
        {
            // 构造一个有效的CONNECT包
            var protocolName = "MQTT";
            var clientId = "TestClient";
            
            var buffer = new List<byte>();
            
            // 固定头部
            buffer.Add(0x10); // CONNECT包类型
            
            // 计算剩余长度
            var payload = new List<byte>();
            
            // 协议名称
            var protocolNameBytes = Encoding.UTF8.GetBytes(protocolName);
            payload.Add((byte)(protocolNameBytes.Length >> 8));
            payload.Add((byte)(protocolNameBytes.Length & 0xFF));
            payload.AddRange(protocolNameBytes);
            
            // 协议版本
            payload.Add(0x04);
            
            // 连接标志
            payload.Add(0x00);
            
            // Keep Alive
            payload.Add(0x00);
            payload.Add(0x3C); // 60秒
            
            // 客户端ID
            var clientIdBytes = Encoding.UTF8.GetBytes(clientId);
            payload.Add((byte)(clientIdBytes.Length >> 8));
            payload.Add((byte)(clientIdBytes.Length & 0xFF));
            payload.AddRange(clientIdBytes);
            
            // 剩余长度
            buffer.Add((byte)payload.Count);
            buffer.AddRange(payload);

            var sequence = new ReadOnlySequence<byte>(buffer.ToArray());
            var result = _parser.ParsePacket(sequence, MqttProtocolVersion.Version311);

            // 应该成功解析
            Assert.NotNull(result);
            Assert.IsType<MqttConnectPacket>(result);
            
            var connectPacket = (MqttConnectPacket)result;
            Assert.Equal(protocolName, connectPacket.ProtocolName);
            Assert.Equal(clientId, connectPacket.ClientId);
        }

        [Fact]
        public void ParsePacket_WithInvalidProtocolName_ShouldReturnNull()
        {
            // 构造一个包含无效协议名称的CONNECT包
            var invalidProtocolName = "INVALID";
            
            var buffer = new List<byte>();
            buffer.Add(0x10); // CONNECT包类型
            
            var payload = new List<byte>();
            var protocolNameBytes = Encoding.UTF8.GetBytes(invalidProtocolName);
            payload.Add((byte)(protocolNameBytes.Length >> 8));
            payload.Add((byte)(protocolNameBytes.Length & 0xFF));
            payload.AddRange(protocolNameBytes);
            payload.Add(0x04); // 协议版本
            payload.Add(0x00); // 连接标志
            payload.Add(0x00); // Keep Alive高字节
            payload.Add(0x3C); // Keep Alive低字节
            payload.Add(0x00); // 客户端ID长度高字节
            payload.Add(0x00); // 客户端ID长度低字节
            
            buffer.Add((byte)payload.Count);
            buffer.AddRange(payload);

            var sequence = new ReadOnlySequence<byte>(buffer.ToArray());
            var result = _parser.ParsePacket(sequence, MqttProtocolVersion.Version311);

            // 应该返回null
            Assert.Null(result);
        }

        [Fact]
        public void TryParsePacketHeader_WithInvalidPacketType_ShouldReturnFalse()
        {
            // 构造一个包含无效数据包类型的缓冲区
            var buffer = new byte[]
            {
                0x00, // 无效的数据包类型 (0)
                0x02, // 剩余长度
                0x00, 0x00 // 数据
            };

            var sequence = new ReadOnlySequence<byte>(buffer);
            var result = _parser.TryParsePacketHeader(sequence, out var packetLength, out var headerLength);

            // 应该返回false
            Assert.False(result);
        }

        [Fact]
        public void TryParsePacketHeader_WithExcessivePacketLength_ShouldReturnFalse()
        {
            // 构造一个包含过大剩余长度的缓冲区
            var buffer = new byte[]
            {
                0x10, // CONNECT包类型
                0xFF, 0xFF, 0xFF, 0x7F // 最大可能的剩余长度
            };

            var sequence = new ReadOnlySequence<byte>(buffer);
            var result = _parser.TryParsePacketHeader(sequence, out var packetLength, out var headerLength);

            // 应该返回false，因为超出了最大数据包大小
            Assert.False(result);
        }

        [Fact]
        public void ParsePacket_WithIncompleteData_ShouldReturnNull()
        {
            // 构造一个不完整的CONNECT包
            var buffer = new byte[]
            {
                0x10, // CONNECT包类型
                0x10, // 剩余长度 = 16字节
                0x00, 0x04, // 协议名称长度 = 4
                0x4D, 0x51, 0x54, 0x54, // "MQTT"
                0x04, // 协议版本
                0x00, // 连接标志
                0x00, 0x3C, // Keep Alive = 60
                0x00, 0x04 // 客户端ID长度 = 4，但后面没有实际的客户端ID数据
            };

            var sequence = new ReadOnlySequence<byte>(buffer);
            var result = _parser.ParsePacket(sequence, MqttProtocolVersion.Version311);

            // 应该返回null，因为数据不完整
            Assert.Null(result);
        }

        [Fact]
        public void ParsePacket_WithInvalidConnectFlags_ShouldReturnNull()
        {
            // 构造一个包含无效连接标志的CONNECT包
            var buffer = new List<byte>();
            buffer.Add(0x10); // CONNECT包类型

            var payload = new List<byte>();
            // 协议名称 "MQTT"
            payload.AddRange(new byte[] { 0x00, 0x04, 0x4D, 0x51, 0x54, 0x54 });
            payload.Add(0x04); // 协议版本
            payload.Add(0x01); // 无效的连接标志（保留位设置为1）
            payload.AddRange(new byte[] { 0x00, 0x3C }); // Keep Alive
            payload.AddRange(new byte[] { 0x00, 0x00 }); // 空客户端ID

            buffer.Add((byte)payload.Count);
            buffer.AddRange(payload);

            var sequence = new ReadOnlySequence<byte>(buffer.ToArray());
            var result = _parser.ParsePacket(sequence, MqttProtocolVersion.Version311);

            // 应该返回null
            Assert.Null(result);
        }

        [Fact]
        public void ParsePacket_WithOriginalErrorScenario_ShouldReturnNull()
        {
            // 重现原始错误场景：字符串长度1297，但总缓冲区大小只有34字节
            var buffer = new byte[]
            {
                0x10, // CONNECT包类型
                0x20, // 剩余长度 = 32字节（这是错误的，实际数据不足）
                0x05, 0x11, // 字符串长度 = 1297 (0x0511) - 这会导致原来的错误
                0x4D, 0x51, 0x54, 0x54, // "MQTT"的前4个字节
                0x04, // 协议版本
                0x00, // 连接标志
                0x00, 0x3C, // Keep Alive
                0x00, 0x04, // 客户端ID长度 = 4
                0x54, 0x65, 0x73, 0x74, // "Test"
                // 剩余的字节用于填充到声明的长度
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00
            };

            var sequence = new ReadOnlySequence<byte>(buffer);
            var result = _parser.ParsePacket(sequence, MqttProtocolVersion.Version311);

            // 应该返回null而不是抛出异常
            Assert.Null(result);
        }

        [Fact]
        public void ParsePacket_WithDataLengthMismatch_ShouldReturnNull()
        {
            // 测试数据长度不匹配的情况
            var buffer = new byte[]
            {
                0x10, // CONNECT包类型
                0x10, // 剩余长度 = 16字节
                0x00, 0x04, // 协议名称长度 = 4
                0x4D, 0x51, 0x54, 0x54, // "MQTT"
                0x04, // 协议版本
                0x00, // 连接标志
                0x00, 0x3C, // Keep Alive
                0x00, 0x04, // 客户端ID长度 = 4
                0x54, 0x65 // 只有"Te"，缺少"st"
                // 实际数据只有14字节，但声明的剩余长度是16字节
            };

            var sequence = new ReadOnlySequence<byte>(buffer);
            var result = _parser.ParsePacket(sequence, MqttProtocolVersion.Version311);

            // 应该返回null，因为数据长度不匹配
            Assert.Null(result);
        }
    }

    /// <summary>
    /// 测试用的Logger实现
    /// </summary>
    public class TestLogger<T> : ILogger<T>
    {
        private readonly ITestOutputHelper _output;

        public TestLogger(ITestOutputHelper output)
        {
            _output = output;
        }

        public IDisposable? BeginScope<TState>(TState state) where TState : notnull => null;
        public bool IsEnabled(LogLevel logLevel) => true;

        public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
        {
            _output.WriteLine($"[{logLevel}] {formatter(state, exception)}");
            if (exception != null)
            {
                _output.WriteLine(exception.ToString());
            }
        }
    }
}
