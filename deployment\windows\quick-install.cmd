@echo off
REM MQTT Broker 快速安装脚本
REM 解决路径和参数问题的简化版本

setlocal enabledelayedexpansion

REM 设置控制台代码页为 UTF-8
chcp 65001 >nul

echo ========================================
echo MQTT Broker 快速安装向导
echo ========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [错误] 需要管理员权限
    echo 请右键点击"命令提示符"，选择"以管理员身份运行"
    echo.
    pause
    exit /b 1
)

REM 获取当前脚本所在目录
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%..\.."

echo [信息] 项目根目录: %PROJECT_ROOT%
echo [信息] 脚本目录: %SCRIPT_DIR%
echo.

REM 检查项目文件是否存在
if not exist "%PROJECT_ROOT%\src\MqttBroker.Host" (
    echo [错误] 未找到项目源代码
    echo 请确保在正确的项目目录中运行此脚本
    echo 期望路径: %PROJECT_ROOT%\src\MqttBroker.Host
    pause
    exit /b 1
)

REM 安装选项菜单
echo 请选择安装选项:
echo.
echo 1. 基础安装 (SQLite + 内存缓存)
echo 2. 标准安装 (SQLite + Redis)
echo 3. 完整安装 (PostgreSQL + Redis)
echo 4. 自定义安装
echo.
set /p "CHOICE=请输入选择 (1-4): "

REM 根据选择设置参数
set "INSTALL_ARGS="
set "INSTALL_DESC=基础安装"

if "%CHOICE%"=="1" (
    set "INSTALL_ARGS="
    set "INSTALL_DESC=基础安装 (SQLite + 内存缓存)"
)
if "%CHOICE%"=="2" (
    set "INSTALL_ARGS=--install-redis"
    set "INSTALL_DESC=标准安装 (SQLite + Redis)"
)
if "%CHOICE%"=="3" (
    set "INSTALL_ARGS=--install-redis --install-postgresql"
    set "INSTALL_DESC=完整安装 (PostgreSQL + Redis)"
)
if "%CHOICE%"=="4" (
    goto custom_install
)

if "%CHOICE%" gtr "4" (
    echo [错误] 无效选择
    pause
    exit /b 1
)

goto start_install

:custom_install
echo.
echo 自定义安装选项:
echo.

REM 安装路径
set /p "CUSTOM_PATH=安装路径 (回车使用默认 C:\MqttBroker): "
if not "%CUSTOM_PATH%"=="" (
    set "INSTALL_ARGS=!INSTALL_ARGS! --install-path "%CUSTOM_PATH%""
)

REM Redis 选项
set /p "INSTALL_REDIS=是否安装 Redis? (y/N): "
if /i "%INSTALL_REDIS%"=="y" (
    set "INSTALL_ARGS=!INSTALL_ARGS! --install-redis"
)

REM PostgreSQL 选项
set /p "INSTALL_POSTGRESQL=是否安装 PostgreSQL? (y/N): "
if /i "%INSTALL_POSTGRESQL%"=="y" (
    set "INSTALL_ARGS=!INSTALL_ARGS! --install-postgresql"
)

set "INSTALL_DESC=自定义安装"

:start_install
echo.
echo ========================================
echo 开始安装: %INSTALL_DESC%
echo ========================================
echo.

REM 显示即将执行的命令
echo [信息] 即将执行:
echo "%SCRIPT_DIR%install.cmd" %INSTALL_ARGS%
echo.

REM 确认安装
set /p "CONFIRM=确认开始安装? (Y/n): "
if /i "%CONFIRM%"=="n" (
    echo 安装已取消
    pause
    exit /b 0
)

echo.
echo [信息] 开始安装，请稍候...
echo.

REM 执行安装
if "%INSTALL_ARGS%"=="" (
    call "%SCRIPT_DIR%install.cmd"
) else (
    call "%SCRIPT_DIR%install.cmd" %INSTALL_ARGS%
)

REM 检查安装结果
if %errorLevel% equ 0 (
    echo.
    echo ========================================
    echo 安装完成！
    echo ========================================
    echo.
    echo 下一步操作:
    echo 1. 启动服务: C:\MqttBroker\scripts\start.cmd start
    echo 2. 查看状态: C:\MqttBroker\scripts\start.cmd status
    echo 3. 查看日志: C:\MqttBroker\scripts\start.cmd logs
    echo.
    echo 访问地址:
    echo   MQTT TCP: localhost:1883
    echo   WebSocket: ws://localhost:8080/mqtt
    echo   健康检查: http://localhost:9090/health
    echo.
    
    REM 询问是否立即启动
    set /p "START_NOW=是否立即启动 MQTT Broker? (Y/n): "
    if /i not "%START_NOW%"=="n" (
        echo.
        echo [信息] 启动 MQTT Broker...
        call "C:\MqttBroker\scripts\start.cmd" start
    )
) else (
    echo.
    echo ========================================
    echo 安装失败！
    echo ========================================
    echo.
    echo 请检查错误信息并重试
    echo 如需帮助，请查看安装日志
)

echo.
pause
endlocal
