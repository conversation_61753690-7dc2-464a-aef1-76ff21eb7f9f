using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Network;

namespace MqttBroker.Core.QoS.Examples;

/// <summary>
/// QoS 处理系统使用示例
/// </summary>
public class QoSExample
{
    /// <summary>
    /// 运行示例
    /// </summary>
    public static async Task RunAsync()
    {
        // 创建服务容器
        var services = new ServiceCollection();
        
        // 添加日志服务
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        
        // 添加高性能 QoS 处理服务
        services.AddHighPerformanceQoSProcessing();
        
        // 构建服务提供者
        var serviceProvider = services.BuildServiceProvider();
        
        // 获取 QoS 管理器
        var qosManager = serviceProvider.GetRequiredService<IQoSManager>();
        var acknowledgmentService = serviceProvider.GetRequiredService<IMessageAcknowledgmentService>();
        var retransmissionService = serviceProvider.GetRequiredService<IMessageRetransmissionService>();
        var deduplicationService = serviceProvider.GetRequiredService<IMessageDeduplicationService>();
        
        // 启动 QoS 服务
        await qosManager.StartAsync();
        
        Console.WriteLine("=== MQTT Broker QoS 处理系统示例 ===");
        Console.WriteLine();
        
        // 示例1：QoS 0 消息处理
        Console.WriteLine("1. QoS 0 消息处理示例");
        await DemonstrateQoS0Processing(qosManager);
        Console.WriteLine();
        
        // 示例2：QoS 1 消息处理
        Console.WriteLine("2. QoS 1 消息处理示例");
        await DemonstrateQoS1Processing(qosManager, acknowledgmentService);
        Console.WriteLine();
        
        // 示例3：QoS 2 消息处理
        Console.WriteLine("3. QoS 2 消息处理示例");
        await DemonstrateQoS2Processing(qosManager, deduplicationService);
        Console.WriteLine();
        
        // 示例4：统计信息
        Console.WriteLine("4. QoS 统计信息");
        await DisplayStatistics(qosManager, acknowledgmentService, retransmissionService, deduplicationService);
        Console.WriteLine();
        
        // 停止 QoS 服务
        await qosManager.StopAsync();
        
        Console.WriteLine("QoS 处理系统示例完成！");
    }
    
    /// <summary>
    /// 演示 QoS 0 消息处理
    /// </summary>
    private static async Task DemonstrateQoS0Processing(IQoSManager qosManager)
    {
        var mockConnection = new MockClientConnection("client-qos0");
        var publishPacket = MqttPublishPacket.Create("sensors/temperature", 
            System.Text.Encoding.UTF8.GetBytes("25.5"), MqttQoSLevel.AtMostOnce);
        
        var result = await qosManager.ProcessPublishAsync(mockConnection, publishPacket);
        
        Console.WriteLine($"  QoS 0 消息处理结果: {(result.IsSuccess ? "成功" : "失败")}");
        Console.WriteLine($"  处理耗时: {result.ElapsedMilliseconds}ms");
    }
    
    /// <summary>
    /// 演示 QoS 1 消息处理
    /// </summary>
    private static async Task DemonstrateQoS1Processing(IQoSManager qosManager, IMessageAcknowledgmentService acknowledgmentService)
    {
        var mockConnection = new MockClientConnection("client-qos1");
        var publishPacket = MqttPublishPacket.Create("sensors/humidity", 
            System.Text.Encoding.UTF8.GetBytes("60.2"), MqttQoSLevel.AtLeastOnce);
        publishPacket.PacketIdentifier = 1001;
        
        // 处理 PUBLISH 消息
        var publishResult = await qosManager.ProcessPublishAsync(mockConnection, publishPacket);
        Console.WriteLine($"  QoS 1 PUBLISH 处理结果: {(publishResult.IsSuccess ? "成功" : "失败")}");
        
        // 模拟收到 PUBACK
        var pubackPacket = MqttPubAckPacket.Create(1001, MqttReasonCode.Success);
        var pubackResult = await qosManager.ProcessPubAckAsync(mockConnection, pubackPacket);
        Console.WriteLine($"  QoS 1 PUBACK 处理结果: {(pubackResult.IsSuccess ? "成功" : "失败")}");
        
        // 检查待确认消息
        var pendingStats = await qosManager.GetPendingMessageStatisticsAsync("client-qos1");
        Console.WriteLine($"  待确认消息数量: {pendingStats.TotalPendingMessages}");
    }
    
    /// <summary>
    /// 演示 QoS 2 消息处理
    /// </summary>
    private static async Task DemonstrateQoS2Processing(IQoSManager qosManager, IMessageDeduplicationService deduplicationService)
    {
        var mockConnection = new MockClientConnection("client-qos2");
        var publishPacket = MqttPublishPacket.Create("sensors/pressure", 
            System.Text.Encoding.UTF8.GetBytes("1013.25"), MqttQoSLevel.ExactlyOnce);
        publishPacket.PacketIdentifier = 2001;
        
        // 处理 PUBLISH 消息
        var publishResult = await qosManager.ProcessPublishAsync(mockConnection, publishPacket);
        Console.WriteLine($"  QoS 2 PUBLISH 处理结果: {(publishResult.IsSuccess ? "成功" : "失败")}");
        
        // 模拟收到 PUBREC
        var pubrecPacket = MqttPubRecPacket.Create(2001, MqttReasonCode.Success);
        var pubrecResult = await qosManager.ProcessPubRecAsync(mockConnection, pubrecPacket);
        Console.WriteLine($"  QoS 2 PUBREC 处理结果: {(pubrecResult.IsSuccess ? "成功" : "失败")}");
        
        // 模拟收到 PUBREL
        var pubrelPacket = MqttPubRelPacket.Create(2001, MqttReasonCode.Success);
        var pubrelResult = await qosManager.ProcessPubRelAsync(mockConnection, pubrelPacket);
        Console.WriteLine($"  QoS 2 PUBREL 处理结果: {(pubrelResult.IsSuccess ? "成功" : "失败")}");
        
        // 模拟收到 PUBCOMP
        var pubcompPacket = MqttPubCompPacket.Create(2001, MqttReasonCode.Success);
        var pubcompResult = await qosManager.ProcessPubCompAsync(mockConnection, pubcompPacket);
        Console.WriteLine($"  QoS 2 PUBCOMP 处理结果: {(pubcompResult.IsSuccess ? "成功" : "失败")}");
        
        // 测试重复消息检测
        var duplicateResult = await deduplicationService.CheckDuplicateAsync("client-qos2", 2001, publishPacket);
        Console.WriteLine($"  重复消息检测: {(duplicateResult.IsDuplicate ? "检测到重复" : "非重复消息")}");
    }
    
    /// <summary>
    /// 显示统计信息
    /// </summary>
    private static async Task DisplayStatistics(
        IQoSManager qosManager, 
        IMessageAcknowledgmentService acknowledgmentService,
        IMessageRetransmissionService retransmissionService,
        IMessageDeduplicationService deduplicationService)
    {
        var globalStats = await qosManager.GetGlobalStatisticsAsync();
        var ackStats = await acknowledgmentService.GetStatisticsAsync();
        var retransStats = await retransmissionService.GetStatisticsAsync();
        var dedupStats = await deduplicationService.GetStatisticsAsync();
        
        Console.WriteLine("  全局 QoS 统计:");
        Console.WriteLine($"    总 QoS 1 消息: {globalStats.TotalQoS1MessagesProcessed}");
        Console.WriteLine($"    总 QoS 2 消息: {globalStats.TotalQoS2MessagesProcessed}");
        Console.WriteLine($"    平均处理延迟: {globalStats.AverageProcessingLatency:F2}ms");
        
        Console.WriteLine("  确认统计:");
        Console.WriteLine($"    总确认消息: {ackStats.TotalAcknowledgedMessages}");
        Console.WriteLine($"    待确认消息: {ackStats.TotalPendingMessages}");
        
        Console.WriteLine("  重传统计:");
        Console.WriteLine($"    总重传次数: {retransStats.TotalRetransmissions}");
        Console.WriteLine($"    成功重传: {retransStats.SuccessfulRetransmissions}");
        
        Console.WriteLine("  去重统计:");
        Console.WriteLine($"    检查消息数: {dedupStats.TotalCheckedMessages}");
        Console.WriteLine($"    重复消息数: {dedupStats.TotalDuplicateMessages}");
        Console.WriteLine($"    重复检测率: {dedupStats.DuplicateDetectionRate:F2}%");
    }
}

/// <summary>
/// 模拟客户端连接
/// </summary>
public class MockClientConnection : IClientConnection
{
    public string Id { get; } = Guid.NewGuid().ToString();
    public string? ClientId { get; set; }
    public System.Net.EndPoint RemoteEndPoint { get; } = new System.Net.IPEndPoint(System.Net.IPAddress.Loopback, 0);
    public System.Net.EndPoint LocalEndPoint { get; } = new System.Net.IPEndPoint(System.Net.IPAddress.Loopback, 1883);
    public ConnectionState State { get; } = ConnectionState.Connected;
    public DateTime ConnectedAt { get; } = DateTime.UtcNow;
    public DateTime LastActivity { get; private set; } = DateTime.UtcNow;
    public MqttProtocolVersion? ProtocolVersion { get; set; } = MqttProtocolVersion.Version311;
    public bool IsAuthenticated { get; set; } = true;
    public int KeepAliveInterval { get; set; } = 60;
    public IDictionary<string, object> Properties { get; } = new Dictionary<string, object>();

    public MockClientConnection(string clientId)
    {
        ClientId = clientId;
    }

    public Task SendPacketAsync(IMqttPacket packet, CancellationToken cancellationToken = default)
    {
        Console.WriteLine($"    发送数据包到客户端 {ClientId}: {packet.GetType().Name}");
        UpdateLastActivity();
        return Task.CompletedTask;
    }

    public Task SendDataAsync(ReadOnlyMemory<byte> data, CancellationToken cancellationToken = default)
    {
        Console.WriteLine($"    发送原始数据到客户端 {ClientId}: {data.Length} bytes");
        UpdateLastActivity();
        return Task.CompletedTask;
    }

    public Task CloseAsync(DisconnectionReason reason = DisconnectionReason.ServerDisconnected, CancellationToken cancellationToken = default)
    {
        Console.WriteLine($"    关闭客户端连接 {ClientId}: {reason}");
        ConnectionClosed?.Invoke(this, new ConnectionClosedEventArgs(reason));
        return Task.CompletedTask;
    }

    public void UpdateLastActivity()
    {
        LastActivity = DateTime.UtcNow;
    }

    public ClientConnectionStatistics GetStatistics()
    {
        return new ClientConnectionStatistics
        {
            ConnectionId = Id,
            ClientId = ClientId,
            RemoteEndPoint = RemoteEndPoint.ToString(),
            ConnectedAt = ConnectedAt,
            LastActivity = LastActivity,
            ProtocolVersion = ProtocolVersion,
            IsAuthenticated = IsAuthenticated,
            KeepAliveInterval = KeepAliveInterval
        };
    }

    public void Dispose()
    {
        // 模拟连接清理
    }

    public event EventHandler<PacketReceivedEventArgs>? PacketReceived;
    public event EventHandler<ConnectionClosedEventArgs>? ConnectionClosed;
}
