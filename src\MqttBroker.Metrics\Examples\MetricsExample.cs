using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MqttBroker.Metrics.Alerts;
using MqttBroker.Metrics.Collectors;
using MqttBroker.Metrics.Configuration;
using MqttBroker.Metrics.Models;
using MqttBroker.Metrics.Services;

namespace MqttBroker.Metrics.Examples;

/// <summary>
/// 性能监控系统使用示例
/// </summary>
public class MetricsExample
{
    /// <summary>
    /// 运行示例
    /// </summary>
    public static async Task RunAsync()
    {
        Console.WriteLine("=== MQTT Broker 性能监控系统演示 ===\n");

        // 创建服务容器
        var services = new ServiceCollection();
        
        // 添加日志服务
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Information);
        });

        // 添加性能监控服务
        services.AddMqttBrokerMetrics(
            metrics =>
            {
                metrics.EnableMetrics = true;
                metrics.CollectionIntervalMs = 2000; // 2秒收集一次
                metrics.MaxInMemoryDataPoints = 100;
                metrics.EnableDetailedMetrics = true;
                metrics.EnableSystemMetrics = true;
                metrics.EnableNetworkMetrics = true;
                metrics.Export.EnableFileExport = true;
                metrics.Export.FileExportPath = "./metrics_export";
                metrics.Export.FileExportFormats = new List<ExportFormat> { ExportFormat.Json, ExportFormat.Csv };
                metrics.Export.ExportIntervalSeconds = 10;
            },
            alerts =>
            {
                alerts.EnableAlerts = true;
                alerts.EvaluationIntervalSeconds = 5;
                alerts.MaxActiveAlerts = 100;
            });

        var serviceProvider = services.BuildServiceProvider();

        try
        {
            await RunMetricsDemo(serviceProvider);
        }
        finally
        {
            await serviceProvider.DisposeAsync();
        }
    }

    /// <summary>
    /// 运行性能监控演示
    /// </summary>
    private static async Task RunMetricsDemo(IServiceProvider serviceProvider)
    {
        var logger = serviceProvider.GetRequiredService<ILogger<MetricsExample>>();
        var metricsManager = serviceProvider.GetRequiredService<IMetricsManager>();
        var alertManager = serviceProvider.GetRequiredService<IAlertManager>();
        var connectionCollector = serviceProvider.GetRequiredService<IConnectionMetricsCollector>();
        var messageCollector = serviceProvider.GetRequiredService<IMessageMetricsCollector>();
        var subscriptionCollector = serviceProvider.GetRequiredService<ISubscriptionMetricsCollector>();
        var qosCollector = serviceProvider.GetRequiredService<IQoSMetricsCollector>();

        // 启动后台服务
        var hostedServices = serviceProvider.GetServices<IHostedService>();
        foreach (var service in hostedServices)
        {
            await service.StartAsync(CancellationToken.None);
        }

        Console.WriteLine("=== 1. 添加告警规则 ===");
        await AddAlertRules(alertManager);

        Console.WriteLine("\n=== 2. 模拟 MQTT Broker 活动 ===");
        await SimulateMqttActivity(connectionCollector, messageCollector, subscriptionCollector, qosCollector);

        Console.WriteLine("\n=== 3. 收集和显示性能指标 ===");
        await DisplayCurrentMetrics(metricsManager);

        Console.WriteLine("\n=== 4. 模拟高负载触发告警 ===");
        await SimulateHighLoad(connectionCollector, messageCollector);

        Console.WriteLine("\n=== 5. 检查活跃告警 ===");
        await DisplayActiveAlerts(alertManager);

        Console.WriteLine("\n=== 6. 导出性能指标 ===");
        await ExportMetrics(metricsManager);

        Console.WriteLine("\n=== 7. 显示统计信息 ===");
        await DisplayStatistics(metricsManager, alertManager);

        // 停止后台服务
        foreach (var service in hostedServices)
        {
            await service.StopAsync(CancellationToken.None);
        }

        Console.WriteLine("\n=== 演示完成 ===");
    }

    /// <summary>
    /// 添加告警规则
    /// </summary>
    private static async Task AddAlertRules(IAlertManager alertManager)
    {
        // 连接数告警规则
        var connectionRule = new AlertRule
        {
            Name = "高连接数告警",
            Description = "当活跃连接数超过阈值时触发告警",
            MetricPath = "Connection.ActiveConnections",
            Operator = ComparisonOperator.GreaterThan,
            Threshold = 100,
            Level = AlertLevel.Warning,
            TriggerCount = 1,
            SuppressionSeconds = 60
        };
        await alertManager.AddRuleAsync(connectionRule);

        // 消息速率告警规则
        var messageRateRule = new AlertRule
        {
            Name = "高消息发送速率告警",
            Description = "当消息发送速率过高时触发告警",
            MetricPath = "Message.MessageSendRate",
            Operator = ComparisonOperator.GreaterThan,
            Threshold = 1000,
            Level = AlertLevel.Error,
            TriggerCount = 2,
            SuppressionSeconds = 120
        };
        await alertManager.AddRuleAsync(messageRateRule);

        // CPU 使用率告警规则
        var cpuRule = new AlertRule
        {
            Name = "高CPU使用率告警",
            Description = "当CPU使用率过高时触发告警",
            MetricPath = "System.CpuUsage",
            Operator = ComparisonOperator.GreaterThan,
            Threshold = 80,
            Level = AlertLevel.Critical,
            TriggerCount = 3,
            SuppressionSeconds = 300
        };
        await alertManager.AddRuleAsync(cpuRule);

        Console.WriteLine("✅ 已添加 3 个告警规则");
    }

    /// <summary>
    /// 模拟 MQTT Broker 活动
    /// </summary>
    private static async Task SimulateMqttActivity(
        IConnectionMetricsCollector connectionCollector,
        IMessageMetricsCollector messageCollector,
        ISubscriptionMetricsCollector subscriptionCollector,
        IQoSMetricsCollector qosCollector)
    {
        var random = new Random();

        // 模拟客户端连接
        for (int i = 1; i <= 50; i++)
        {
            connectionCollector.RecordConnection($"client-{i:D3}");
            await Task.Delay(10);
        }

        // 模拟订阅
        for (int i = 1; i <= 50; i++)
        {
            subscriptionCollector.RecordSubscription($"client-{i:D3}", $"sensors/+/temperature", true);
            subscriptionCollector.RecordSubscription($"client-{i:D3}", $"devices/client-{i:D3}/status", false);
            await Task.Delay(5);
        }

        // 模拟消息发送和接收
        for (int i = 0; i < 200; i++)
        {
            var messageSize = random.Next(100, 1000);
            var routingLatency = TimeSpan.FromMilliseconds(random.NextDouble() * 10);
            
            messageCollector.RecordMessageSent(messageSize, routingLatency);
            messageCollector.RecordMessageReceived(messageSize);

            // 模拟 QoS 处理
            var qosLevel = random.Next(0, 3);
            var processingLatency = TimeSpan.FromMilliseconds(random.NextDouble() * 5);
            qosCollector.RecordQoSProcessing(qosLevel, processingLatency, true);

            if (i % 20 == 0)
            {
                Console.Write(".");
            }
            
            await Task.Delay(50);
        }

        Console.WriteLine("\n✅ 已模拟 50 个客户端连接、100 个订阅、200 条消息");
    }

    /// <summary>
    /// 显示当前性能指标
    /// </summary>
    private static async Task DisplayCurrentMetrics(IMetricsManager metricsManager)
    {
        var metrics = await metricsManager.GetCurrentMetricsAsync();

        Console.WriteLine($"📊 当前性能指标 (时间: {metrics.Timestamp:HH:mm:ss}):");
        Console.WriteLine($"   连接数: {metrics.Connection.ActiveConnections} (峰值: {metrics.Connection.PeakConnections})");
        Console.WriteLine($"   消息: 发送 {metrics.Message.MessagesSent}, 接收 {metrics.Message.MessagesReceived}");
        Console.WriteLine($"   消息速率: 发送 {metrics.Message.MessageSendRate:F1}/s, 接收 {metrics.Message.MessageReceiveRate:F1}/s");
        Console.WriteLine($"   订阅数: {metrics.Subscription.TotalSubscriptions} (活跃主题: {metrics.Subscription.ActiveTopics})");
        Console.WriteLine($"   QoS: QoS0={metrics.QoS.QoS0Messages}, QoS1={metrics.QoS.QoS1Messages}, QoS2={metrics.QoS.QoS2Messages}");
        Console.WriteLine($"   系统: CPU {metrics.System.CpuUsage:F1}%, 内存 {metrics.System.Memory.TotalMemory / 1024 / 1024:F1}MB");
    }

    /// <summary>
    /// 模拟高负载
    /// </summary>
    private static async Task SimulateHighLoad(
        IConnectionMetricsCollector connectionCollector,
        IMessageMetricsCollector messageCollector)
    {
        Console.WriteLine("🔥 模拟高负载场景...");

        // 模拟大量连接
        for (int i = 51; i <= 150; i++)
        {
            connectionCollector.RecordConnection($"client-{i:D3}");
        }

        // 模拟高频消息
        for (int i = 0; i < 500; i++)
        {
            var messageSize = 500;
            var routingLatency = TimeSpan.FromMilliseconds(2);
            
            messageCollector.RecordMessageSent(messageSize, routingLatency);
            messageCollector.RecordMessageReceived(messageSize);

            if (i % 50 == 0)
            {
                Console.Write("🔥");
            }
        }

        Console.WriteLine("\n✅ 高负载模拟完成");
        await Task.Delay(2000); // 等待指标收集
    }

    /// <summary>
    /// 显示活跃告警
    /// </summary>
    private static async Task DisplayActiveAlerts(IAlertManager alertManager)
    {
        var activeAlerts = await alertManager.GetActiveAlertsAsync();
        var alertsList = activeAlerts.ToList();

        Console.WriteLine($"🚨 活跃告警数量: {alertsList.Count}");
        
        foreach (var alert in alertsList)
        {
            var levelIcon = alert.Level switch
            {
                AlertLevel.Info => "ℹ️",
                AlertLevel.Warning => "⚠️",
                AlertLevel.Error => "❌",
                AlertLevel.Critical => "🔴",
                _ => "❓"
            };

            Console.WriteLine($"   {levelIcon} {alert.RuleName}: {alert.Message}");
            Console.WriteLine($"      当前值: {alert.CurrentValue:F2}, 阈值: {alert.Threshold:F2}");
            Console.WriteLine($"      触发时间: {alert.FirstTriggeredAt:HH:mm:ss}");
        }
    }

    /// <summary>
    /// 导出性能指标
    /// </summary>
    private static async Task ExportMetrics(IMetricsManager metricsManager)
    {
        var endTime = DateTime.UtcNow;
        var startTime = endTime.AddMinutes(-5);

        try
        {
            // 导出 JSON 格式
            var jsonData = await metricsManager.ExportMetricsAsync(startTime, endTime, "json");
            Console.WriteLine($"📄 JSON 导出大小: {jsonData.Length} 字节");

            // 导出 CSV 格式
            var csvData = await metricsManager.ExportMetricsAsync(startTime, endTime, "csv");
            Console.WriteLine($"📊 CSV 导出大小: {csvData.Length} 字节");

            Console.WriteLine("✅ 指标导出完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 导出失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 显示统计信息
    /// </summary>
    private static async Task DisplayStatistics(IMetricsManager metricsManager, IAlertManager alertManager)
    {
        var storageStats = await metricsManager.GetStorageStatisticsAsync();
        var alertStats = await alertManager.GetStatisticsAsync();

        Console.WriteLine("📈 存储统计:");
        Console.WriteLine($"   总记录数: {storageStats.TotalRecords}");
        Console.WriteLine($"   存储大小: {storageStats.StorageSize / 1024:F1} KB");
        Console.WriteLine($"   平均写入延迟: {storageStats.AverageWriteLatency:F2}ms");
        Console.WriteLine($"   平均读取延迟: {storageStats.AverageReadLatency:F2}ms");

        Console.WriteLine("\n🚨 告警统计:");
        Console.WriteLine($"   活跃告警: {alertStats.ActiveAlerts}");
        Console.WriteLine($"   今日新增: {alertStats.TodayNewAlerts}");
        Console.WriteLine($"   今日解决: {alertStats.TodayResolvedAlerts}");
    }
}
