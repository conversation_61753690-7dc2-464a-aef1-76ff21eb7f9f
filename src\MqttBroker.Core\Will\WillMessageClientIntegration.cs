using Microsoft.Extensions.Logging;
using MqttBroker.Core.Client;
using MqttBroker.Core.Network;

namespace MqttBroker.Core.Will;

/// <summary>
/// 遗嘱消息与客户端管理器的集成
/// </summary>
public class WillMessageClientIntegration : IDisposable
{
    private readonly IWillMessageManager _willMessageManager;
    private readonly IMqttClientManager _clientManager;
    private readonly ILogger<WillMessageClientIntegration> _logger;
    private bool _disposed;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="willMessageManager">遗嘱消息管理器</param>
    /// <param name="clientManager">客户端管理器</param>
    /// <param name="logger">日志记录器</param>
    public WillMessageClientIntegration(
        IWillMessageManager willMessageManager,
        IMqttClientManager clientManager,
        ILogger<WillMessageClientIntegration> logger)
    {
        _willMessageManager = willMessageManager ?? throw new ArgumentNullException(nameof(willMessageManager));
        _clientManager = clientManager ?? throw new ArgumentNullException(nameof(clientManager));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // 订阅客户端事件
        _clientManager.ClientConnected += OnClientConnected;
        _clientManager.ClientDisconnected += OnClientDisconnected;

        _logger.LogInformation("WillMessageClientIntegration initialized");
    }

    /// <summary>
    /// 处理客户端连接事件
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private async void OnClientConnected(object? sender, MqttClientConnectedEventArgs e)
    {
        if (_disposed || e.Client?.WillMessage == null)
            return;

        try
        {
            _logger.LogDebug("Registering will message for connected client: {ClientId}", e.Client.ClientId);

            // 注册遗嘱消息
            var result = await _willMessageManager.RegisterWillMessageAsync(
                e.Client.ClientId, 
                e.Client.WillMessage);

            if (result.IsSuccess)
            {
                _logger.LogTrace("Will message registered successfully for client: {ClientId}", e.Client.ClientId);
            }
            else
            {
                _logger.LogWarning("Failed to register will message for client: {ClientId}, error: {Error}", 
                    e.Client.ClientId, result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error registering will message for client: {ClientId}", e.Client?.ClientId);
        }
    }

    /// <summary>
    /// 处理客户端断开连接事件
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private async void OnClientDisconnected(object? sender, MqttClientDisconnectedEventArgs e)
    {
        if (_disposed)
            return;

        try
        {
            var clientId = e.Client.ClientId;
            var disconnectionReason = e.Reason;

            _logger.LogDebug("Processing client disconnection for will message: {ClientId}, reason: {Reason}", 
                clientId, disconnectionReason);

            // 根据断开原因决定是触发还是清除遗嘱消息
            if (ShouldTriggerWillMessage(disconnectionReason))
            {
                var triggerCondition = MapDisconnectionReasonToTriggerCondition(disconnectionReason);
                
                _logger.LogDebug("Triggering will message for client: {ClientId}, condition: {Condition}", 
                    clientId, triggerCondition);

                var triggerResult = await _willMessageManager.TriggerWillMessageAsync(clientId, triggerCondition);

                if (triggerResult.IsSuccess && triggerResult.WillMessagePublished)
                {
                    _logger.LogInformation("Will message triggered and published for client: {ClientId}, topic: {Topic}", 
                        clientId, triggerResult.WillTopic);
                }
                else if (triggerResult.IsSuccess && !triggerResult.WillMessageExisted)
                {
                    _logger.LogTrace("No will message to trigger for client: {ClientId}", clientId);
                }
                else if (!triggerResult.IsSuccess)
                {
                    _logger.LogWarning("Failed to trigger will message for client: {ClientId}, error: {Error}", 
                        clientId, triggerResult.ErrorMessage);
                }
            }
            else
            {
                _logger.LogDebug("Clearing will message for normally disconnected client: {ClientId}", clientId);

                var clearResult = await _willMessageManager.ClearWillMessageAsync(clientId);

                if (clearResult.IsSuccess && clearResult.WillMessageExisted)
                {
                    _logger.LogTrace("Will message cleared for client: {ClientId}", clientId);
                }
                else if (!clearResult.IsSuccess)
                {
                    _logger.LogWarning("Failed to clear will message for client: {ClientId}, error: {Error}", 
                        clientId, clearResult.ErrorMessage);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing client disconnection for will message: {ClientId}", e.Client?.ClientId);
        }
    }

    /// <summary>
    /// 判断是否应该触发遗嘱消息
    /// </summary>
    /// <param name="disconnectionReason">断开连接原因</param>
    /// <returns>如果应该触发则返回true，否则返回false</returns>
    private static bool ShouldTriggerWillMessage(DisconnectionReason disconnectionReason)
    {
        return disconnectionReason switch
        {
            DisconnectionReason.ClientDisconnected => false, // 正常断开，不触发
            DisconnectionReason.ServerDisconnected => false, // 服务器主动断开，不触发
            DisconnectionReason.NetworkError => true,        // 网络错误，触发
            DisconnectionReason.ProtocolError => true,       // 协议错误，触发
            DisconnectionReason.AuthenticationFailed => true, // 认证失败，触发
            DisconnectionReason.Timeout => true,             // 超时，触发
            DisconnectionReason.ServerShutdown => true,      // 服务器关闭，触发
            _ => true // 默认触发
        };
    }

    /// <summary>
    /// 将断开连接原因映射到遗嘱消息触发条件
    /// </summary>
    /// <param name="disconnectionReason">断开连接原因</param>
    /// <returns>遗嘱消息触发条件</returns>
    private static WillMessageTriggerCondition MapDisconnectionReasonToTriggerCondition(DisconnectionReason disconnectionReason)
    {
        return disconnectionReason switch
        {
            DisconnectionReason.NetworkError => WillMessageTriggerCondition.NetworkFailure,
            DisconnectionReason.ProtocolError => WillMessageTriggerCondition.ProtocolError,
            DisconnectionReason.AuthenticationFailed => WillMessageTriggerCondition.AuthenticationFailure,
            DisconnectionReason.Timeout => WillMessageTriggerCondition.KeepAliveTimeout,
            DisconnectionReason.ServerShutdown => WillMessageTriggerCondition.ServerShutdown,
            _ => WillMessageTriggerCondition.UnexpectedDisconnection
        };
    }

    /// <summary>
    /// 手动触发客户端的遗嘱消息（用于测试或特殊情况）
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="triggerCondition">触发条件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>触发结果</returns>
    public async Task<WillMessageTriggerResult> TriggerWillMessageAsync(string clientId, 
        WillMessageTriggerCondition triggerCondition = WillMessageTriggerCondition.ManualTrigger, 
        CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(WillMessageClientIntegration));

        try
        {
            _logger.LogInformation("Manually triggering will message for client: {ClientId}, condition: {Condition}", 
                clientId, triggerCondition);

            return await _willMessageManager.TriggerWillMessageAsync(clientId, triggerCondition, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error manually triggering will message for client: {ClientId}", clientId);
            return WillMessageTriggerResult.Failure(clientId, triggerCondition, ex.Message);
        }
    }

    /// <summary>
    /// 手动清除客户端的遗嘱消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清除结果</returns>
    public async Task<WillMessageClearResult> ClearWillMessageAsync(string clientId, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(WillMessageClientIntegration));

        try
        {
            _logger.LogInformation("Manually clearing will message for client: {ClientId}", clientId);

            return await _willMessageManager.ClearWillMessageAsync(clientId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error manually clearing will message for client: {ClientId}", clientId);
            return WillMessageClearResult.Failure(clientId, ex.Message);
        }
    }

    /// <summary>
    /// 获取遗嘱消息管理器
    /// </summary>
    /// <returns>遗嘱消息管理器</returns>
    public IWillMessageManager GetWillMessageManager()
    {
        return _willMessageManager;
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        // 取消订阅客户端事件
        _clientManager.ClientConnected -= OnClientConnected;
        _clientManager.ClientDisconnected -= OnClientDisconnected;

        _logger.LogInformation("WillMessageClientIntegration disposed");
    }
}
