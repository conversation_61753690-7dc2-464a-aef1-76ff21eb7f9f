using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MqttBroker.Metrics.Configuration;
using MqttBroker.Metrics.Models;
using System.Collections.Concurrent;

namespace MqttBroker.Metrics.Collectors;

/// <summary>
/// 性能指标收集器实现
/// </summary>
public class MetricsCollector : IMetricsCollector, IDisposable
{
    private readonly IConnectionMetricsCollector _connectionCollector;
    private readonly IMessageMetricsCollector _messageCollector;
    private readonly ISubscriptionMetricsCollector _subscriptionCollector;
    private readonly IQoSMetricsCollector _qosCollector;
    private readonly ISystemMetricsCollector _systemCollector;
    private readonly INetworkMetricsCollector _networkCollector;
    private readonly ILogger<MetricsCollector> _logger;
    private readonly MetricsOptions _options;

    private Timer? _collectionTimer;
    private bool _isRunning;
    private readonly object _lockObject = new();

    /// <summary>
    /// 指标收集事件
    /// </summary>
    public event EventHandler<MetricsCollectedEventArgs>? MetricsCollected;

    /// <summary>
    /// 构造函数
    /// </summary>
    public MetricsCollector(
        IConnectionMetricsCollector connectionCollector,
        IMessageMetricsCollector messageCollector,
        ISubscriptionMetricsCollector subscriptionCollector,
        IQoSMetricsCollector qosCollector,
        ISystemMetricsCollector systemCollector,
        INetworkMetricsCollector networkCollector,
        ILogger<MetricsCollector> logger,
        IOptions<MetricsOptions> options)
    {
        _connectionCollector = connectionCollector ?? throw new ArgumentNullException(nameof(connectionCollector));
        _messageCollector = messageCollector ?? throw new ArgumentNullException(nameof(messageCollector));
        _subscriptionCollector = subscriptionCollector ?? throw new ArgumentNullException(nameof(subscriptionCollector));
        _qosCollector = qosCollector ?? throw new ArgumentNullException(nameof(qosCollector));
        _systemCollector = systemCollector ?? throw new ArgumentNullException(nameof(systemCollector));
        _networkCollector = networkCollector ?? throw new ArgumentNullException(nameof(networkCollector));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
    }

    /// <summary>
    /// 收集当前性能指标
    /// </summary>
    public Task<PerformanceMetrics> CollectMetricsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var metrics = new PerformanceMetrics
            {
                Timestamp = DateTime.UtcNow,
                Connection = _connectionCollector.CollectConnectionMetrics(),
                Message = _messageCollector.CollectMessageMetrics(),
                Subscription = _subscriptionCollector.CollectSubscriptionMetrics(),
                QoS = _qosCollector.CollectQoSMetrics()
            };

            // 根据配置决定是否收集系统和网络指标
            if (_options.EnableSystemMetrics)
            {
                metrics.System = _systemCollector.CollectSystemMetrics();
            }

            if (_options.EnableNetworkMetrics)
            {
                metrics.Network = _networkCollector.CollectNetworkMetrics();
            }

            _logger.LogDebug("性能指标收集完成，时间戳: {Timestamp}", metrics.Timestamp);
            return Task.FromResult(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "收集性能指标时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 开始指标收集
    /// </summary>
    public Task StartAsync(CancellationToken cancellationToken = default)
    {
        lock (_lockObject)
        {
            if (_isRunning)
            {
                _logger.LogWarning("指标收集器已经在运行中");
                return Task.CompletedTask;
            }

            if (!_options.EnableMetrics)
            {
                _logger.LogInformation("性能监控已禁用，跳过指标收集");
                return Task.CompletedTask;
            }

            _logger.LogInformation("启动性能指标收集器，收集间隔: {IntervalMs}ms", _options.CollectionIntervalMs);

            _collectionTimer = new Timer(
                CollectMetricsCallback,
                null,
                TimeSpan.Zero,
                TimeSpan.FromMilliseconds(_options.CollectionIntervalMs));

            _isRunning = true;
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 停止指标收集
    /// </summary>
    public Task StopAsync(CancellationToken cancellationToken = default)
    {
        lock (_lockObject)
        {
            if (!_isRunning)
            {
                return Task.CompletedTask;
            }

            _logger.LogInformation("停止性能指标收集器");

            _collectionTimer?.Dispose();
            _collectionTimer = null;
            _isRunning = false;
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 指标收集回调
    /// </summary>
    private async void CollectMetricsCallback(object? state)
    {
        try
        {
            var metrics = await CollectMetricsAsync();
            MetricsCollected?.Invoke(this, new MetricsCollectedEventArgs(metrics));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "定时收集性能指标时发生错误");
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        StopAsync().GetAwaiter().GetResult();
        GC.SuppressFinalize(this);
    }
}

/// <summary>
/// 连接指标收集器实现
/// </summary>
public class ConnectionMetricsCollector : IConnectionMetricsCollector
{
    private readonly ConcurrentDictionary<string, DateTime> _activeConnections = new();
    private readonly object _lockObject = new();

    private long _totalConnections;
    private long _peakConnections;
    private long _authenticationFailures;
    private long _connectionTimeouts;
    private double _totalConnectionDuration;
    private long _connectionCount;

    // 速率计算相关
    private DateTime _lastRateCalculation = DateTime.UtcNow;
    private long _lastConnectionCount;
    private long _lastDisconnectionCount;
    private long _disconnectionCount;

    /// <summary>
    /// 收集连接指标
    /// </summary>
    public ConnectionMetrics CollectConnectionMetrics()
    {
        lock (_lockObject)
        {
            var now = DateTime.UtcNow;
            var timeDiff = (now - _lastRateCalculation).TotalSeconds;

            var connectionRate = timeDiff > 0 ? (_totalConnections - _lastConnectionCount) / timeDiff : 0;
            var disconnectionRate = timeDiff > 0 ? (_disconnectionCount - _lastDisconnectionCount) / timeDiff : 0;

            _lastRateCalculation = now;
            _lastConnectionCount = _totalConnections;
            _lastDisconnectionCount = _disconnectionCount;

            return new ConnectionMetrics
            {
                ActiveConnections = _activeConnections.Count,
                PeakConnections = _peakConnections,
                TotalConnections = _totalConnections,
                ConnectionRate = connectionRate,
                DisconnectionRate = disconnectionRate,
                AverageConnectionDuration = _connectionCount > 0 ? _totalConnectionDuration / _connectionCount : 0,
                AuthenticationFailures = _authenticationFailures,
                ConnectionTimeouts = _connectionTimeouts
            };
        }
    }

    /// <summary>
    /// 记录新连接
    /// </summary>
    public void RecordConnection(string clientId)
    {
        _activeConnections[clientId] = DateTime.UtcNow;
        
        lock (_lockObject)
        {
            Interlocked.Increment(ref _totalConnections);
            var currentActive = _activeConnections.Count;
            if (currentActive > _peakConnections)
            {
                _peakConnections = currentActive;
            }
        }
    }

    /// <summary>
    /// 记录连接断开
    /// </summary>
    public void RecordDisconnection(string clientId, TimeSpan duration)
    {
        _activeConnections.TryRemove(clientId, out _);
        
        lock (_lockObject)
        {
            Interlocked.Increment(ref _disconnectionCount);
            _totalConnectionDuration += duration.TotalSeconds;
            Interlocked.Increment(ref _connectionCount);
        }
    }

    /// <summary>
    /// 记录认证失败
    /// </summary>
    public void RecordAuthenticationFailure()
    {
        Interlocked.Increment(ref _authenticationFailures);
    }

    /// <summary>
    /// 记录连接超时
    /// </summary>
    public void RecordConnectionTimeout()
    {
        Interlocked.Increment(ref _connectionTimeouts);
    }
}
