using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Message;
using System.Diagnostics;

namespace MqttBroker.Core.Will;

/// <summary>
/// 遗嘱消息管理器实现
/// </summary>
public class WillMessageManager : IWillMessageManager
{
    private readonly IWillMessageStorage _storage;
    private readonly IMessageRoutingEngine _routingEngine;
    private readonly ILogger<WillMessageManager> _logger;
    private readonly WillMessageOptions _options;
    private readonly object _statsLock = new();
    private readonly DateTime _startTime;

    // 统计信息
    private long _totalRegistrations;
    private long _totalTriggers;
    private long _totalClears;
    private long _totalFailures;
    private DateTime _lastOperationTime;
    private readonly List<long> _processingTimes = new();
    private bool _disposed;

    /// <summary>
    /// 遗嘱消息注册事件
    /// </summary>
    public event EventHandler<WillMessageRegisteredEventArgs>? WillMessageRegistered;

    /// <summary>
    /// 遗嘱消息触发事件
    /// </summary>
    public event EventHandler<WillMessageTriggeredEventArgs>? WillMessageTriggered;

    /// <summary>
    /// 遗嘱消息清除事件
    /// </summary>
    public event EventHandler<WillMessageClearedEventArgs>? WillMessageCleared;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="storage">遗嘱消息存储</param>
    /// <param name="routingEngine">消息路由引擎</param>
    /// <param name="options">配置选项</param>
    /// <param name="logger">日志记录器</param>
    public WillMessageManager(
        IWillMessageStorage storage,
        IMessageRoutingEngine routingEngine,
        IOptions<WillMessageOptions> options,
        ILogger<WillMessageManager> logger)
    {
        _storage = storage ?? throw new ArgumentNullException(nameof(storage));
        _routingEngine = routingEngine ?? throw new ArgumentNullException(nameof(routingEngine));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _startTime = DateTime.UtcNow;

        _logger.LogInformation("WillMessageManager initialized with options: MaxWillMessages={MaxWillMessages}, ExpirationHours={ExpirationHours}", 
            _options.MaxWillMessagesPerClient, _options.WillMessageExpirationHours);
    }

    /// <summary>
    /// 注册客户端遗嘱消息
    /// </summary>
    public async Task<WillMessageRegistrationResult> RegisterWillMessageAsync(string clientId, MqttWillMessage willMessage, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(WillMessageManager));

        var stopwatch = Stopwatch.StartNew();

        try
        {
            if (string.IsNullOrEmpty(clientId))
                throw new ArgumentException("Client ID cannot be null or empty", nameof(clientId));

            if (willMessage == null)
                throw new ArgumentNullException(nameof(willMessage));

            if (string.IsNullOrEmpty(willMessage.Topic))
                throw new ArgumentException("Will message topic cannot be null or empty");

            _logger.LogDebug("Registering will message for client: {ClientId}, topic: {Topic}", clientId, willMessage.Topic);

            // 验证遗嘱消息
            var validationResult = ValidateWillMessage(willMessage);
            if (!validationResult.IsValid)
            {
                stopwatch.Stop();
                UpdateStatistics(false, stopwatch.ElapsedMilliseconds);

                var errorResult = WillMessageRegistrationResult.Failure(clientId, validationResult.ErrorMessage!);
                WillMessageRegistered?.Invoke(this, new WillMessageRegisteredEventArgs(
                    WillMessageRegistration.FromMqttWillMessage(clientId, willMessage, _options.DefaultProtocolVersion), errorResult));

                return errorResult;
            }

            // 创建遗嘱消息注册信息
            var registration = WillMessageRegistration.FromMqttWillMessage(clientId, willMessage, _options.DefaultProtocolVersion);
            
            // 设置过期时间
            if (_options.WillMessageExpirationHours > 0)
            {
                registration.ExpiresAt = DateTime.UtcNow.AddHours(_options.WillMessageExpirationHours);
            }

            // 存储遗嘱消息
            var storageResult = await _storage.StoreWillMessageAsync(registration, cancellationToken);
            if (!storageResult.IsSuccess)
            {
                stopwatch.Stop();
                UpdateStatistics(false, stopwatch.ElapsedMilliseconds);

                var errorResult = WillMessageRegistrationResult.Failure(clientId, storageResult.ErrorMessage ?? "Storage failed");
                WillMessageRegistered?.Invoke(this, new WillMessageRegisteredEventArgs(registration, errorResult));

                return errorResult;
            }

            stopwatch.Stop();
            UpdateStatistics(true, stopwatch.ElapsedMilliseconds, isRegistration: true);

            var successResult = WillMessageRegistrationResult.Success(clientId, registration.RegisteredAt);
            WillMessageRegistered?.Invoke(this, new WillMessageRegisteredEventArgs(registration, successResult));

            _logger.LogInformation("Will message registered successfully for client: {ClientId}, topic: {Topic}, QoS: {QoS}", 
                clientId, willMessage.Topic, willMessage.QoSLevel);

            return successResult;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            UpdateStatistics(false, stopwatch.ElapsedMilliseconds);

            _logger.LogError(ex, "Error registering will message for client: {ClientId}", clientId);
            
            var errorResult = WillMessageRegistrationResult.Failure(clientId, ex.Message);
            WillMessageRegistered?.Invoke(this, new WillMessageRegisteredEventArgs(
                WillMessageRegistration.FromMqttWillMessage(clientId, willMessage ?? new MqttWillMessage(), _options.DefaultProtocolVersion), 
                errorResult));

            return errorResult;
        }
    }

    /// <summary>
    /// 清除客户端遗嘱消息（正常断开连接时调用）
    /// </summary>
    public async Task<WillMessageClearResult> ClearWillMessageAsync(string clientId, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(WillMessageManager));

        var stopwatch = Stopwatch.StartNew();

        try
        {
            if (string.IsNullOrEmpty(clientId))
                throw new ArgumentException("Client ID cannot be null or empty", nameof(clientId));

            _logger.LogDebug("Clearing will message for client: {ClientId}", clientId);

            // 获取现有的遗嘱消息
            var existingRegistration = await _storage.GetWillMessageAsync(clientId, cancellationToken);
            var willMessageExisted = existingRegistration != null;

            // 删除遗嘱消息
            var storageResult = await _storage.DeleteWillMessageAsync(clientId, cancellationToken);
            if (!storageResult.IsSuccess)
            {
                stopwatch.Stop();
                UpdateStatistics(false, stopwatch.ElapsedMilliseconds);

                var errorResult = WillMessageClearResult.Failure(clientId, storageResult.ErrorMessage ?? "Storage deletion failed");
                WillMessageCleared?.Invoke(this, new WillMessageClearedEventArgs(existingRegistration, errorResult));

                return errorResult;
            }

            stopwatch.Stop();
            UpdateStatistics(true, stopwatch.ElapsedMilliseconds, isClear: true);

            var successResult = WillMessageClearResult.Success(clientId, willMessageExisted, DateTime.UtcNow);
            WillMessageCleared?.Invoke(this, new WillMessageClearedEventArgs(existingRegistration, successResult));

            if (willMessageExisted)
            {
                _logger.LogInformation("Will message cleared for client: {ClientId}", clientId);
            }
            else
            {
                _logger.LogTrace("No will message to clear for client: {ClientId}", clientId);
            }

            return successResult;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            UpdateStatistics(false, stopwatch.ElapsedMilliseconds);

            _logger.LogError(ex, "Error clearing will message for client: {ClientId}", clientId);
            
            var errorResult = WillMessageClearResult.Failure(clientId, ex.Message);
            WillMessageCleared?.Invoke(this, new WillMessageClearedEventArgs(null, errorResult));

            return errorResult;
        }
    }

    /// <summary>
    /// 触发遗嘱消息发布（异常断开连接时调用）
    /// </summary>
    public async Task<WillMessageTriggerResult> TriggerWillMessageAsync(string clientId, WillMessageTriggerCondition triggerCondition, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(WillMessageManager));

        var stopwatch = Stopwatch.StartNew();

        try
        {
            if (string.IsNullOrEmpty(clientId))
                throw new ArgumentException("Client ID cannot be null or empty", nameof(clientId));

            _logger.LogDebug("Triggering will message for client: {ClientId}, condition: {Condition}", clientId, triggerCondition);

            // 获取遗嘱消息
            var registration = await _storage.GetWillMessageAsync(clientId, cancellationToken);
            if (registration == null)
            {
                stopwatch.Stop();
                UpdateStatistics(true, stopwatch.ElapsedMilliseconds, isTrigger: true);

                var noWillResult = WillMessageTriggerResult.Success(clientId, triggerCondition, false, false, null, stopwatch.ElapsedMilliseconds);
                WillMessageTriggered?.Invoke(this, new WillMessageTriggeredEventArgs(null, noWillResult));

                _logger.LogTrace("No will message to trigger for client: {ClientId}", clientId);
                return noWillResult;
            }

            // 检查是否已经触发过
            if (registration.IsTriggered)
            {
                stopwatch.Stop();
                UpdateStatistics(true, stopwatch.ElapsedMilliseconds, isTrigger: true);

                var alreadyTriggeredResult = WillMessageTriggerResult.Success(clientId, triggerCondition, true, false, registration.Topic, stopwatch.ElapsedMilliseconds);
                WillMessageTriggered?.Invoke(this, new WillMessageTriggeredEventArgs(registration, alreadyTriggeredResult));

                _logger.LogTrace("Will message already triggered for client: {ClientId}", clientId);
                return alreadyTriggeredResult;
            }

            // 检查是否过期
            if (registration.IsExpired())
            {
                stopwatch.Stop();
                UpdateStatistics(true, stopwatch.ElapsedMilliseconds, isTrigger: true);

                // 删除过期的遗嘱消息
                await _storage.DeleteWillMessageAsync(clientId, cancellationToken);

                var expiredResult = WillMessageTriggerResult.Success(clientId, triggerCondition, true, false, registration.Topic, stopwatch.ElapsedMilliseconds);
                WillMessageTriggered?.Invoke(this, new WillMessageTriggeredEventArgs(registration, expiredResult));

                _logger.LogWarning("Will message expired for client: {ClientId}, topic: {Topic}", clientId, registration.Topic);
                return expiredResult;
            }

            // 发布遗嘱消息
            var publishPacket = registration.ToPublishPacket();
            var routingResult = await _routingEngine.RouteMessageAsync(publishPacket, clientId, cancellationToken);

            // 标记为已触发
            registration.MarkAsTriggered(triggerCondition);
            await _storage.StoreWillMessageAsync(registration, cancellationToken);

            stopwatch.Stop();
            UpdateStatistics(routingResult.IsSuccess, stopwatch.ElapsedMilliseconds, isTrigger: true);

            var triggerResult = WillMessageTriggerResult.Success(clientId, triggerCondition, true, routingResult.IsSuccess, registration.Topic, stopwatch.ElapsedMilliseconds);
            WillMessageTriggered?.Invoke(this, new WillMessageTriggeredEventArgs(registration, triggerResult));

            if (routingResult.IsSuccess)
            {
                _logger.LogInformation("Will message triggered and published for client: {ClientId}, topic: {Topic}, condition: {Condition}", 
                    clientId, registration.Topic, triggerCondition);
            }
            else
            {
                _logger.LogWarning("Will message triggered but failed to publish for client: {ClientId}, topic: {Topic}, condition: {Condition}", 
                    clientId, registration.Topic, triggerCondition);
            }

            return triggerResult;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            UpdateStatistics(false, stopwatch.ElapsedMilliseconds);

            _logger.LogError(ex, "Error triggering will message for client: {ClientId}, condition: {Condition}", clientId, triggerCondition);
            
            var errorResult = WillMessageTriggerResult.Failure(clientId, triggerCondition, ex.Message);
            WillMessageTriggered?.Invoke(this, new WillMessageTriggeredEventArgs(null, errorResult));

            return errorResult;
        }
    }

    /// <summary>
    /// 获取客户端遗嘱消息
    /// </summary>
    public async Task<WillMessageRegistration?> GetWillMessageAsync(string clientId, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(WillMessageManager));

        try
        {
            if (string.IsNullOrEmpty(clientId))
                return null;

            return await _storage.GetWillMessageAsync(clientId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting will message for client: {ClientId}", clientId);
            return null;
        }
    }

    /// <summary>
    /// 检查客户端是否有遗嘱消息
    /// </summary>
    public async Task<bool> HasWillMessageAsync(string clientId)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(WillMessageManager));

        try
        {
            if (string.IsNullOrEmpty(clientId))
                return false;

            return await _storage.ExistsAsync(clientId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking will message existence for client: {ClientId}", clientId);
            return false;
        }
    }

    /// <summary>
    /// 获取所有遗嘱消息注册信息
    /// </summary>
    public async Task<IList<WillMessageRegistration>> GetAllWillMessagesAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(WillMessageManager));

        try
        {
            return await _storage.GetAllWillMessagesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all will messages");
            return new List<WillMessageRegistration>();
        }
    }

    /// <summary>
    /// 清理过期的遗嘱消息
    /// </summary>
    public async Task<WillMessageCleanupResult> CleanupExpiredWillMessagesAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(WillMessageManager));

        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogDebug("Starting cleanup of expired will messages");

            var expirationTime = DateTime.UtcNow;
            var expiredWillMessages = await _storage.GetExpiredWillMessagesAsync(expirationTime, cancellationToken);

            if (expiredWillMessages.Count == 0)
            {
                stopwatch.Stop();
                _logger.LogTrace("No expired will messages found");
                return WillMessageCleanupResult.Success(0, new List<string>(), stopwatch.ElapsedMilliseconds);
            }

            var clientIds = expiredWillMessages.Select(w => w.ClientId).ToList();
            var batchResult = await _storage.DeleteWillMessagesAsync(clientIds, cancellationToken);

            stopwatch.Stop();

            if (batchResult.IsSuccess)
            {
                _logger.LogInformation("Cleaned up {Count} expired will messages", batchResult.SuccessfulClientIds.Count);
                return WillMessageCleanupResult.Success(batchResult.SuccessfulClientIds.Count, batchResult.SuccessfulClientIds, stopwatch.ElapsedMilliseconds);
            }
            else
            {
                _logger.LogWarning("Partial cleanup of expired will messages: {SuccessfulCount} successful, {FailedCount} failed",
                    batchResult.SuccessfulClientIds.Count, batchResult.FailedClientIds.Count);
                return WillMessageCleanupResult.Success(batchResult.SuccessfulClientIds.Count, batchResult.SuccessfulClientIds, stopwatch.ElapsedMilliseconds);
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error cleaning up expired will messages");
            return WillMessageCleanupResult.Failure(ex.Message);
        }
    }

    /// <summary>
    /// 获取遗嘱消息管理器统计信息
    /// </summary>
    public async Task<WillMessageStatistics> GetStatisticsAsync()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(WillMessageManager));

        try
        {
            var storageStats = await _storage.GetStatisticsAsync();
            var allWillMessages = await _storage.GetAllWillMessagesAsync();

            lock (_statsLock)
            {
                var statistics = new WillMessageStatistics
                {
                    TotalWillMessages = storageStats.TotalWillMessages,
                    ActiveWillMessages = allWillMessages.Count(w => !w.IsTriggered && !w.IsExpired()),
                    TriggeredWillMessages = allWillMessages.Count(w => w.IsTriggered),
                    ExpiredWillMessages = allWillMessages.Count(w => w.IsExpired()),
                    TotalRegistrations = _totalRegistrations,
                    TotalTriggers = _totalTriggers,
                    TotalClears = _totalClears,
                    TotalFailures = _totalFailures,
                    AverageProcessingTimeMs = _processingTimes.Count > 0 ? _processingTimes.Average() : 0,
                    LastOperationTime = _lastOperationTime,
                    StartTime = _startTime
                };

                // 按触发条件分组统计
                var triggerGroups = allWillMessages.Where(w => w.TriggerCondition.HasValue).GroupBy(w => w.TriggerCondition!.Value);
                foreach (var group in triggerGroups)
                {
                    statistics.TriggersByCondition[group.Key] = group.Count();
                }

                // 按QoS级别分组统计
                var qosGroups = allWillMessages.GroupBy(w => w.QoSLevel);
                foreach (var group in qosGroups)
                {
                    statistics.WillMessagesByQoS[group.Key] = group.Count();
                }

                // 按主题分组统计
                var topicGroups = allWillMessages.GroupBy(w => w.Topic);
                foreach (var group in topicGroups)
                {
                    statistics.WillMessagesByTopic[group.Key] = group.Count();
                }

                // 按协议版本分组统计
                var protocolGroups = allWillMessages.GroupBy(w => w.ProtocolVersion);
                foreach (var group in protocolGroups)
                {
                    statistics.WillMessagesByProtocolVersion[group.Key] = group.Count();
                }

                return statistics;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting will message statistics");
            return new WillMessageStatistics
            {
                StartTime = _startTime,
                LastOperationTime = _lastOperationTime
            };
        }
    }

    /// <summary>
    /// 验证遗嘱消息
    /// </summary>
    private WillMessageValidationResult ValidateWillMessage(MqttWillMessage willMessage)
    {
        if (string.IsNullOrEmpty(willMessage.Topic))
        {
            return WillMessageValidationResult.Invalid("Will message topic cannot be null or empty");
        }

        if (willMessage.Topic.Length > _options.MaxTopicLength)
        {
            return WillMessageValidationResult.Invalid($"Will message topic length exceeds maximum allowed length of {_options.MaxTopicLength}");
        }

        if (willMessage.Payload.Length > _options.MaxPayloadSize)
        {
            return WillMessageValidationResult.Invalid($"Will message payload size exceeds maximum allowed size of {_options.MaxPayloadSize} bytes");
        }

        if (_options.AllowedQoSLevels != null && !_options.AllowedQoSLevels.Contains(willMessage.QoSLevel))
        {
            return WillMessageValidationResult.Invalid($"Will message QoS level {willMessage.QoSLevel} is not allowed");
        }

        if (_options.ForbiddenTopicPatterns != null)
        {
            foreach (var pattern in _options.ForbiddenTopicPatterns)
            {
                if (willMessage.Topic.Contains(pattern, StringComparison.OrdinalIgnoreCase))
                {
                    return WillMessageValidationResult.Invalid($"Will message topic contains forbidden pattern: {pattern}");
                }
            }
        }

        return WillMessageValidationResult.Valid();
    }

    /// <summary>
    /// 更新统计信息
    /// </summary>
    private void UpdateStatistics(bool isSuccess, long processingTimeMs, bool isRegistration = false, bool isTrigger = false, bool isClear = false)
    {
        lock (_statsLock)
        {
            _lastOperationTime = DateTime.UtcNow;

            if (isRegistration)
                _totalRegistrations++;
            else if (isTrigger)
                _totalTriggers++;
            else if (isClear)
                _totalClears++;

            if (!isSuccess)
                _totalFailures++;

            if (processingTimeMs > 0)
            {
                _processingTimes.Add(processingTimeMs);

                // 保持最近1000个处理时间记录
                if (_processingTimes.Count > 1000)
                {
                    _processingTimes.RemoveAt(0);
                }
            }
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;
        _storage?.Dispose();

        _logger.LogInformation("WillMessageManager disposed");
    }
}

/// <summary>
/// 遗嘱消息验证结果
/// </summary>
internal class WillMessageValidationResult
{
    public bool IsValid { get; private set; }
    public string? ErrorMessage { get; private set; }

    private WillMessageValidationResult(bool isValid, string? errorMessage = null)
    {
        IsValid = isValid;
        ErrorMessage = errorMessage;
    }

    public static WillMessageValidationResult Valid() => new(true);
    public static WillMessageValidationResult Invalid(string errorMessage) => new(false, errorMessage);
}
