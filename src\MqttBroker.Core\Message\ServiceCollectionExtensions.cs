using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using MqttBroker.Core.Message.Filters;

namespace MqttBroker.Core.Message;

/// <summary>
/// 消息路由引擎服务注册扩展
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加消息路由引擎服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureRouting">路由配置</param>
    /// <param name="configureFilters">过滤器配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddMessageRoutingEngine(
        this IServiceCollection services,
        Action<MessageRoutingOptions>? configureRouting = null,
        Action<MessageFilterConfiguration>? configureFilters = null)
    {
        // 注册核心服务
        services.TryAddSingleton<IMessageRoutingEngine, MessageRoutingEngine>();
        services.TryAddSingleton<IMessageFilterManager, MessageFilterManager>();
        services.TryAddSingleton<IMessagePersistenceService, InMemoryMessagePersistenceService>();
        services.TryAddSingleton<IDeadLetterQueueService, InMemoryDeadLetterQueueService>();

        // 注册数据包处理器
        services.AddMessagePacketHandler<MqttPublishPacketHandler>();
        services.AddMessagePacketHandler<MqttPubRecPacketHandler>();
        services.AddMessagePacketHandler<MqttPubRelPacketHandler>();

        // 配置路由选项
        if (configureRouting != null)
        {
            services.Configure(configureRouting);
        }

        // 配置内置过滤器
        var filterConfig = new MessageFilterConfiguration();
        configureFilters?.Invoke(filterConfig);
        
        if (filterConfig.EnableMessageSizeFilter)
        {
            services.TryAddSingleton<MessageSizeFilter>();
            services.Configure<MessageSizeFilterOptions>(options =>
            {
                options.MaxMessageSize = filterConfig.MaxMessageSize;
            });
        }

        if (filterConfig.EnableTopicBlacklistFilter)
        {
            services.TryAddSingleton<TopicBlacklistFilter>();
            services.Configure<TopicBlacklistFilterOptions>(options =>
            {
                options.BlacklistedTopics = filterConfig.BlacklistedTopics;
                options.BlacklistedPatterns = filterConfig.BlacklistedPatterns;
            });
        }

        if (filterConfig.EnableContentValidationFilter)
        {
            services.TryAddSingleton<ContentValidationFilter>();
            services.Configure<ContentValidationFilterOptions>(options =>
            {
                options.ValidateContent = filterConfig.ValidateContent;
                options.AllowEmptyMessages = filterConfig.AllowEmptyMessages;
                options.RequireValidJson = filterConfig.RequireValidJson;
                options.ForbiddenWords = filterConfig.ForbiddenWords;
            });
        }

        if (filterConfig.EnableRateLimitFilter)
        {
            services.TryAddSingleton<RateLimitFilter>();
            services.Configure<RateLimitFilterOptions>(options =>
            {
                options.EnableRateLimit = filterConfig.EnableRateLimit;
                options.WindowSizeSeconds = filterConfig.WindowSizeSeconds;
                options.MaxMessagesPerWindow = filterConfig.MaxMessagesPerWindow;
            });
        }

        return services;
    }

    /// <summary>
    /// 添加自定义消息过滤器
    /// </summary>
    /// <typeparam name="TFilter">过滤器类型</typeparam>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddMessageFilter<TFilter>(this IServiceCollection services)
        where TFilter : class, IMessageFilter
    {
        services.TryAddSingleton<TFilter>();
        return services;
    }

    /// <summary>
    /// 添加消息数据包处理器
    /// </summary>
    /// <typeparam name="THandler">处理器类型</typeparam>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddMessagePacketHandler<THandler>(this IServiceCollection services)
        where THandler : class, IMessagePacketHandler
    {
        services.TryAddSingleton<IMessagePacketHandler, THandler>();
        return services;
    }

    /// <summary>
    /// 配置消息路由引擎（带有完整配置）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureRouting">路由配置</param>
    /// <param name="configureSizeFilter">消息大小过滤器配置</param>
    /// <param name="configureBlacklistFilter">黑名单过滤器配置</param>
    /// <param name="configureContentFilter">内容验证过滤器配置</param>
    /// <param name="configureRateLimitFilter">频率限制过滤器配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection ConfigureMessageRoutingEngine(
        this IServiceCollection services,
        Action<MessageRoutingOptions>? configureRouting = null,
        Action<MessageSizeFilterOptions>? configureSizeFilter = null,
        Action<TopicBlacklistFilterOptions>? configureBlacklistFilter = null,
        Action<ContentValidationFilterOptions>? configureContentFilter = null,
        Action<RateLimitFilterOptions>? configureRateLimitFilter = null)
    {
        if (configureRouting != null)
        {
            services.Configure(configureRouting);
        }

        if (configureSizeFilter != null)
        {
            services.Configure(configureSizeFilter);
        }

        if (configureBlacklistFilter != null)
        {
            services.Configure(configureBlacklistFilter);
        }

        if (configureContentFilter != null)
        {
            services.Configure(configureContentFilter);
        }

        if (configureRateLimitFilter != null)
        {
            services.Configure(configureRateLimitFilter);
        }

        return services;
    }
}

/// <summary>
/// 消息过滤器配置
/// </summary>
public class MessageFilterConfiguration
{
    /// <summary>
    /// 是否启用消息大小过滤器
    /// </summary>
    public bool EnableMessageSizeFilter { get; set; } = true;

    /// <summary>
    /// 最大消息大小（字节）
    /// </summary>
    public int MaxMessageSize { get; set; } = 1024 * 1024; // 1MB

    /// <summary>
    /// 是否启用主题黑名单过滤器
    /// </summary>
    public bool EnableTopicBlacklistFilter { get; set; } = false;

    /// <summary>
    /// 黑名单主题列表
    /// </summary>
    public HashSet<string> BlacklistedTopics { get; set; } = new();

    /// <summary>
    /// 黑名单模式列表
    /// </summary>
    public List<string> BlacklistedPatterns { get; set; } = new();

    /// <summary>
    /// 是否启用内容验证过滤器
    /// </summary>
    public bool EnableContentValidationFilter { get; set; } = false;

    /// <summary>
    /// 是否验证内容
    /// </summary>
    public bool ValidateContent { get; set; } = true;

    /// <summary>
    /// 是否允许空消息
    /// </summary>
    public bool AllowEmptyMessages { get; set; } = true;

    /// <summary>
    /// 是否要求有效的JSON格式
    /// </summary>
    public bool RequireValidJson { get; set; } = false;

    /// <summary>
    /// 禁用词汇列表
    /// </summary>
    public List<string> ForbiddenWords { get; set; } = new();

    /// <summary>
    /// 是否启用频率限制过滤器
    /// </summary>
    public bool EnableRateLimitFilter { get; set; } = true;

    /// <summary>
    /// 是否启用频率限制
    /// </summary>
    public bool EnableRateLimit { get; set; } = true;

    /// <summary>
    /// 时间窗口大小（秒）
    /// </summary>
    public int WindowSizeSeconds { get; set; } = 60;

    /// <summary>
    /// 每个时间窗口内的最大消息数
    /// </summary>
    public int MaxMessagesPerWindow { get; set; } = 1000;
}
