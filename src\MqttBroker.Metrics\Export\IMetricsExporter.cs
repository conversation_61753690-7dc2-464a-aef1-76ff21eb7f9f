using MqttBroker.Metrics.Models;
using MqttBroker.Metrics.Storage;

namespace MqttBroker.Metrics.Export;

/// <summary>
/// 指标导出器接口
/// </summary>
public interface IMetricsExporter
{
    /// <summary>
    /// 导出格式
    /// </summary>
    string Format { get; }

    /// <summary>
    /// 导出当前指标
    /// </summary>
    /// <param name="metrics">性能指标</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>导出的数据</returns>
    Task<string> ExportCurrentMetricsAsync(PerformanceMetrics metrics, CancellationToken cancellationToken = default);

    /// <summary>
    /// 导出历史指标
    /// </summary>
    /// <param name="metricsList">性能指标列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>导出的数据</returns>
    Task<string> ExportHistoricalMetricsAsync(IEnumerable<PerformanceMetrics> metricsList, CancellationToken cancellationToken = default);

    /// <summary>
    /// 导出聚合指标
    /// </summary>
    /// <param name="aggregatedMetrics">聚合指标列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>导出的数据</returns>
    Task<string> ExportAggregatedMetricsAsync(IEnumerable<AggregatedMetrics> aggregatedMetrics, CancellationToken cancellationToken = default);
}

/// <summary>
/// 指标导出管理器接口
/// </summary>
public interface IMetricsExportManager
{
    /// <summary>
    /// 注册导出器
    /// </summary>
    /// <param name="exporter">导出器</param>
    void RegisterExporter(IMetricsExporter exporter);

    /// <summary>
    /// 获取支持的导出格式
    /// </summary>
    /// <returns>支持的格式列表</returns>
    IEnumerable<string> GetSupportedFormats();

    /// <summary>
    /// 导出当前指标
    /// </summary>
    /// <param name="metrics">性能指标</param>
    /// <param name="format">导出格式</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>导出的数据</returns>
    Task<string> ExportCurrentMetricsAsync(PerformanceMetrics metrics, string format, CancellationToken cancellationToken = default);

    /// <summary>
    /// 导出历史指标
    /// </summary>
    /// <param name="metricsList">性能指标列表</param>
    /// <param name="format">导出格式</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>导出的数据</returns>
    Task<string> ExportHistoricalMetricsAsync(IEnumerable<PerformanceMetrics> metricsList, string format, CancellationToken cancellationToken = default);

    /// <summary>
    /// 导出聚合指标
    /// </summary>
    /// <param name="aggregatedMetrics">聚合指标列表</param>
    /// <param name="format">导出格式</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>导出的数据</returns>
    Task<string> ExportAggregatedMetricsAsync(IEnumerable<AggregatedMetrics> aggregatedMetrics, string format, CancellationToken cancellationToken = default);

    /// <summary>
    /// 导出到文件
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="filePath">文件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task ExportToFileAsync(string data, string filePath, CancellationToken cancellationToken = default);
}

/// <summary>
/// REST API 导出服务接口
/// </summary>
public interface IMetricsRestApiService
{
    /// <summary>
    /// 启动 REST API 服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止 REST API 服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取当前指标
    /// </summary>
    /// <param name="format">导出格式</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>指标数据</returns>
    Task<string> GetCurrentMetricsAsync(string format = "json", CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取历史指标
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="format">导出格式</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>指标数据</returns>
    Task<string> GetHistoricalMetricsAsync(DateTime startTime, DateTime endTime, string format = "json", CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取聚合指标
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="interval">聚合间隔（秒）</param>
    /// <param name="format">导出格式</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>聚合指标数据</returns>
    Task<string> GetAggregatedMetricsAsync(DateTime startTime, DateTime endTime, int interval = 60, string format = "json", CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取活跃告警
    /// </summary>
    /// <param name="format">导出格式</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>告警数据</returns>
    Task<string> GetActiveAlertsAsync(string format = "json", CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取告警历史
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="format">导出格式</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>告警历史数据</returns>
    Task<string> GetAlertHistoryAsync(DateTime startTime, DateTime endTime, string format = "json", CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取系统状态
    /// </summary>
    /// <param name="format">导出格式</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>系统状态数据</returns>
    Task<string> GetSystemStatusAsync(string format = "json", CancellationToken cancellationToken = default);
}

/// <summary>
/// Prometheus 导出器接口
/// </summary>
public interface IPrometheusExporter
{
    /// <summary>
    /// 导出 Prometheus 格式的指标
    /// </summary>
    /// <param name="metrics">性能指标</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>Prometheus 格式的指标数据</returns>
    Task<string> ExportPrometheusMetricsAsync(PerformanceMetrics metrics, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取 Prometheus 端点路径
    /// </summary>
    string EndpointPath { get; }
}

/// <summary>
/// 导出结果
/// </summary>
public class ExportResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 导出的数据
    /// </summary>
    public string Data { get; set; } = string.Empty;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 导出时间
    /// </summary>
    public DateTime ExportedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 数据大小（字节）
    /// </summary>
    public long DataSize { get; set; }

    /// <summary>
    /// 导出耗时（毫秒）
    /// </summary>
    public double ExportDurationMs { get; set; }
}

/// <summary>
    /// 导出统计信息
    /// </summary>
public class ExportStatistics
{
    /// <summary>
    /// 总导出次数
    /// </summary>
    public long TotalExports { get; set; }

    /// <summary>
    /// 成功导出次数
    /// </summary>
    public long SuccessfulExports { get; set; }

    /// <summary>
    /// 失败导出次数
    /// </summary>
    public long FailedExports { get; set; }

    /// <summary>
    /// 平均导出耗时（毫秒）
    /// </summary>
    public double AverageExportDurationMs { get; set; }

    /// <summary>
    /// 总导出数据大小（字节）
    /// </summary>
    public long TotalExportedDataSize { get; set; }

    /// <summary>
    /// 最后导出时间
    /// </summary>
    public DateTime? LastExportTime { get; set; }

    /// <summary>
    /// 按格式分组的导出统计
    /// </summary>
    public Dictionary<string, FormatExportStatistics> ExportsByFormat { get; set; } = new();
}

/// <summary>
/// 按格式的导出统计
/// </summary>
public class FormatExportStatistics
{
    /// <summary>
    /// 格式名称
    /// </summary>
    public string Format { get; set; } = string.Empty;

    /// <summary>
    /// 导出次数
    /// </summary>
    public long ExportCount { get; set; }

    /// <summary>
    /// 平均导出耗时（毫秒）
    /// </summary>
    public double AverageExportDurationMs { get; set; }

    /// <summary>
    /// 总导出数据大小（字节）
    /// </summary>
    public long TotalDataSize { get; set; }

    /// <summary>
    /// 最后导出时间
    /// </summary>
    public DateTime? LastExportTime { get; set; }
}
