using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Network;

namespace MqttBroker.Core.QoS;

/// <summary>
/// QoS 管理器接口，负责协调所有 QoS 级别的处理
/// </summary>
public interface IQoSManager
{
    /// <summary>
    /// 处理发布消息的 QoS 流程
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="publishPacket">发布数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>QoS 处理结果</returns>
    Task<QoSProcessingResult> ProcessPublishAsync(
        IClientConnection connection, 
        MqttPublishPacket publishPacket, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 处理 PUBACK 数据包
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="pubackPacket">PUBACK 数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理结果</returns>
    Task<QoSProcessingResult> ProcessPubAckAsync(
        IClientConnection connection, 
        MqttPubAckPacket pubackPacket, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 处理 PUBREC 数据包
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="pubrecPacket">PUBREC 数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理结果</returns>
    Task<QoSProcessingResult> ProcessPubRecAsync(
        IClientConnection connection, 
        MqttPubRecPacket pubrecPacket, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 处理 PUBREL 数据包
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="pubrelPacket">PUBREL 数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理结果</returns>
    Task<QoSProcessingResult> ProcessPubRelAsync(
        IClientConnection connection, 
        MqttPubRelPacket pubrelPacket, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 处理 PUBCOMP 数据包
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="pubcompPacket">PUBCOMP 数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理结果</returns>
    Task<QoSProcessingResult> ProcessPubCompAsync(
        IClientConnection connection, 
        MqttPubCompPacket pubcompPacket, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取客户端的待确认消息统计
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>待确认消息统计</returns>
    Task<PendingMessageStatistics> GetPendingMessageStatisticsAsync(string clientId);

    /// <summary>
    /// 获取全局 QoS 统计信息
    /// </summary>
    /// <returns>全局 QoS 统计信息</returns>
    Task<GlobalQoSStatistics> GetGlobalStatisticsAsync();

    /// <summary>
    /// 清理客户端的所有待确认消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>清理的消息数量</returns>
    Task<int> CleanupClientMessagesAsync(string clientId);

    /// <summary>
    /// 启动 QoS 管理器（开始重传和清理任务）
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止 QoS 管理器
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    Task StopAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// QoS 处理结果
/// </summary>
public class QoSProcessingResult
{
    /// <summary>
    /// 是否处理成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 处理的消息ID
    /// </summary>
    public ushort? MessageId { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string? ClientId { get; set; }

    /// <summary>
    /// QoS 级别
    /// </summary>
    public MqttQoSLevel QoSLevel { get; set; }

    /// <summary>
    /// 处理耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }

    /// <summary>
    /// 额外的处理信息
    /// </summary>
    public Dictionary<string, object>? AdditionalInfo { get; set; }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    public static QoSProcessingResult Success(ushort? messageId = null, string? clientId = null, MqttQoSLevel qosLevel = MqttQoSLevel.AtMostOnce)
    {
        return new QoSProcessingResult
        {
            IsSuccess = true,
            MessageId = messageId,
            ClientId = clientId,
            QoSLevel = qosLevel
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    public static QoSProcessingResult Failure(string errorMessage, ushort? messageId = null, string? clientId = null)
    {
        return new QoSProcessingResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            MessageId = messageId,
            ClientId = clientId
        };
    }
}

/// <summary>
/// 待确认消息统计
/// </summary>
public class PendingMessageStatistics
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// QoS 1 待确认消息数量
    /// </summary>
    public int PendingQoS1Messages { get; set; }

    /// <summary>
    /// QoS 2 待确认消息数量
    /// </summary>
    public int PendingQoS2Messages { get; set; }

    /// <summary>
    /// 总待确认消息数量
    /// </summary>
    public int TotalPendingMessages => PendingQoS1Messages + PendingQoS2Messages;

    /// <summary>
    /// 最早的待确认消息时间
    /// </summary>
    public DateTime? OldestPendingMessageTime { get; set; }

    /// <summary>
    /// 重传次数统计
    /// </summary>
    public Dictionary<ushort, int> RetransmissionCounts { get; set; } = new();
}

/// <summary>
/// 全局 QoS 统计信息
/// </summary>
public class GlobalQoSStatistics
{
    /// <summary>
    /// 总处理的 QoS 1 消息数量
    /// </summary>
    public long TotalQoS1MessagesProcessed { get; set; }

    /// <summary>
    /// 总处理的 QoS 2 消息数量
    /// </summary>
    public long TotalQoS2MessagesProcessed { get; set; }

    /// <summary>
    /// 当前待确认的 QoS 1 消息数量
    /// </summary>
    public int CurrentPendingQoS1Messages { get; set; }

    /// <summary>
    /// 当前待确认的 QoS 2 消息数量
    /// </summary>
    public int CurrentPendingQoS2Messages { get; set; }

    /// <summary>
    /// 总重传次数
    /// </summary>
    public long TotalRetransmissions { get; set; }

    /// <summary>
    /// 总去重次数
    /// </summary>
    public long TotalDuplications { get; set; }

    /// <summary>
    /// 平均处理延迟（毫秒）
    /// </summary>
    public double AverageProcessingLatency { get; set; }

    /// <summary>
    /// 有待确认消息的客户端数量
    /// </summary>
    public int ClientsWithPendingMessages { get; set; }
}
