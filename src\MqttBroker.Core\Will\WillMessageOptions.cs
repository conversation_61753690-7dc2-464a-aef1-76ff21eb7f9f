using MqttBroker.Core.Protocol;

namespace MqttBroker.Core.Will;

/// <summary>
/// 遗嘱消息配置选项
/// </summary>
public class WillMessageOptions
{
    /// <summary>
    /// 是否启用遗嘱消息处理
    /// </summary>
    public bool EnableWillMessageProcessing { get; set; } = true;

    /// <summary>
    /// 每个客户端最大遗嘱消息数量
    /// </summary>
    public int MaxWillMessagesPerClient { get; set; } = 1;

    /// <summary>
    /// 遗嘱消息过期时间（小时）
    /// 设置为0表示永不过期
    /// </summary>
    public int WillMessageExpirationHours { get; set; } = 24;

    /// <summary>
    /// 最大主题长度
    /// </summary>
    public int MaxTopicLength { get; set; } = 1024;

    /// <summary>
    /// 最大载荷大小（字节）
    /// </summary>
    public int MaxPayloadSize { get; set; } = 256 * 1024; // 256KB

    /// <summary>
    /// 允许的QoS级别
    /// 如果为null，则允许所有QoS级别
    /// </summary>
    public HashSet<MqttQoSLevel>? AllowedQoSLevels { get; set; }

    /// <summary>
    /// 禁止的主题模式
    /// </summary>
    public HashSet<string>? ForbiddenTopicPatterns { get; set; }

    /// <summary>
    /// 默认协议版本
    /// </summary>
    public MqttProtocolVersion DefaultProtocolVersion { get; set; } = MqttProtocolVersion.Version311;

    /// <summary>
    /// 是否启用自动清理过期遗嘱消息
    /// </summary>
    public bool EnableAutomaticCleanup { get; set; } = true;

    /// <summary>
    /// 自动清理间隔（分钟）
    /// </summary>
    public int CleanupIntervalMinutes { get; set; } = 60;

    /// <summary>
    /// 最大并发遗嘱消息处理数
    /// </summary>
    public int MaxConcurrentWillMessageProcessing { get; set; } = 1000;

    /// <summary>
    /// 遗嘱消息处理超时时间（毫秒）
    /// </summary>
    public int WillMessageProcessingTimeoutMs { get; set; } = 5000;

    /// <summary>
    /// 是否启用遗嘱消息统计
    /// </summary>
    public bool EnableStatistics { get; set; } = true;

    /// <summary>
    /// 是否启用性能监控
    /// </summary>
    public bool EnablePerformanceMonitoring { get; set; } = true;

    /// <summary>
    /// 是否在客户端重连时自动清除遗嘱消息
    /// </summary>
    public bool AutoClearOnReconnect { get; set; } = true;

    /// <summary>
    /// 是否在服务器关闭时触发所有遗嘱消息
    /// </summary>
    public bool TriggerAllOnServerShutdown { get; set; } = false;

    /// <summary>
    /// 遗嘱消息触发延迟（毫秒）
    /// 用于避免网络抖动导致的误触发
    /// </summary>
    public int TriggerDelayMs { get; set; } = 1000;

    /// <summary>
    /// 是否启用遗嘱消息持久化
    /// </summary>
    public bool EnablePersistence { get; set; } = true;

    /// <summary>
    /// 持久化存储类型
    /// </summary>
    public WillMessageStorageType StorageType { get; set; } = WillMessageStorageType.InMemory;

    /// <summary>
    /// 数据库连接字符串（当使用数据库存储时）
    /// </summary>
    public string? DatabaseConnectionString { get; set; }

    /// <summary>
    /// 是否启用批量操作优化
    /// </summary>
    public bool EnableBatchOptimization { get; set; } = true;

    /// <summary>
    /// 批量操作大小
    /// </summary>
    public int BatchSize { get; set; } = 100;

    /// <summary>
    /// 是否启用压缩存储
    /// </summary>
    public bool EnableCompression { get; set; } = false;

    /// <summary>
    /// 压缩阈值（字节）
    /// 只有超过此大小的载荷才会被压缩
    /// </summary>
    public int CompressionThreshold { get; set; } = 1024;

    /// <summary>
    /// 验证配置选项
    /// </summary>
    /// <returns>验证结果</returns>
    public WillMessageOptionsValidationResult Validate()
    {
        var errors = new List<string>();

        if (MaxWillMessagesPerClient <= 0)
            errors.Add("MaxWillMessagesPerClient must be greater than 0");

        if (WillMessageExpirationHours < 0)
            errors.Add("WillMessageExpirationHours cannot be negative");

        if (MaxTopicLength <= 0)
            errors.Add("MaxTopicLength must be greater than 0");

        if (MaxPayloadSize <= 0)
            errors.Add("MaxPayloadSize must be greater than 0");

        if (CleanupIntervalMinutes <= 0)
            errors.Add("CleanupIntervalMinutes must be greater than 0");

        if (MaxConcurrentWillMessageProcessing <= 0)
            errors.Add("MaxConcurrentWillMessageProcessing must be greater than 0");

        if (WillMessageProcessingTimeoutMs <= 0)
            errors.Add("WillMessageProcessingTimeoutMs must be greater than 0");

        if (TriggerDelayMs < 0)
            errors.Add("TriggerDelayMs cannot be negative");

        if (BatchSize <= 0)
            errors.Add("BatchSize must be greater than 0");

        if (CompressionThreshold < 0)
            errors.Add("CompressionThreshold cannot be negative");

        if (StorageType == WillMessageStorageType.Database && string.IsNullOrEmpty(DatabaseConnectionString))
            errors.Add("DatabaseConnectionString is required when using database storage");

        return errors.Count == 0 
            ? WillMessageOptionsValidationResult.Valid() 
            : WillMessageOptionsValidationResult.Invalid(errors);
    }

    /// <summary>
    /// 创建默认配置
    /// </summary>
    /// <returns>默认配置</returns>
    public static WillMessageOptions CreateDefault()
    {
        return new WillMessageOptions();
    }

    /// <summary>
    /// 创建高性能配置
    /// </summary>
    /// <returns>高性能配置</returns>
    public static WillMessageOptions CreateHighPerformance()
    {
        return new WillMessageOptions
        {
            MaxConcurrentWillMessageProcessing = 10000,
            WillMessageProcessingTimeoutMs = 1000,
            EnableBatchOptimization = true,
            BatchSize = 500,
            CleanupIntervalMinutes = 30,
            TriggerDelayMs = 500,
            EnableCompression = true,
            CompressionThreshold = 512
        };
    }

    /// <summary>
    /// 创建低资源配置
    /// </summary>
    /// <returns>低资源配置</returns>
    public static WillMessageOptions CreateLowResource()
    {
        return new WillMessageOptions
        {
            MaxConcurrentWillMessageProcessing = 100,
            WillMessageProcessingTimeoutMs = 10000,
            MaxPayloadSize = 64 * 1024, // 64KB
            EnableBatchOptimization = false,
            BatchSize = 10,
            CleanupIntervalMinutes = 120,
            EnablePerformanceMonitoring = false,
            EnableCompression = false
        };
    }
}

/// <summary>
/// 遗嘱消息存储类型
/// </summary>
public enum WillMessageStorageType
{
    /// <summary>
    /// 内存存储
    /// </summary>
    InMemory,

    /// <summary>
    /// 数据库存储
    /// </summary>
    Database,

    /// <summary>
    /// 文件存储
    /// </summary>
    File,

    /// <summary>
    /// Redis存储
    /// </summary>
    Redis
}

/// <summary>
/// 遗嘱消息配置选项验证结果
/// </summary>
public class WillMessageOptionsValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; private set; }

    /// <summary>
    /// 错误消息列表
    /// </summary>
    public IList<string> Errors { get; private set; }

    private WillMessageOptionsValidationResult(bool isValid, IList<string> errors)
    {
        IsValid = isValid;
        Errors = errors;
    }

    /// <summary>
    /// 创建有效结果
    /// </summary>
    public static WillMessageOptionsValidationResult Valid()
    {
        return new WillMessageOptionsValidationResult(true, new List<string>());
    }

    /// <summary>
    /// 创建无效结果
    /// </summary>
    public static WillMessageOptionsValidationResult Invalid(IList<string> errors)
    {
        return new WillMessageOptionsValidationResult(false, errors);
    }

    /// <summary>
    /// 获取错误消息字符串
    /// </summary>
    public string GetErrorMessage()
    {
        return string.Join("; ", Errors);
    }
}
