using System.Collections.Concurrent;
using MqttBroker.Core.Protocol;

namespace MqttBroker.Core.Topic;

/// <summary>
/// 高效的主题树数据结构，支持快速主题匹配和订阅者查找
/// </summary>
public class TopicTree
{
    private readonly TopicNode _root;
    private readonly object _lock = new();

    /// <summary>
    /// 初始化主题树
    /// </summary>
    public TopicTree()
    {
        _root = new TopicNode(string.Empty);
    }

    /// <summary>
    /// 添加订阅者到主题树
    /// </summary>
    /// <param name="topicFilter">主题过滤器</param>
    /// <param name="subscriber">订阅者</param>
    public void AddSubscriber(string topicFilter, TopicSubscriber subscriber)
    {
        if (string.IsNullOrEmpty(topicFilter) || subscriber == null)
            return;

        lock (_lock)
        {
            var segments = SplitTopic(topicFilter);
            var node = GetOrCreateNode(segments);
            node.AddSubscriber(subscriber);
        }
    }

    /// <summary>
    /// 从主题树移除订阅者
    /// </summary>
    /// <param name="topicFilter">主题过滤器</param>
    /// <param name="clientId">客户端ID</param>
    /// <returns>是否成功移除</returns>
    public bool RemoveSubscriber(string topicFilter, string clientId)
    {
        if (string.IsNullOrEmpty(topicFilter) || string.IsNullOrEmpty(clientId))
            return false;

        lock (_lock)
        {
            var segments = SplitTopic(topicFilter);
            var node = FindNode(segments);
            if (node == null)
                return false;

            var removed = node.RemoveSubscriber(clientId);
            
            // 如果节点没有订阅者且没有子节点，则清理空节点
            if (removed)
            {
                CleanupEmptyNodes(segments);
            }

            return removed;
        }
    }

    /// <summary>
    /// 获取匹配指定主题的所有订阅者
    /// </summary>
    /// <param name="topicName">主题名称</param>
    /// <returns>匹配的订阅者列表</returns>
    public IList<TopicSubscriber> GetMatchingSubscribers(string topicName)
    {
        if (string.IsNullOrEmpty(topicName))
            return new List<TopicSubscriber>();

        lock (_lock)
        {
            var segments = SplitTopic(topicName);
            var subscribers = new List<TopicSubscriber>();
            
            // 递归查找匹配的订阅者
            FindMatchingSubscribers(_root, segments, 0, subscribers);
            
            return subscribers;
        }
    }

    /// <summary>
    /// 获取客户端的所有订阅
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>客户端订阅列表</returns>
    public IList<TopicSubscriber> GetClientSubscriptions(string clientId)
    {
        if (string.IsNullOrEmpty(clientId))
            return new List<TopicSubscriber>();

        lock (_lock)
        {
            var subscriptions = new List<TopicSubscriber>();
            CollectClientSubscriptions(_root, clientId, string.Empty, subscriptions);
            return subscriptions;
        }
    }

    /// <summary>
    /// 移除客户端的所有订阅
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>移除的订阅数量</returns>
    public int RemoveClientSubscriptions(string clientId)
    {
        if (string.IsNullOrEmpty(clientId))
            return 0;

        lock (_lock)
        {
            var removedCount = 0;
            var nodesToCleanup = new List<string[]>();
            
            RemoveClientSubscriptionsRecursive(_root, clientId, new List<string>(), ref removedCount, nodesToCleanup);
            
            // 清理空节点
            foreach (var segments in nodesToCleanup)
            {
                CleanupEmptyNodes(segments);
            }
            
            return removedCount;
        }
    }

    /// <summary>
    /// 获取主题树统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    public TopicTreeStatistics GetStatistics()
    {
        lock (_lock)
        {
            var stats = new TopicTreeStatistics();
            CollectStatistics(_root, stats);
            return stats;
        }
    }

    #region 私有方法

    /// <summary>
    /// 分割主题为段
    /// </summary>
    /// <param name="topic">主题</param>
    /// <returns>主题段数组</returns>
    private static string[] SplitTopic(string topic)
    {
        return topic.Split(MqttProtocolConstants.TopicWildcards.Separator, StringSplitOptions.None);
    }

    /// <summary>
    /// 获取或创建节点
    /// </summary>
    /// <param name="segments">主题段</param>
    /// <returns>节点</returns>
    private TopicNode GetOrCreateNode(string[] segments)
    {
        var current = _root;
        
        foreach (var segment in segments)
        {
            current = current.GetOrCreateChild(segment);
        }
        
        return current;
    }

    /// <summary>
    /// 查找节点
    /// </summary>
    /// <param name="segments">主题段</param>
    /// <returns>节点，如果不存在则返回null</returns>
    private TopicNode? FindNode(string[] segments)
    {
        var current = _root;
        
        foreach (var segment in segments)
        {
            if (!current.Children.TryGetValue(segment, out current))
                return null;
        }
        
        return current;
    }

    /// <summary>
    /// 递归查找匹配的订阅者
    /// </summary>
    /// <param name="node">当前节点</param>
    /// <param name="segments">主题段</param>
    /// <param name="segmentIndex">当前段索引</param>
    /// <param name="subscribers">订阅者列表</param>
    private void FindMatchingSubscribers(TopicNode node, string[] segments, int segmentIndex, List<TopicSubscriber> subscribers)
    {
        // 如果已经处理完所有段
        if (segmentIndex >= segments.Length)
        {
            // 添加当前节点的订阅者
            subscribers.AddRange(node.Subscribers.Values);
            return;
        }

        var currentSegment = segments[segmentIndex];

        // 检查精确匹配
        if (node.Children.TryGetValue(currentSegment, out var exactMatch))
        {
            FindMatchingSubscribers(exactMatch, segments, segmentIndex + 1, subscribers);
        }

        // 检查单级通配符匹配
        if (node.Children.TryGetValue(MqttProtocolConstants.TopicWildcards.SingleLevel.ToString(), out var singleWildcard))
        {
            FindMatchingSubscribers(singleWildcard, segments, segmentIndex + 1, subscribers);
        }

        // 检查多级通配符匹配
        if (node.Children.TryGetValue(MqttProtocolConstants.TopicWildcards.MultiLevel.ToString(), out var multiWildcard))
        {
            // 多级通配符匹配剩余的所有段
            subscribers.AddRange(multiWildcard.Subscribers.Values);
        }
    }

    /// <summary>
    /// 收集客户端订阅
    /// </summary>
    /// <param name="node">当前节点</param>
    /// <param name="clientId">客户端ID</param>
    /// <param name="currentPath">当前路径</param>
    /// <param name="subscriptions">订阅列表</param>
    private void CollectClientSubscriptions(TopicNode node, string clientId, string currentPath, List<TopicSubscriber> subscriptions)
    {
        // 检查当前节点是否有该客户端的订阅
        if (node.Subscribers.TryGetValue(clientId, out var subscriber))
        {
            subscriptions.Add(subscriber);
        }

        // 递归检查子节点
        foreach (var child in node.Children)
        {
            var childPath = string.IsNullOrEmpty(currentPath) ? child.Key : $"{currentPath}/{child.Key}";
            CollectClientSubscriptions(child.Value, clientId, childPath, subscriptions);
        }
    }

    /// <summary>
    /// 递归移除客户端订阅
    /// </summary>
    /// <param name="node">当前节点</param>
    /// <param name="clientId">客户端ID</param>
    /// <param name="currentPath">当前路径</param>
    /// <param name="removedCount">移除计数</param>
    /// <param name="nodesToCleanup">需要清理的节点</param>
    private void RemoveClientSubscriptionsRecursive(TopicNode node, string clientId, List<string> currentPath, 
        ref int removedCount, List<string[]> nodesToCleanup)
    {
        // 移除当前节点的客户端订阅
        if (node.RemoveSubscriber(clientId))
        {
            removedCount++;
            if (node.IsEmpty)
            {
                nodesToCleanup.Add(currentPath.ToArray());
            }
        }

        // 递归处理子节点
        foreach (var child in node.Children.ToList())
        {
            currentPath.Add(child.Key);
            RemoveClientSubscriptionsRecursive(child.Value, clientId, currentPath, ref removedCount, nodesToCleanup);
            currentPath.RemoveAt(currentPath.Count - 1);
        }
    }

    /// <summary>
    /// 清理空节点
    /// </summary>
    /// <param name="segments">主题段</param>
    private void CleanupEmptyNodes(string[] segments)
    {
        if (segments.Length == 0)
            return;

        var nodePath = new List<TopicNode> { _root };
        var current = _root;

        // 构建节点路径
        foreach (var segment in segments)
        {
            if (current.Children.TryGetValue(segment, out current))
            {
                nodePath.Add(current);
            }
            else
            {
                return; // 节点不存在
            }
        }

        // 从叶子节点向上清理空节点
        for (int i = nodePath.Count - 1; i > 0; i--)
        {
            var node = nodePath[i];
            var parent = nodePath[i - 1];
            var segment = segments[i - 1];

            if (node.IsEmpty)
            {
                parent.Children.TryRemove(segment, out _);
            }
            else
            {
                break; // 如果节点不为空，停止清理
            }
        }
    }

    /// <summary>
    /// 收集统计信息
    /// </summary>
    /// <param name="node">当前节点</param>
    /// <param name="stats">统计信息</param>
    private void CollectStatistics(TopicNode node, TopicTreeStatistics stats)
    {
        stats.TotalNodes++;
        stats.TotalSubscribers += node.Subscribers.Count;

        if (node.Subscribers.Count > 0)
        {
            stats.NodesWithSubscribers++;
        }

        foreach (var child in node.Children.Values)
        {
            CollectStatistics(child, stats);
        }
    }

    #endregion
}

/// <summary>
/// 主题树节点
/// </summary>
internal class TopicNode
{
    /// <summary>
    /// 节点段名称
    /// </summary>
    public string Segment { get; }

    /// <summary>
    /// 子节点
    /// </summary>
    public ConcurrentDictionary<string, TopicNode> Children { get; }

    /// <summary>
    /// 订阅者（按客户端ID索引）
    /// </summary>
    public ConcurrentDictionary<string, TopicSubscriber> Subscribers { get; }

    /// <summary>
    /// 节点是否为空（没有订阅者且没有子节点）
    /// </summary>
    public bool IsEmpty => Subscribers.IsEmpty && Children.IsEmpty;

    /// <summary>
    /// 初始化主题节点
    /// </summary>
    /// <param name="segment">节点段名称</param>
    public TopicNode(string segment)
    {
        Segment = segment;
        Children = new ConcurrentDictionary<string, TopicNode>();
        Subscribers = new ConcurrentDictionary<string, TopicSubscriber>();
    }

    /// <summary>
    /// 获取或创建子节点
    /// </summary>
    /// <param name="segment">段名称</param>
    /// <returns>子节点</returns>
    public TopicNode GetOrCreateChild(string segment)
    {
        return Children.GetOrAdd(segment, s => new TopicNode(s));
    }

    /// <summary>
    /// 添加订阅者
    /// </summary>
    /// <param name="subscriber">订阅者</param>
    public void AddSubscriber(TopicSubscriber subscriber)
    {
        Subscribers.AddOrUpdate(subscriber.ClientId, subscriber, (key, existing) => subscriber);
    }

    /// <summary>
    /// 移除订阅者
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>是否成功移除</returns>
    public bool RemoveSubscriber(string clientId)
    {
        return Subscribers.TryRemove(clientId, out _);
    }
}

/// <summary>
/// 主题树统计信息
/// </summary>
public class TopicTreeStatistics
{
    /// <summary>
    /// 总节点数
    /// </summary>
    public long TotalNodes { get; set; }

    /// <summary>
    /// 有订阅者的节点数
    /// </summary>
    public long NodesWithSubscribers { get; set; }

    /// <summary>
    /// 总订阅者数
    /// </summary>
    public long TotalSubscribers { get; set; }

    /// <summary>
    /// 平均每个节点的订阅者数
    /// </summary>
    public double AverageSubscribersPerNode => NodesWithSubscribers > 0 ? (double)TotalSubscribers / NodesWithSubscribers : 0;
}
